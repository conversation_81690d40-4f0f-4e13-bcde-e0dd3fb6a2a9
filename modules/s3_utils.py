import filecmp
import os
import shutil
import queue
import boto3
from botocore.exceptions import NoCredentialsError


class S3Sync:
    def __init__(self, bucket_name, s3_prefix="", local_directory=""):
        self.bucket_name = bucket_name
        self.s3_prefix = s3_prefix
        self.local_directory = local_directory
        self.s3 = boto3.client('s3')

        self.s3_copy_dir = f"/tmp/{self.local_directory}"

    def download_all_files_from_s3_folder(self):
        """
        Download all files from the specified S3 prefix (folder) to the local directory.
        """
        if os.path.exists(self.s3_copy_dir):
            shutil.rmtree(self.s3_copy_dir)

        paginator = self.s3.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=self.bucket_name, Prefix=self.s3_prefix):
            for obj in page.get('Contents', []):
                file_key = obj['Key']
                local_path = os.path.join(self.s3_copy_dir, os.path.relpath(file_key, self.s3_prefix))
                os.makedirs(os.path.dirname(local_path), exist_ok=True)

                print(f"Downloading {file_key} to {local_path}")
                try:
                    self.s3.download_file(self.bucket_name, file_key, local_path)
                except NoCredentialsError:
                    print("AWS credentials not available.")
                    return

        if not os.path.exists(self.s3_copy_dir):
            os.makedirs(self.s3_copy_dir)

    def folders_to_be_synced(self):
        changes = list()
        result = set()

        dirs_to_proceed = queue.Queue()
        dirs_to_proceed.put("")

        while not dirs_to_proceed.empty():
            dir = dirs_to_proceed.get()

            dircmp = filecmp.dircmp(f"{self.local_directory}/{dir}", f"{self.s3_copy_dir}/{dir}")
            for diff in dircmp.diff_files:
                changes.append(f"{dir}/{diff}")

            for diff in dircmp.left_only:
                changes.append(f"{dir}/{diff}")

            for diff in dircmp.right_only:
                changes.append(f"{dir}/{diff}")

            for sub_dir in dircmp.subdirs:
                dirs_to_proceed.put(f"{dir}/{sub_dir}")

        for change in changes:
            folder_to_sync = change.split("/")[1]
            if folder_to_sync:
                result.add(folder_to_sync)

        return result

    def sync_local_folder_to_s3(self, folder_name):
        dirs_to_proceed = queue.Queue()
        dirs_to_proceed.put(folder_name)

        while not dirs_to_proceed.empty():
            dir = dirs_to_proceed.get()

            if not os.path.exists(f"{self.local_directory}/{dir}"):
                # delete the folder from S3 recursively
                paginator = self.s3.get_paginator('list_objects_v2')
                for page in paginator.paginate(Bucket=self.bucket_name, Prefix=f"{self.s3_prefix}/{dir}"):
                    for obj in page.get('Contents', []):
                        s3_key = obj['Key']
                        print(f"0 Deleting s3://{self.bucket_name}/{s3_key}")
                        try:
                            self.s3.delete_object(Bucket=self.bucket_name, Key=s3_key)
                        except NoCredentialsError:
                            print("AWS credentials not available.")
                            return
                continue

            if not os.path.exists(f"{self.s3_copy_dir}/{dir}"):
                # upload dir files to S3 recursively
                for root, dirs, files in os.walk(f"{self.local_directory}/{dir}"):
                    for file in files:
                        local_path = os.path.join(root, file)
                        s3_key = f"{self.s3_prefix}/{local_path.split(f'{self.local_directory}/')[1]}"
                        print(f"1 Uploading {local_path} to s3://{self.bucket_name}/{s3_key}")
                        try:
                            self.s3.upload_file(local_path, self.bucket_name, s3_key)
                        except NoCredentialsError:
                            print("AWS credentials not available.")
                            return
                continue

            dircmp = filecmp.dircmp(f"{self.local_directory}/{dir}", f"{self.s3_copy_dir}/{dir}")
            for diff in dircmp.diff_files:
                local_path = f"{self.local_directory}/{dir}/{diff}"
                s3_key = f"{self.s3_prefix}/{dir}/{diff}"
                print(f"2 Uploading {local_path} to s3://{self.bucket_name}/{s3_key}")
                try:
                    self.s3.upload_file(local_path, self.bucket_name, s3_key)
                except NoCredentialsError:
                    print("AWS credentials not available.")
                    return

            for diff in dircmp.left_only:
                # check file or dir and upload to S3
                local_path = f"{self.local_directory}/{dir}/{diff}"
                if os.path.isfile(local_path):
                    s3_key = f"{self.s3_prefix}/{dir}/{diff}"
                    print(f"3 Uploading {local_path} to s3://{self.bucket_name}/{s3_key}")
                    try:
                        self.s3.upload_file(local_path, self.bucket_name, s3_key)
                    except NoCredentialsError:
                        print("AWS credentials not available.")
                        return
                else:
                    dirs_to_proceed.put(f"{dir}/{diff}")

            for diff in dircmp.right_only:
                # check file or dir and delete to S3
                local_path = f"{self.s3_copy_dir}/{dir}/{diff}"
                if os.path.isfile(local_path):
                    s3_key = f"{self.s3_prefix}/{dir}/{diff}"
                    print(f"4 Deleting s3://{self.bucket_name}/{s3_key}")
                    try:
                        self.s3.delete_object(Bucket=self.bucket_name, Key=s3_key)
                    except NoCredentialsError:
                        print("AWS credentials not available.")
                        return
                else:
                    dirs_to_proceed.put(f"{dir}/{diff}")

            for sub_dir in dircmp.subdirs:
                dirs_to_proceed.put(f"{dir}/{sub_dir}")
