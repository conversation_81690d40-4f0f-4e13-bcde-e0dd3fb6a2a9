---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: container-ephemeral-storage
  labels:
    scope: global
spec:
  name: "Container Ephemeral Storage Metrics"
  json: |
    {
        "version": 18,
        "variables": [
            {
            "key": "Cluster",
            "visible": true,
            "type": "query",
            "version": 1,
            "editable": true,
            "input": "fetch dt.entity.kubernetes_cluster\n| fields entity.name\n| sort entity.name asc",
            "multiple": false
            },
            {
            "key": "k8s_node_name",
            "visible": false,
            "type": "query",
            "version": 1,
            "editable": true,
            "input": "fetch dt.entity.kubernetes_node\n| fields id, node.name = entity.name\n| filter in(id, classicEntitySelector(concat(\"type(KUBERNETES_NODE),toRelationship.isClusterOfNode(type(KUBERNETES_CLUSTER),entityName.equals(\",$Cluster,\"))\")))\n| summarize collectDistinct(node.name)\n| fieldsAdd `collectDistinct(node.name)` = arraySort(`collectDistinct(node.name)`, direction: \"ascending\")",
            "multiple": false
            },
            {
            "key": "Namespace",
            "visible": true,
            "type": "query",
            "version": 1,
            "editable": true,
            "input": "fetch dt.entity.cloud_application_namespace\n| fields id, name = entity.name\n| filter in(id, classicEntitySelector(concat(\"type(CLOUD_APPLICATION_NAMESPACE),toRelationship.isClusterOfNamespace(type(KUBERNETES_CLUSTER),entityName.equals(\", $Cluster, \"))\")))\n| fields name\n| sort name asc",
            "multiple": false
            }
        ],
        "tiles": {
            "3": {
            "title": "Ephemeral Storage Consumed By Pod",
            "type": "data",
            "query": "timeseries { max(kube_summary_pod_ephemeral_storage_used_bytes), value.A = max(kube_summary_pod_ephemeral_storage_used_bytes, scalar: true),max(kube_summary_pod_ephemeral_storage_capacity_bytes), value.B = max(kube_summary_pod_ephemeral_storage_capacity_bytes, scalar: true) }, union: TRUE, by: { namespace,pod }, filter: { matchesValue( namespace ,$Namespace) }\n| fieldsAdd result = `max(kube_summary_pod_ephemeral_storage_used_bytes)`[]/`max(kube_summary_pod_ephemeral_storage_capacity_bytes)`[]",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                "gapPolicy": "gap",
                "circleChartSettings": {
                    "groupingThresholdType": "relative",
                    "groupingThresholdValue": 0,
                    "valueType": "relative"
                },
                "categoryOverrides": {},
                "curve": "linear",
                "pointsDisplay": "auto",
                "categoricalBarChartSettings": {
                    "layout": "horizontal",
                    "categoryAxisTickLayout": "horizontal",
                    "scale": "absolute",
                    "groupMode": "stacked",
                    "colorPaletteMode": "multi-color",
                    "valueAxisScale": "linear"
                },
                "colorPalette": "categorical",
                "valueRepresentation": "absolute",
                "truncationMode": "middle",
                "bandChartSettings": {
                    "lower": "max(kube_summary_pod_ephemeral_storage_used_bytes)",
                    "upper": "max(kube_summary_pod_ephemeral_storage_capacity_bytes)"
                },
                "xAxisScaling": "analyzedTimeframe",
                "xAxisLabel": "timeframe",
                "xAxisIsLabelVisible": false,
                "hiddenLegendFields": [
                    "k8s.node",
                    "interval",
                    "A",
                    "sum(kube_summary_pod_ephemeral_storage_used_bytes)"
                ],
                "fieldMapping": {
                    "timestamp": "timeframe",
                    "leftAxisValues": [
                    "result"
                    ]
                },
                "leftYAxisSettings": {}
                },
                "singleValue": {
                "showLabel": true,
                "label": "",
                "prefixIcon": "AnalyticsIcon",
                "isIconVisible": false,
                "autoscale": true,
                "alignment": "center",
                "colorThresholdTarget": "value"
                },
                "table": {
                "rowDensity": "condensed",
                "enableSparklines": false,
                "hiddenColumns": [],
                "linewrapEnabled": false,
                "lineWrapIds": [],
                "monospacedFontEnabled": false,
                "monospacedFontColumns": [],
                "columnWidths": {},
                "columnTypeOverrides": [
                    {
                    "fields": [
                        "max(kube_summary_pod_ephemeral_storage_used_bytes)",
                        "max(kube_summary_pod_ephemeral_storage_capacity_bytes)",
                        "result"
                    ],
                    "value": "sparkline",
                    "id": 1746802257611
                    }
                ]
                },
                "honeycomb": {
                "shape": "hexagon",
                "legend": {
                    "hidden": false,
                    "position": "auto",
                    "ratio": "auto"
                },
                "dataMappings": {},
                "displayedFields": [],
                "truncationMode": "middle",
                "colorMode": "color-palette",
                "colorPalette": "categorical"
                },
                "histogram": {
                "legend": {
                    "position": "auto"
                },
                "yAxis": {
                    "label": "Frequency",
                    "isLabelVisible": true,
                    "scale": "linear"
                },
                "colorPalette": "categorical",
                "dataMappings": [],
                "variant": "single",
                "truncationMode": "middle"
                },
                "valueBoundaries": {
                "min": "auto",
                "max": "auto"
                },
                "autoSelectVisualization": false
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                "isAvailable": true
                }
            }
            },
            "5": {
            "title": "Ephemeral Storage Consumed By Logs",
            "type": "data",
            "query": "timeseries { max(kube_summary_container_logs_used_bytes), value.A = max(kube_summary_container_logs_used_bytes, scalar: true),max(kube_summary_container_logs_capacity_bytes), value.B = max(kube_summary_container_logs_capacity_bytes, scalar: true) }, union: TRUE, by: { namespace,pod }, filter: { matchesValue( namespace ,$Namespace) }\n| fieldsAdd result = `max(kube_summary_container_logs_used_bytes)`[]/`max(kube_summary_container_logs_capacity_bytes)`[]\n",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                "gapPolicy": "gap",
                "circleChartSettings": {
                    "groupingThresholdType": "relative",
                    "groupingThresholdValue": 0,
                    "valueType": "relative"
                },
                "categoryOverrides": {},
                "curve": "linear",
                "pointsDisplay": "auto",
                "categoricalBarChartSettings": {
                    "layout": "horizontal",
                    "categoryAxisTickLayout": "horizontal",
                    "scale": "absolute",
                    "groupMode": "stacked",
                    "colorPaletteMode": "multi-color",
                    "valueAxisScale": "linear"
                },
                "colorPalette": "categorical",
                "valueRepresentation": "absolute",
                "truncationMode": "middle",
                "bandChartSettings": {
                    "lower": "max(kube_summary_container_logs_used_bytes)",
                    "upper": "max(kube_summary_container_logs_capacity_bytes)"
                },
                "xAxisScaling": "analyzedTimeframe",
                "xAxisLabel": "timeframe",
                "xAxisIsLabelVisible": false,
                "hiddenLegendFields": [
                    "k8s.node",
                    "interval",
                    "A",
                    "sum(kube_summary_pod_ephemeral_storage_used_bytes)"
                ],
                "fieldMapping": {
                    "timestamp": "timeframe",
                    "leftAxisValues": [
                    "result"
                    ]
                },
                "leftYAxisSettings": {}
                },
                "singleValue": {
                "showLabel": true,
                "label": "",
                "prefixIcon": "AnalyticsIcon",
                "isIconVisible": false,
                "autoscale": true,
                "alignment": "center",
                "colorThresholdTarget": "value"
                },
                "table": {
                "rowDensity": "condensed",
                "enableSparklines": false,
                "hiddenColumns": [],
                "linewrapEnabled": false,
                "lineWrapIds": [],
                "monospacedFontEnabled": false,
                "monospacedFontColumns": [],
                "columnWidths": {},
                "columnTypeOverrides": [
                    {
                    "fields": [
                        "result"
                    ],
                    "value": "sparkline",
                    "id": 1746789042322
                    }
                ]
                },
                "honeycomb": {
                "shape": "hexagon",
                "legend": {
                    "hidden": false,
                    "position": "auto",
                    "ratio": "auto"
                },
                "dataMappings": {},
                "displayedFields": [],
                "truncationMode": "middle",
                "colorMode": "color-palette",
                "colorPalette": "categorical"
                },
                "histogram": {
                "legend": {
                    "position": "auto"
                },
                "yAxis": {
                    "label": "Frequency",
                    "isLabelVisible": true,
                    "scale": "linear"
                },
                "colorPalette": "categorical",
                "dataMappings": [],
                "variant": "single",
                "truncationMode": "middle"
                },
                "valueBoundaries": {
                "min": "auto",
                "max": "auto"
                },
                "autoSelectVisualization": false
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                "isAvailable": true
                }
            }
            },
            "8": {
            "title": "Total Ephemeral Storage Used",
            "type": "data",
            "query": "timeseries { avg(kube_summary_pod_ephemeral_storage_used_bytes), value.A = avg(kube_summary_pod_ephemeral_storage_used_bytes, scalar: true), avg(kube_summary_container_logs_used_bytes), value.B = avg(kube_summary_container_logs_used_bytes, scalar: true), avg(kube_summary_pod_ephemeral_storage_capacity_bytes), value.C = avg(kube_summary_pod_ephemeral_storage_capacity_bytes, scalar: true) }, union: TRUE, by: { pod }, filter: { matchesValue(namespace, $Namespace) }\n| fieldsAdd D = (`avg(kube_summary_pod_ephemeral_storage_used_bytes)`[]+`avg(kube_summary_container_logs_used_bytes)`[])/`avg(kube_summary_pod_ephemeral_storage_capacity_bytes)`[]",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                "gapPolicy": "gap",
                "circleChartSettings": {
                    "groupingThresholdType": "relative",
                    "groupingThresholdValue": 0,
                    "valueType": "relative"
                },
                "categoryOverrides": {},
                "curve": "linear",
                "pointsDisplay": "auto",
                "categoricalBarChartSettings": {
                    "layout": "horizontal",
                    "categoryAxisTickLayout": "horizontal",
                    "scale": "absolute",
                    "groupMode": "stacked",
                    "colorPaletteMode": "multi-color",
                    "valueAxisScale": "linear"
                },
                "colorPalette": "categorical",
                "valueRepresentation": "absolute",
                "truncationMode": "middle",
                "bandChartSettings": {
                    "lower": "avg(kube_summary_pod_ephemeral_storage_used_bytes)",
                    "upper": "avg(kube_summary_container_logs_used_bytes)"
                },
                "fieldMapping": {
                    "timestamp": "timeframe",
                    "leftAxisValues": [
                    "D"
                    ]
                },
                "xAxisScaling": "analyzedTimeframe",
                "xAxisLabel": "timeframe",
                "xAxisIsLabelVisible": false,
                "hiddenLegendFields": [
                    "interval",
                    "A",
                    "B",
                    "C"
                ],
                "leftYAxisSettings": {}
                },
                "singleValue": {
                "showLabel": true,
                "label": "",
                "prefixIcon": "AnalyticsIcon",
                "isIconVisible": false,
                "autoscale": true,
                "alignment": "center",
                "colorThresholdTarget": "value"
                },
                "table": {
                "rowDensity": "condensed",
                "enableSparklines": false,
                "hiddenColumns": [],
                "linewrapEnabled": false,
                "lineWrapIds": [],
                "monospacedFontEnabled": false,
                "monospacedFontColumns": [],
                "columnWidths": {},
                "columnTypeOverrides": []
                },
                "honeycomb": {
                "shape": "hexagon",
                "legend": {
                    "hidden": false,
                    "position": "auto",
                    "ratio": "auto"
                },
                "dataMappings": {},
                "displayedFields": [],
                "truncationMode": "middle",
                "colorMode": "color-palette",
                "colorPalette": "categorical"
                },
                "histogram": {
                "legend": {
                    "position": "auto"
                },
                "yAxis": {
                    "label": "Frequency",
                    "isLabelVisible": true,
                    "scale": "linear"
                },
                "colorPalette": "categorical",
                "dataMappings": [],
                "variant": "single",
                "truncationMode": "middle"
                },
                "valueBoundaries": {
                "min": "auto",
                "max": "auto"
                },
                "autoSelectVisualization": true
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                "isAvailable": true
                }
            }
            },
            "10": {
            "type": "data",
            "title": "Ephemeral Storage Usage (Bytes)",
            "query": "timeseries { avg(kube_summary_pod_ephemeral_storage_used_bytes), value.A = avg(kube_summary_pod_ephemeral_storage_used_bytes, scalar: true), avg(kube_summary_pod_ephemeral_storage_capacity_bytes), value.B = avg(kube_summary_pod_ephemeral_storage_capacity_bytes, scalar: true) }, union: TRUE, by: { pod }, filter: { matchesValue(namespace, $Namespace) }\n| join [\n    timeseries { avg(kube_summary_container_logs_used_bytes), value.C = avg(kube_summary_container_logs_used_bytes, scalar: true) },\n    by: { pod, service.name },\n    filter: { matchesValue(namespace, $Namespace) }\n  ], on: { pod }, fields: {\n    `avg(kube_summary_container_logs_used_bytes)`,\n    service.name,\n    value.C\n  }",
            "queryConfig": {
                "version": "13.5.1",
                "subQueries": [
                {
                    "id": "A",
                    "isEnabled": true,
                    "datatype": "metrics",
                    "metric": {
                    "key": "kube_summary_pod_ephemeral_storage_used_bytes",
                    "aggregation": "avg"
                    },
                    "by": [
                    "pod"
                    ],
                    "filter": "namespace = $Namespace "
                },
                {
                    "id": "B",
                    "isEnabled": true,
                    "datatype": "metrics",
                    "metric": {
                    "key": "kube_summary_pod_ephemeral_storage_capacity_bytes",
                    "aggregation": "avg"
                    },
                    "by": [
                    "pod"
                    ],
                    "filter": "namespace = $Namespace "
                },
                {
                    "id": "C",
                    "isEnabled": true,
                    "datatype": "metrics",
                    "metric": {
                    "key": "kube_summary_container_logs_used_bytes",
                    "aggregation": "avg"
                    },
                    "by": [
                    "pod",
                    "service.name"
                    ],
                    "filter": "namespace = $Namespace "
                }
                ]
            },
            "subType": "dql-builder-metrics",
            "visualization": "lineChart",
            "davis": {
                "enabled": false,
                "davisVisualization": {
                "isAvailable": true
                }
            },
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                "gapPolicy": "gap",
                "circleChartSettings": {
                    "groupingThresholdType": "relative",
                    "groupingThresholdValue": 0,
                    "valueType": "relative"
                },
                "categoryOverrides": {},
                "curve": "linear",
                "pointsDisplay": "auto",
                "categoricalBarChartSettings": {
                    "layout": "horizontal",
                    "categoryAxisTickLayout": "horizontal",
                    "scale": "absolute",
                    "groupMode": "stacked",
                    "colorPaletteMode": "multi-color",
                    "valueAxisScale": "linear"
                },
                "colorPalette": "categorical",
                "valueRepresentation": "absolute",
                "truncationMode": "middle",
                "bandChartSettings": {
                    "lower": "avg(kube_summary_pod_ephemeral_storage_used_bytes)",
                    "upper": "avg(kube_summary_pod_ephemeral_storage_capacity_bytes)"
                },
                "xAxisScaling": "analyzedTimeframe",
                "xAxisLabel": "timeframe",
                "xAxisIsLabelVisible": false,
                "hiddenLegendFields": [
                    "interval",
                    "value.A",
                    "value.B"
                ],
                "fieldMapping": {
                    "timestamp": "timeframe",
                    "leftAxisValues": [
                    "avg(kube_summary_pod_ephemeral_storage_used_bytes)",
                    "avg(kube_summary_pod_ephemeral_storage_capacity_bytes)",
                    "avg(kube_summary_container_logs_used_bytes)"
                    ]
                },
                "leftYAxisSettings": {}
                },
                "singleValue": {
                "showLabel": true,
                "label": "",
                "prefixIcon": "AnalyticsIcon",
                "isIconVisible": false,
                "autoscale": true,
                "alignment": "center",
                "colorThresholdTarget": "value"
                },
                "table": {
                "rowDensity": "condensed",
                "enableSparklines": false,
                "hiddenColumns": [],
                "linewrapEnabled": false,
                "lineWrapIds": [],
                "monospacedFontEnabled": false,
                "monospacedFontColumns": [],
                "columnWidths": {},
                "columnTypeOverrides": [
                    {
                    "fields": [
                        "avg(kube_summary_pod_ephemeral_storage_used_bytes)",
                        "avg(kube_summary_pod_ephemeral_storage_capacity_bytes)"
                    ],
                    "value": "sparkline",
                    "id": 1747123574378
                    }
                ]
                },
                "honeycomb": {
                "shape": "hexagon",
                "legend": {
                    "hidden": false,
                    "position": "auto",
                    "ratio": "auto"
                },
                "dataMappings": {},
                "displayedFields": [],
                "truncationMode": "middle",
                "colorMode": "color-palette",
                "colorPalette": "categorical"
                },
                "histogram": {
                "legend": {
                    "position": "auto"
                },
                "yAxis": {
                    "label": "Frequency",
                    "isLabelVisible": true,
                    "scale": "linear"
                },
                "colorPalette": "categorical",
                "dataMappings": [],
                "variant": "single",
                "truncationMode": "middle"
                },
                "valueBoundaries": {
                "min": "auto",
                "max": "auto"
                },
                "autoSelectVisualization": true
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            }
            },
            "11": {
            "type": "data",
            "title": "Ephemeral Storage Available",
            "query": "timeseries { max(kube_summary_pod_ephemeral_storage_available_bytes), value.A = avg(kube_summary_pod_ephemeral_storage_available_bytes, scalar: true), max(kube_summary_pod_ephemeral_storage_capacity_bytes), value.B = avg(kube_summary_pod_ephemeral_storage_capacity_bytes, scalar: true) }, union: TRUE, by: { pod }, filter: { matchesValue(namespace, $Namespace) }\n| fieldsAdd C = `max(kube_summary_pod_ephemeral_storage_available_bytes)`[]/`max(kube_summary_pod_ephemeral_storage_capacity_bytes)`[] *100",
            "queryConfig": {
                "version": "13.5.1",
                "subQueries": [
                {
                    "id": "A",
                    "isEnabled": true,
                    "datatype": "metrics",
                    "metric": {
                    "key": "kube_summary_pod_ephemeral_storage_available_bytes",
                    "aggregation": "max"
                    },
                    "by": [
                    "pod"
                    ],
                    "filter": "namespace = $Namespace "
                },
                {
                    "id": "B",
                    "isEnabled": true,
                    "datatype": "metrics",
                    "metric": {
                    "key": "kube_summary_pod_ephemeral_storage_capacity_bytes",
                    "aggregation": "max"
                    },
                    "by": [
                    "pod"
                    ],
                    "filter": "namespace = $Namespace "
                },
                {
                    "id": "C",
                    "isEnabled": true,
                    "datatype": "expression",
                    "expression": "A/B *100"
                }
                ]
            },
            "subType": "dql-builder-metrics",
            "visualization": "lineChart",
            "davis": {
                "enabled": false,
                "davisVisualization": {
                "isAvailable": true
                }
            },
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                "gapPolicy": "gap",
                "circleChartSettings": {
                    "groupingThresholdType": "relative",
                    "groupingThresholdValue": 0,
                    "valueType": "relative"
                },
                "categoryOverrides": {},
                "curve": "linear",
                "pointsDisplay": "auto",
                "categoricalBarChartSettings": {
                    "layout": "horizontal",
                    "categoryAxisTickLayout": "horizontal",
                    "scale": "absolute",
                    "groupMode": "stacked",
                    "colorPaletteMode": "multi-color",
                    "valueAxisScale": "linear"
                },
                "colorPalette": "categorical",
                "valueRepresentation": "absolute",
                "truncationMode": "middle",
                "bandChartSettings": {
                    "lower": "max(kube_summary_pod_ephemeral_storage_available_bytes)",
                    "upper": "max(kube_summary_pod_ephemeral_storage_capacity_bytes)"
                },
                "xAxisScaling": "analyzedTimeframe",
                "xAxisLabel": "timeframe",
                "xAxisIsLabelVisible": false,
                "hiddenLegendFields": [
                    "interval",
                    "value.A"
                ],
                "fieldMapping": {
                    "timestamp": "timeframe",
                    "leftAxisValues": [
                    "C"
                    ]
                },
                "leftYAxisSettings": {}
                },
                "singleValue": {
                "showLabel": true,
                "label": "",
                "prefixIcon": "AnalyticsIcon",
                "isIconVisible": false,
                "autoscale": true,
                "alignment": "center",
                "colorThresholdTarget": "value"
                },
                "table": {
                "rowDensity": "condensed",
                "enableSparklines": false,
                "hiddenColumns": [],
                "linewrapEnabled": false,
                "lineWrapIds": [],
                "monospacedFontEnabled": false,
                "monospacedFontColumns": [],
                "columnWidths": {},
                "columnTypeOverrides": [
                    {
                    "fields": [
                        "max(kube_summary_pod_ephemeral_storage_available_bytes)"
                    ],
                    "value": "sparkline",
                    "id": 1747124220763
                    }
                ]
                },
                "honeycomb": {
                "shape": "hexagon",
                "legend": {
                    "hidden": false,
                    "position": "auto",
                    "ratio": "auto"
                },
                "dataMappings": {},
                "displayedFields": [],
                "truncationMode": "middle",
                "colorMode": "color-palette",
                "colorPalette": "categorical"
                },
                "histogram": {
                "legend": {
                    "position": "auto"
                },
                "yAxis": {
                    "label": "Frequency",
                    "isLabelVisible": true,
                    "scale": "linear"
                },
                "colorPalette": "categorical",
                "dataMappings": [],
                "variant": "single",
                "truncationMode": "middle"
                },
                "valueBoundaries": {
                "min": "auto",
                "max": "auto"
                },
                "autoSelectVisualization": true
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            }
            }
        },
        "layouts": {
            "3": {
            "x": 0,
            "y": 8,
            "w": 12,
            "h": 8
            },
            "5": {
            "x": 12,
            "y": 8,
            "w": 12,
            "h": 8
            },
            "8": {
            "x": 0,
            "y": 0,
            "w": 12,
            "h": 8
            },
            "10": {
            "x": 0,
            "y": 16,
            "w": 24,
            "h": 6
            },
            "11": {
            "x": 12,
            "y": 0,
            "w": 12,
            "h": 8
            }
        },
        "importedWithCode": false,
        "settings": {}
        }