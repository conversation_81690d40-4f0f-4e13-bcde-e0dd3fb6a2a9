---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: karpenter-dashboard
  labels:
    scope: global
spec:
  name: "Karpenter Metrics"
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "type": "query",
                "visible": true,
                "input": "timeseries a = min(karpenter_cluster_state_synced), by: { k8s.cluster.name } \n| fields k8s.cluster.name",
                "multiple": false,
                "version": 1
            },
            {
                "key": "Nodepool",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries count(karpenter_nodepools_limit), by: { nodepool },\nfilter: {$Cluster == k8s.cluster.name AND resource_type == \"cpu\"}\n| fields nodepool",
                "multiple": true
            }
        ],
        "tiles": {
            "1": {
                "title": "Sync Status",
                "type": "data",
                "query": "timeseries is_synced = sum(karpenter_cluster_state_synced), by: { k8s.cluster.name } \n| filter $Cluster == k8s.cluster.name\n| fieldsAdd is_synced = toLong(arrayLast(is_synced))\n| fieldsAdd isSynced = if(is_synced == 1, \"Synced\", else: \"Not Synced\")\n",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "isSynced",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "Synced"
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "Not Synced"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "k8s.cluster.name,isSynced",
                            "valueAxisLabel": "is_synced",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "is_synced"
                        ],
                        "leftYAxisSettings": {},
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "interval"
                            ]
                        },
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "name",
                        "prefixIcon": "",
                        "recordField": "isSynced",
                        "autoscale": true,
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isVisible": true,
                            "isRelative": true,
                            "isLabelVisible": false,
                            "label": "",
                            "trendField": "is_synced"
                        },
                        "colorThresholdTarget": "value",
                        "sparklineSettings": {
                            "variant": "line",
                            "lineType": "linear",
                            "showTicks": false,
                            "color": {
                                "Default": "var(--dt-colors-charts-categorical-color-07-default, #438fb1)"
                            },
                            "record": "is_synced"
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "is_synced"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "isSynced"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "is_synced",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    },
                    "icon": {
                        "showIcon": false,
                        "icon": ""
                    },
                    "dataMapping": {
                        "value": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "is_synced",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741861982586
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "8": {
                "title": "Cloud Operations Error Rate",
                "type": "data",
                "query": "timeseries karpenter_cloudprovider_errors_total = sum(karpenter_cloudprovider_errors_total, rate: 5m)\n,by: { k8s.cluster.name, controller, error}\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd errors_total = karpenter_cloudprovider_errors_total[] / 300\n| fields timeframe, interval, errors_total, controller, error",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "k8s.cluster.name,controller,error",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "end",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster.name",
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "errors_total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        },
                        "tooltip": {
                            "variant": "shared",
                            "seriesDisplayMode": "single-line"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "errors_total_avg",
                        "prefixIcon": "",
                        "recordField": "errors_total_avg",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "errors_total"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "errors_total"
                                ],
                                "value": "sparkline",
                                "id": 1744723858191
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "k8s.cluster.name"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "controller",
                            "error"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "dataMapping": {
                        "value": "errors_total_avg"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "errors_total_avg"
                    },
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "9": {
                "title": "Cloud Operations Latencies by Controller/Method",
                "type": "data",
                "query": "timeseries {\nlatency = percentile(karpenter_cloudprovider_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name, controller, method }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, latency, controller, method\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "always",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "controller,method",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "max_request_duration_seconds",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "latency"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "karpenter_cloudprovider_duration_seconds",
                            "scale": "linear",
                            "min": "auto",
                            "max": "auto"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "tooltip": {
                            "variant": "shared",
                            "seriesDisplayMode": "single-line"
                        },
                        "seriesOverrides": []
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "error",
                        "prefixIcon": "",
                        "recordField": "error",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "percentile_95"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "latency"
                                ],
                                "value": "sparkline",
                                "id": 1745317179394
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "controller"
                        },
                        "displayedFields": [
                            "controller",
                            "method"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "controller"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "interval"
                    },
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "title": "Node Pool CPU Usage",
                "type": "data",
                "query": "timeseries { \nby: {resource_type, nodepool, k8s.cluster.name}, filter: { k8s.cluster.name == $Cluster AND in(nodepool, $Nodepool) AND resource_type == \"cpu\" },\ncpu_limit = sum(karpenter_nodepools_limit),\ncpu_usage = sum(karpenter_nodepools_usage)\n\n} \n| fieldsAdd usage_percent = (cpu_usage[] / cpu_limit[]) * 100\n| fieldsAdd usage_percent = arrayLast(usage_percent)\n| fields nodepool, cpu_usage, cpu_limit, usage_percent, timeframe",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "usage_percent",
                            "valueAxisLabel": "usage_percent",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "usage_percent",
                            "arrayLast(usage_percent)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "usage_percent"
                            ]
                        },
                        "leftYAxisSettings": {
                            "scale": "linear"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "usage_percent"
                                ],
                                "override": {
                                    "geometry": "area"
                                }
                            }
                        ],
                        "tooltip": {
                            "seriesDisplayMode": "single-line",
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "error",
                        "prefixIcon": "",
                        "recordField": "error",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "cpu_limit"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "usage_percent",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": []
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "usage_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741869899341
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "11": {
                "title": "Node Pool Memory Usage",
                "type": "data",
                "query": "timeseries { \nby: {resource_type, nodepool, k8s.cluster.name}, filter: { k8s.cluster.name == $Cluster AND in(nodepool, $Nodepool) AND resource_type == \"memory\" },\nmemory_limit = sum(karpenter_nodepools_limit),\nmemory_usage = sum(karpenter_nodepools_usage)\n\n} \n| fieldsAdd usage_percent = (memory_usage[] / memory_limit[]) * 100\n| fieldsAdd usage_percent = arrayLast(usage_percent)\n| fields nodepool, memory_usage, memory_limit, usage_percent, timeframe",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "usage_percent",
                            "valueAxisLabel": "usage_percent",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "usage_percent"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "usage_percent"
                            ]
                        },
                        "leftYAxisSettings": {
                            "scale": "linear"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "usage_percent"
                                ],
                                "override": {
                                    "geometry": "area"
                                }
                            }
                        ],
                        "tooltip": {
                            "seriesDisplayMode": "single-line",
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "error",
                        "prefixIcon": "",
                        "recordField": "error",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "cpu_limit"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "usage_percent",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": []
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "usage_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741869899341
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "14": {
                "title": "Nodes",
                "type": "data",
                "query": "timeseries sum(karpenter_nodes_system_overhead), \nby: { nodepool, capacity_type, instance_type, zone, k8s.cluster.name, node_name, resource_type }\n| filter in(nodepool, $Nodepool) AND resource_type == \"cpu\" AND k8s.cluster.name == $Cluster\n| fields nodepool, node_name, capacity_type, instance_type, zone ",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "nodepool,node_name,capacity_type,instance_type,zone",
                            "valueAxisLabel": "",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "nodes_count",
                            "interval",
                            "value.A",
                            "count"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "nodes_count",
                        "prefixIcon": "",
                        "recordField": "value",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "value"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "default",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": true,
                        "lineWrapIds": [
                            [
                                "capacity_type"
                            ]
                        ],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [],
                        "colorThresholdTarget": "value",
                        "enableThresholdInRow": false
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "nodepool"
                        },
                        "displayedFields": [
                            "nodepool",
                            "node_name",
                            "capacity_type",
                            "instance_type",
                            "zone"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "node_name"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "nodes_count"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "nodes_count"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "Nodes Count",
                "type": "data",
                "query": "timeseries nodeCount = count(karpenter_nodes_system_overhead) \n,by: {nodepool}\n,filter: {in(nodepool, $Nodepool) AND resource_type == \"cpu\" AND k8s.cluster.name == $Cluster}\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "interval",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "nodeCount"
                            ]
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "nodeCount"
                        ],
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": true
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "nodepool",
                        "prefixIcon": "",
                        "recordField": "nodepool",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "a"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "nodeCount"
                                ],
                                "value": "sparkline",
                                "id": 1745315162250
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": []
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "16": {
                "title": "Nodes Count By Zone",
                "type": "data",
                "query": "timeseries nodeCount = count(karpenter_nodes_system_overhead), by: {nodepool, zone},\nfilter: {in(nodepool, $Nodepool) AND resource_type == \"cpu\" AND k8s.cluster.name == $Cluster}\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "nodepool,zone",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "nodeCount"
                            ]
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "seriesDisplayMode": "multi-line",
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "nodepool",
                        "prefixIcon": "",
                        "recordField": "nodepool",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "a"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "nodeCount"
                                ],
                                "value": "sparkline",
                                "id": 1745315192768
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "nodepool"
                        },
                        "displayedFields": [
                            "nodepool",
                            "zone"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "zone"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "17": {
                "title": "Nodes Count By Capacity",
                "type": "data",
                "query": "timeseries nodeCount = count(karpenter_nodes_system_overhead), by: {nodepool, capacity_type},\nfilter: {in(nodepool, $Nodepool) AND resource_type == \"cpu\" AND k8s.cluster.name == $Cluster}\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "nodepool,capacity_type",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "nodeCount"
                            ]
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "seriesDisplayMode": "multi-line",
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "nodepool",
                        "prefixIcon": "",
                        "recordField": "nodepool",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "a"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "nodeCount"
                                ],
                                "value": "sparkline",
                                "id": 1745315196757
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "nodepool"
                        },
                        "displayedFields": [
                            "nodepool",
                            "capacity_type"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "nodepool",
                            "capacity_type"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "18": {
                "title": "Nodes Count By Instance Type",
                "type": "data",
                "query": "timeseries nodeCount = count(karpenter_nodes_system_overhead), by: {nodepool, instance_type},\nfilter: {in(nodepool, $Nodepool) AND resource_type == \"cpu\" AND k8s.cluster.name == $Cluster}\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "nodepool,instance_type",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "nodeCount"
                            ]
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "seriesDisplayMode": "multi-line",
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "nodepool",
                        "prefixIcon": "",
                        "recordField": "nodepool",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "a"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "nodeCount"
                                ],
                                "value": "sparkline",
                                "id": 1745315187452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "nodepool"
                        },
                        "displayedFields": [
                            "nodepool",
                            "instance_type"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "nodepool"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "19": {
                "title": "Pods Count",
                "type": "data",
                "query": "timeseries podCount = count(karpenter_pods_state), by: {phase, nodepool, namespace},\nfilter: {in(nodepool, $Nodepool) AND k8s.cluster.name == $Cluster}\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "nodepool",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "podCount"
                            ]
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        },
                        "tooltip": {
                            "seriesDisplayMode": "multi-line",
                            "variant": "shared"
                        },
                        "seriesOverrides": []
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "nodepool",
                        "prefixIcon": "",
                        "recordField": "nodepool",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "a"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "podCount"
                                ],
                                "value": "sparkline",
                                "id": 1745315172338
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "nodepool"
                        },
                        "displayedFields": [
                            "nodepool"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "nodepool"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "21": {
                "title": "Disruption Rate ",
                "type": "data",
                "query": "timeseries karpenter_voluntary_disruption_decisions_total = sum(karpenter_voluntary_disruption_decisions_total, rate: 5m),\nby: { k8s.cluster.name, consolidation_type, decision, reason }\n| filter $Cluster == k8s.cluster.name\n| fieldsAdd result = karpenter_voluntary_disruption_decisions_total[] / 300\n| fields timeframe, interval, result, consolidation_type, decision, reason\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "consolidation_type,decision,reason",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster.name",
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "result"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        },
                        "tooltip": {
                            "variant": "shared",
                            "seriesDisplayMode": "single-line"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "errors_total_avg",
                        "prefixIcon": "",
                        "recordField": "errors_total_avg",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "errors_total"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "result"
                                ],
                                "value": "sparkline",
                                "id": 1744724039589
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "consolidation_type"
                        },
                        "displayedFields": [
                            "consolidation_type",
                            "decision",
                            "reason"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "consolidation_type",
                            "decision",
                            "reason"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "errors_total_avg"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "errors_total_avg"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "22": {
                "title": "Allowed Disruptions",
                "type": "data",
                "query": "timeseries sum(karpenter_nodepools_allowed_disruptions),\nfilter: {in(nodepool, $Nodepool) AND $Cluster == k8s.cluster.name},\nby: {nodepool, reason}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "nodepool",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster.name",
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(karpenter_nodepools_allowed_disruptions)"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        },
                        "tooltip": {
                            "variant": "shared",
                            "seriesDisplayMode": "single-line"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "errors_total_avg",
                        "prefixIcon": "",
                        "recordField": "errors_total_avg",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "errors_total"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(karpenter_nodepools_allowed_disruptions)"
                                ],
                                "value": "sparkline",
                                "id": 1745315207555
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "nodepool"
                        },
                        "displayedFields": [
                            "nodepool"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "nodepool"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "errors_total_avg"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "errors_total_avg"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "23": {
                "title": "Nodeclaim Disruption Rate",
                "type": "data",
                "query": "timeseries karpenter_nodeclaims_disrupted_total = sum(karpenter_nodeclaims_disrupted_total, rate: 5m),\nby: { k8s.cluster.name, reason, nodepool, capacity_type }\n| filter in(nodepool, $Nodepool) AND $Cluster == k8s.cluster.name\n| fieldsAdd result = karpenter_nodeclaims_disrupted_total[] / 300\n| fields timeframe, interval, nodepool, result, reason, capacity_type\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "nodepool,reason,capacity_type",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "result"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        },
                        "tooltip": {
                            "variant": "shared",
                            "seriesDisplayMode": "single-line"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "errors_total_avg",
                        "prefixIcon": "",
                        "recordField": "errors_total_avg",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "errors_total"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "result"
                                ],
                                "value": "sparkline",
                                "id": 1745315235153
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "nodepool"
                        },
                        "displayedFields": [
                            "nodepool",
                            "reason",
                            "capacity_type"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "nodepool"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "errors_total_avg"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "errors_total_avg"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "24": {
                "type": "markdown",
                "content": "# Node Pools"
            },
            "25": {
                "type": "markdown",
                "content": "# Disruption"
            },
            "26": {
                "type": "markdown",
                "content": "# Cloud Provider"
            }
        },
        "layouts": {
            "1": {
                "x": 0,
                "y": 1,
                "w": 4,
                "h": 5
            },
            "8": {
                "x": 4,
                "y": 1,
                "w": 9,
                "h": 5
            },
            "9": {
                "x": 13,
                "y": 1,
                "w": 11,
                "h": 5
            },
            "10": {
                "x": 0,
                "y": 7,
                "w": 12,
                "h": 6
            },
            "11": {
                "x": 12,
                "y": 7,
                "w": 12,
                "h": 6
            },
            "14": {
                "x": 0,
                "y": 24,
                "w": 24,
                "h": 6
            },
            "15": {
                "x": 0,
                "y": 13,
                "w": 12,
                "h": 5
            },
            "16": {
                "x": 8,
                "y": 18,
                "w": 8,
                "h": 6
            },
            "17": {
                "x": 16,
                "y": 18,
                "w": 8,
                "h": 6
            },
            "18": {
                "x": 0,
                "y": 18,
                "w": 8,
                "h": 6
            },
            "19": {
                "x": 12,
                "y": 13,
                "w": 12,
                "h": 5
            },
            "21": {
                "x": 0,
                "y": 31,
                "w": 8,
                "h": 5
            },
            "22": {
                "x": 8,
                "y": 31,
                "w": 8,
                "h": 5
            },
            "23": {
                "x": 16,
                "y": 31,
                "w": 8,
                "h": 5
            },
            "24": {
                "x": 0,
                "y": 6,
                "w": 23,
                "h": 1
            },
            "25": {
                "x": 0,
                "y": 30,
                "w": 23,
                "h": 1
            },
            "26": {
                "x": 0,
                "y": 0,
                "w": 23,
                "h": 1
            }
        },
        "importedWithCode": false,
        "settings": {}
    }
