---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: Kubernetes-api-server-dashboard
  labels:
    scope: global
spec:
  name: "Kubernetes API Server Metrics"
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries a = sum(apiserver_request_total), by: { k8s.cluster.name } \n| fields k8s.cluster.name",
                "multiple": false
            }
        ],
        "tiles": {
            "0": {
                "title": "API Requests",
                "type": "data",
                "query": "timeseries {\napiserver_request_total = sum(apiserver_request_total, rate:5m),\napiserver_request_terminations_total = sum(apiserver_request_terminations_total, rate:5m)\n}\n,by: { k8s.cluster.name }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = apiserver_request_total[] / 300\n| fieldsAdd terminated_requests_total = apiserver_request_terminations_total[] / 300\n| fields timeframe, interval, requests_total, terminated_requests_total",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total",
                                "terminated_requests_total"
                            ]
                        },
                        "bandChartSettings": {
                            "lower": "requests_total",
                            "upper": "requests_total"
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1744619148079
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "1": {
                "title": "API Requests by Code, Verb, Resource, Scope",
                "type": "data",
                "query": "timeseries {\napiserver_request_total = sum(apiserver_request_total, rate:5m)\n//,apiserver_request_terminations_total = sum(apiserver_request_terminations_total, rate:5m)\n}\n,by: { k8s.cluster.name, code, verb, resource, scope  }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = apiserver_request_total[] / 300\n//| fieldsAdd terminated_requests_total = apiserver_request_terminations_total[] / 300\n| fields timeframe, interval, requests_total, code, verb, resource, scope",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1745406941395
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 21
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "6": {
                "type": "markdown",
                "content": "# API Requests"
            },
            "7": {
                "title": "Deprecated Kubernetes Resources",
                "type": "data",
                "query": "timeseries apiserver_requested_deprecated_apis = count(apiserver_requested_deprecated_apis)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { group, resource, subresource, version, removed_release }\n| fields group, resource, subresource, version, removed_release\n",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "position": "right",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "default",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [],
                        "enableThresholdInRow": false
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "8": {
                "title": "API Request Latency by Code, Verb, Resource, Scope",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(apiserver_request_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { verb, resource, scope, k8s.cluster.name }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency, verb, resource, scope",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620981251
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [],
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 18
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "title": "API Request Latency",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(apiserver_request_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620965716
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "14": {
                "type": "markdown",
                "content": "# Admission Webhooks"
            },
            "15": {
                "title": "Admission Webhook Requests by Name, Operation, Type, Rejection",
                "type": "data",
                "query": "timeseries {\napiserver_admission_webhook_request_total = sum(apiserver_admission_webhook_request_total, rate:5m)\n}\n,by: { k8s.cluster.name, name, operation, type, rejected }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = apiserver_admission_webhook_request_total[] / 300\n| fields timeframe, interval, requests_total, name, operation, type, rejected",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "position": "right",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1745407770910
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "16": {
                "title": "Admission Webhook Requests",
                "type": "data",
                "query": "timeseries {\napiserver_admission_webhook_request_total = sum(apiserver_admission_webhook_request_total, rate:5m)\n}\n,by: { k8s.cluster.name }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = apiserver_admission_webhook_request_total[] / 300\n| fields timeframe, interval, requests_total",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1745407734313
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "17": {
                "title": "Admission Webhook Rejected Requests",
                "type": "data",
                "query": "timeseries {\napiserver_admission_webhook_rejection_count = sum(apiserver_admission_webhook_rejection_count, rate:5m)\n}\n,by: { k8s.cluster.name }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd rejected_requests_total = apiserver_admission_webhook_rejection_count[] / 300\n| fields timeframe, interval, rejected_requests_total",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "rejected_requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "rejected_requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1745407744668
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "26": {
                "title": "Admission Webhook Fail Open count",
                "type": "data",
                "query": "timeseries apiserver_admission_webhook_fail_open_count = sum(apiserver_admission_webhook_fail_open_count, rate:5m)\n,by: { k8s.cluster.name, name, type }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = apiserver_admission_webhook_fail_open_count[] / 300\n| fields timeframe, interval, requests_total, name, type",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1744619148079
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "28": {
                "type": "markdown",
                "content": "# Etcd"
            },
            "33": {
                "title": "APIServices Marked as Unavailable",
                "description": "APIServices which are marked as unavailable",
                "type": "data",
                "query": "timeseries aggregator_unavailable_apiservice_total = sum(aggregator_unavailable_apiservice_total, rate:5m)\n,by: { k8s.cluster.name, name, reason }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd aggregator_total = aggregator_unavailable_apiservice_total[] / 300\n| fields timeframe, interval, aggregator_unavailable_apiservice_total\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "aggregator_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        },
                        "bandChartSettings": {
                            "lower": "aggregator_unavailable_apiservice_total",
                            "upper": "aggregator_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "aggregator_unavailable_apiservice_total"
                                ],
                                "value": "sparkline",
                                "id": 1744628825288
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "34": {
                "title": "Etcd Requests",
                "type": "data",
                "query": "timeseries {\netcd_requests_total = sum(etcd_requests_total, rate:5m),\netcd_request_errors_total = sum(etcd_request_errors_total, rate:5m)\n}\n,by: { k8s.cluster.name }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = etcd_requests_total[] / 300\n| fieldsAdd failed_requests_total = etcd_request_errors_total[] / 300\n| fields timeframe, interval, requests_total, failed_requests_total\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total",
                                "failed_requests_total"
                            ]
                        },
                        "bandChartSettings": {
                            "lower": "requests_total",
                            "upper": "failed_requests_total"
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1744619148079
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "35": {
                "title": "Etcd Request Latency",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(etcd_request_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: {k8s.cluster.name}\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620965716
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "37": {
                "title": "Etcd Requests by Type, Operation",
                "type": "data",
                "query": "timeseries {\netcd_requests_total = sum(etcd_requests_total, rate:5m)\n}\n,by: { k8s.cluster.name, type, operation }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = etcd_requests_total[] / 300\n| fields timeframe, interval, requests_total, type, operation\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1744619148079
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "42": {
                "title": "Etcd Request Latency by Type, Operation",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(etcd_request_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: {k8s.cluster.name, type, operation}\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency, type, operation\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620965716
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "43": {
                "title": "Active Long-Running API Requests",
                "type": "data",
                "query": "timeseries apiserver_longrunning_requests = sum(apiserver_longrunning_requests)\n,by: { k8s.cluster.name, component, group, resource, subresource, scope, verb, version }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requestsCount = arrayLast(apiserver_longrunning_requests)\n| fields component, group, resource, subresource, scope, verb, version, requestsCount\n",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "default",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [],
                        "sortBy": {
                            "columnId": "[\"resource\"]",
                            "direction": "unset"
                        },
                        "colorThresholdTarget": "value",
                        "enableThresholdInRow": false
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [
                        {
                            "identifier": "requestsCount",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1744632828539
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "44": {
                "type": "markdown",
                "content": "# Client Requests"
            },
            "45": {
                "title": "Client Requests",
                "type": "data",
                "query": "timeseries {\nrest_client_requests_total = sum(rest_client_requests_total, rate:5m)\n}\n,by: { k8s.cluster.name }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = rest_client_requests_total[] / 300\n| fields timeframe, interval, requests_total",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1744708781932
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "46": {
                "title": "Client Requests by Host, Method, Code",
                "type": "data",
                "query": "timeseries {\nrest_client_requests_total = sum(rest_client_requests_total, rate:5m)\n}\n,by: { k8s.cluster.name, host, method, code }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = rest_client_requests_total[] / 300\n| fields timeframe, interval, requests_total, host, method, code",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1744708781932
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "50": {
                "title": "Client Requests Latency",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(rest_client_request_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620965716
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "51": {
                "title": "Current Inflight Requests",
                "type": "data",
                "query": "timeseries apiserver_current_inflight_requests = avg(apiserver_current_inflight_requests)\n,filter: { k8s.cluster.name == $Cluster }\n| fieldsAdd currentInflight = arrayLast(apiserver_current_inflight_requests)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "apiserver_current_inflight_requests"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "currentInflight",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "currentInflight",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "apiserver_current_inflight_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "apiserver_current_inflight_requests"
                                ],
                                "value": "sparkline",
                                "id": 1744709351121
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "currentInflight",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1744709364398
                        }
                    ],
                    "dataMapping": {
                        "value": "currentInflight"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "currentInflight"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "52": {
                "title": "Current Executing Requests",
                "type": "data",
                "query": "timeseries apiserver_flowcontrol_current_executing_requests = avg(apiserver_flowcontrol_current_executing_requests)\n,filter: { k8s.cluster.name == $Cluster }\n| fieldsAdd currentRequests = arrayLast(apiserver_flowcontrol_current_executing_requests)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "apiserver_flowcontrol_current_executing_requests"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "currentInflight",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "currentRequests",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "apiserver_current_inflight_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "apiserver_flowcontrol_current_executing_requests"
                                ],
                                "value": "sparkline",
                                "id": 1744709774898
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [
                        {
                            "identifier": "currentRequests",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1744709364398
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "53": {
                "title": "Current Occupied Seats",
                "type": "data",
                "query": "timeseries apiserver_flowcontrol_current_executing_seats = avg(apiserver_flowcontrol_current_executing_seats)\n,filter: { k8s.cluster.name == $Cluster }\n| fieldsAdd currentRequests = arrayLast(apiserver_flowcontrol_current_executing_seats)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "apiserver_flowcontrol_current_executing_seats"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "currentInflight",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "currentRequests",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "apiserver_current_inflight_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "apiserver_flowcontrol_current_executing_seats"
                                ],
                                "value": "sparkline",
                                "id": 1744709924114
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [
                        {
                            "identifier": "currentRequests",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1744709364398
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "54": {
                "type": "markdown",
                "content": "# API Priority & Fairness"
            },
            "55": {
                "title": "Requests Rejected by API Priority and Fairness Subsystem",
                "type": "data",
                "query": "timeseries {\napiserver_flowcontrol_rejected_requests_total = sum(apiserver_flowcontrol_rejected_requests_total, rate:5m)\n}\n,by: { k8s.cluster.name, flow_schema, priority_level, reason, instance}\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd requests_total = apiserver_flowcontrol_rejected_requests_total[] / 300\n| fields timeframe, interval, requests_total, flow_schema, priority_level, reason, instance\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "56": {
                "title": "Current Inflight Requests",
                "type": "data",
                "query": "timeseries apiserver_current_inflight_requests = avg(apiserver_current_inflight_requests)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { request_kind }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "currentInflight"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "apiserver_current_inflight_requests"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "currentInflight",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "currentInflight",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "apiserver_current_inflight_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "apiserver_current_inflight_requests"
                                ],
                                "value": "sparkline",
                                "id": 1744709351121
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "57": {
                "title": "Current Executing Requests",
                "type": "data",
                "query": "timeseries apiserver_flowcontrol_current_executing_requests = avg(apiserver_flowcontrol_current_executing_requests)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { flow_schema, priority_level, instance }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "currentInflight"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "apiserver_flowcontrol_current_executing_requests"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "currentInflight",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "currentInflight",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "apiserver_current_inflight_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "apiserver_flowcontrol_current_executing_requests"
                                ],
                                "value": "sparkline",
                                "id": 1744711355069
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "58": {
                "title": "Current Occupied Seats",
                "type": "data",
                "query": "timeseries apiserver_flowcontrol_current_executing_seats = avg(apiserver_flowcontrol_current_executing_seats)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { flow_schema, priority_level, instance }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "currentInflight"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "apiserver_flowcontrol_current_executing_seats"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "currentInflight",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "currentInflight",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "apiserver_current_inflight_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "apiserver_flowcontrol_current_executing_seats"
                                ],
                                "value": "sparkline",
                                "id": 1744711355000
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "59": {
                "title": "Time Spent by Requests Waiting in its Queue",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(apiserver_flowcontrol_request_wait_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name, execute, flow_schema, priority_level, instance }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency, execute, flow_schema, priority_level, instance\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620965716
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "60": {
                "title": "Request Execution Time",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(apiserver_flowcontrol_request_execution_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name, type, flow_schema, priority_level, instance }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency, type, flow_schema, priority_level, instance\n\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620965716
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "61": {
                "title": "Nominal Execution Seats by Priority Levels",
                "type": "data",
                "query": "timeseries apiserver_flowcontrol_nominal_limit_seats = avg(apiserver_flowcontrol_nominal_limit_seats)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { priority_level, instance}\n| fieldsAdd nominalSeats = arrayLast(apiserver_flowcontrol_nominal_limit_seats)\n| fields priority_level, alias: priorityLevel, instance, nominalSeats",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "currentInflight",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "currentRequests",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "apiserver_current_inflight_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "default",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {
                            "[\"priorityLevel\"]": 410.9453125
                        },
                        "columnTypeOverrides": [],
                        "enableThresholdInRow": false
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [
                        {
                            "identifier": "nominalSeats",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1744709364398
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "62": {
                "title": "Admission Webhook Rejected Requests by Name, Operation, Type, Code",
                "type": "data",
                "query": "timeseries {\napiserver_admission_webhook_rejection_count = sum(apiserver_admission_webhook_rejection_count, rate:5m)\n}\n,by: { k8s.cluster.name, name, operation, type, rejection, error_type, rejection_code }\n| filter k8s.cluster.name == $Cluster\n| fieldsAdd rejected_requests_total = apiserver_admission_webhook_rejection_count[] / 300\n| fields timeframe, interval, rejected_requests_total, name, operation, type, rejection, error_type, rejection_code",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "rejected_requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "position": "right",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "rejected_requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1745407668372
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 24
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "63": {
                "title": "Client Requests Latency by Host, Method, Code",
                "type": "data",
                "query": "timeseries {\nrequestLatency = percentile(rest_client_request_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name, host, method, code }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, requestLatency, host, method, code",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requestLatency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "apiserver_request_duration_seconds"
                        },
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "hidden": false,
                            "position": "right"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requestLatency"
                                ],
                                "value": "sparkline",
                                "id": 1744620965716
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "64": {
                "title": "API Requests with Errors",
                "type": "data",
                "query": "timeseries {\napiserver_request_total = sum(apiserver_request_total, rate:5m)\n//,apiserver_request_terminations_total = sum(apiserver_request_terminations_total, rate:5m)\n}\n,by: { k8s.cluster.name, code, verb, resource, scope  }\n| filter k8s.cluster.name == $Cluster AND code <= 299\n| fieldsAdd requests_total = apiserver_request_total[] / 300\n//| fieldsAdd terminated_requests_total = apiserver_request_terminations_total[] / 300\n| fields timeframe, interval, requests_total, code, verb, resource, scope",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1745406941395
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 21
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            }
        },
        "layouts": {
            "0": {
                "x": 0,
                "y": 1,
                "w": 12,
                "h": 5
            },
            "1": {
                "x": 0,
                "y": 6,
                "w": 12,
                "h": 6
            },
            "6": {
                "x": 0,
                "y": 0,
                "w": 24,
                "h": 1
            },
            "7": {
                "x": 0,
                "y": 17,
                "w": 24,
                "h": 5
            },
            "8": {
                "x": 12,
                "y": 6,
                "w": 12,
                "h": 6
            },
            "12": {
                "x": 12,
                "y": 1,
                "w": 12,
                "h": 5
            },
            "14": {
                "x": 0,
                "y": 27,
                "w": 24,
                "h": 1
            },
            "15": {
                "x": 0,
                "y": 33,
                "w": 12,
                "h": 5
            },
            "16": {
                "x": 0,
                "y": 28,
                "w": 12,
                "h": 5
            },
            "17": {
                "x": 12,
                "y": 28,
                "w": 12,
                "h": 5
            },
            "26": {
                "x": 0,
                "y": 38,
                "w": 12,
                "h": 5
            },
            "28": {
                "x": 0,
                "y": 43,
                "w": 24,
                "h": 1
            },
            "33": {
                "x": 12,
                "y": 12,
                "w": 12,
                "h": 5
            },
            "34": {
                "x": 0,
                "y": 44,
                "w": 12,
                "h": 5
            },
            "35": {
                "x": 12,
                "y": 44,
                "w": 12,
                "h": 5
            },
            "37": {
                "x": 0,
                "y": 49,
                "w": 12,
                "h": 5
            },
            "42": {
                "x": 12,
                "y": 49,
                "w": 12,
                "h": 5
            },
            "43": {
                "x": 0,
                "y": 22,
                "w": 24,
                "h": 5
            },
            "44": {
                "x": 0,
                "y": 54,
                "w": 24,
                "h": 1
            },
            "45": {
                "x": 0,
                "y": 55,
                "w": 12,
                "h": 5
            },
            "46": {
                "x": 0,
                "y": 60,
                "w": 12,
                "h": 5
            },
            "50": {
                "x": 12,
                "y": 55,
                "w": 12,
                "h": 5
            },
            "51": {
                "x": 0,
                "y": 66,
                "w": 8,
                "h": 3
            },
            "52": {
                "x": 8,
                "y": 66,
                "w": 8,
                "h": 3
            },
            "53": {
                "x": 16,
                "y": 66,
                "w": 8,
                "h": 3
            },
            "54": {
                "x": 0,
                "y": 65,
                "w": 24,
                "h": 1
            },
            "55": {
                "x": 12,
                "y": 74,
                "w": 12,
                "h": 5
            },
            "56": {
                "x": 0,
                "y": 69,
                "w": 12,
                "h": 5
            },
            "57": {
                "x": 12,
                "y": 69,
                "w": 12,
                "h": 5
            },
            "58": {
                "x": 0,
                "y": 74,
                "w": 12,
                "h": 5
            },
            "59": {
                "x": 0,
                "y": 79,
                "w": 12,
                "h": 5
            },
            "60": {
                "x": 12,
                "y": 79,
                "w": 12,
                "h": 5
            },
            "61": {
                "x": 0,
                "y": 84,
                "w": 24,
                "h": 6
            },
            "62": {
                "x": 12,
                "y": 33,
                "w": 12,
                "h": 5
            },
            "63": {
                "x": 12,
                "y": 60,
                "w": 12,
                "h": 5
            },
            "64": {
                "x": 0,
                "y": 12,
                "w": 12,
                "h": 5
            }
        },
        "importedWithCode": false,
        "settings": {}
    }
