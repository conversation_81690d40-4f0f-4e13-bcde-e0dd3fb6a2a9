---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: kyverno-dashboard
  labels:
    scope: global
spec:
  name: "Kyverno Metrics"
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries a = min(kyverno_policy_results_total), by: { k8s.cluster.name } \n| fields k8s.cluster.name",
                "multiple": false,
                "defaultValue": "dev-rh"
            }
        ],
        "tiles": {
            "2": {
                "title": "Cluster Policies",
                "type": "data",
                "query": "timeseries result = count(kyverno_policy_rule_info_total)\n,by: { policy_name }\n,filter: { k8s.cluster.name == $Cluster AND policy_type == \"cluster\" }\n| summarize count()",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "countValue"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Cluster Policies",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "count()",
                        "autoscale": true,
                        "sparklineSettings": {},
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "4": {
                "title": "Validate Rules",
                "type": "data",
                "query": "timeseries rulesCount = count(kyverno_policy_rule_info_total)\n,by: { k8s.cluster.name, rule_type }\n| filter k8s.cluster.name == $Cluster AND rule_type == \"validate\"\n| fields interval, timeframe, arrayFirst(rulesCount)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "interval"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Validate Rules",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "arrayFirst(rulesCount)",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "takeLast(rulesCount)"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "arrayFirst(rulesCount)",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1745834108630
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "5": {
                "title": "Mutate Rules",
                "type": "data",
                "query": "timeseries rulesCount = count(kyverno_policy_rule_info_total)\n,by: { k8s.cluster.name, rule_type }\n| filter k8s.cluster.name == $Cluster AND rule_type == \"mutate\"\n| fields interval, timeframe, arrayFirst(rulesCount)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "interval"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "arrayLast(rulesCount)"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Mutate Rules",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "arrayFirst(rulesCount)",
                        "autoscale": true,
                        "sparklineSettings": {},
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [
                        {
                            "identifier": "arrayFirst(rulesCount)",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1745834137946
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "6": {
                "title": "Generate Rules",
                "type": "data",
                "query": "timeseries rulesCount = count(kyverno_policy_rule_info_total)\n,by: { k8s.cluster.name, rule_type }\n| filter k8s.cluster.name == $Cluster AND rule_type == \"generate\"\n| fields interval, timeframe, arrayFirst(rulesCount)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "interval"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "arrayLast(rulesCount)"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Generate Rules",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "arrayFirst(rulesCount)",
                        "autoscale": true,
                        "sparklineSettings": {},
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [
                        {
                            "identifier": "arrayFirst(rulesCount)",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1745834163852
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "8": {
                "title": "Policies",
                "type": "data",
                "query": "timeseries result = sum(kyverno_policy_rule_info_total)\n,by: { k8s.cluster.name, policy_background_mode, policy_name, policy_type, policy_validation_mode, rule_type, status_ready }\n| filter k8s.cluster.name == $Cluster\n| fields {policy_name, alias: policyName}, {policy_type, alias: policyType}, {policy_validation_mode, alias: enforceMode }, {policy_background_mode, alias: backgroundMode}, {rule_type, alias: ruleType}, {status_ready, alias: isReady}\n| sort ruleType, enforceMode, backgroundMode, isReady",
                "visualization": "table",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "isReady",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "true"
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "false"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "default",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [],
                        "colorThresholdTarget": "value",
                        "enableThresholdInRow": true,
                        "selectedColumnForRowThreshold": "isReady"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "title": "HTTP Requests Latency",
                "type": "data",
                "query": "timeseries {\nlatency = percentile(kyverno_http_requests_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name, http_method, http_url }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, latency, http_method, http_url\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "latency"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "right",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "title": "Average Rule Execution Latency",
                "type": "data",
                "query": "timeseries {\nlatency = percentile(kyverno_policy_execution_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { rule_type }\n,filter: { k8s.cluster.name == $Cluster }\n}\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "latency"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "latency"
                                ],
                                "value": "sparkline",
                                "id": 1745836874334
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "14": {
                "title": "Admission Requests",
                "type": "data",
                "query": "timeseries kyverno_admission_requests_total = sum(kyverno_admission_requests_total, rate: 5m)\n,by: { k8s.cluster.name, request_allowed, request_webhook, resource_kind, resource_namespace, resource_request_operation}\n| filter k8s.cluster.name == $Cluster\n| fields resource_kind, resource_namespace, resource_request_operation, request_webhook, request_allowed",
                "visualization": "table",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "default",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [],
                        "enableThresholdInRow": false
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "HTTP Requests Rate",
                "type": "data",
                "query": "timeseries kyverno_http_requests_total = count(kyverno_http_requests_total)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { http_method, http_url }\n| fieldsAdd requests_total = kyverno_http_requests_total[] / 300\n| fields timeframe, interval, requests_total, http_method, http_url",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests_total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "request_webhook",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "arrayLast(denied_requests)",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "denied_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "16": {
                "title": "Policy Changes Rate",
                "type": "data",
                "query": "timeseries kyverno_policy_changes_total = sum(kyverno_policy_changes_total, rate: 5m)\n,filter: { k8s.cluster.name == $Cluster }\n,by: {policy_name, policy_change_type }\n| fieldsAdd changes = kyverno_policy_changes_total[] / 300\n| fields timeframe, interval, changes, policy_name, policy_change_type",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "changes"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "request_webhook",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "arrayLast(denied_requests)",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "denied_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "changes"
                                ],
                                "value": "sparkline",
                                "id": 1745498084055
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "17": {
                "title": "Client Queries Rate",
                "type": "data",
                "query": "timeseries kyverno_client_queries_total = sum(kyverno_client_queries_total, rate: 5m)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { client_type, operation }\n| fieldsAdd queries = kyverno_client_queries_total[] / 300\n| fields timeframe, interval, queries, client_type, operation",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "queries"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "request_webhook",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "arrayLast(denied_requests)",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "denied_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "18": {
                "title": "Controller Reconciliations Rate",
                "type": "data",
                "query": "timeseries kyverno_controller_reconcile_total = sum(kyverno_controller_reconcile_total, rate: 5m)\n,filter: { k8s.cluster.name == $Cluster }\n,by: { controller_name }\n| fieldsAdd reconciliations = kyverno_controller_reconcile_total[] / 300\n| fields timeframe, interval, reconciliations, controller_name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "reconciliations"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "request_webhook",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "arrayLast(denied_requests)",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "denied_requests"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false,
                            "label": "",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isRelative": true,
                            "isLabelVisible": false
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "19": {
                "title": "Namespaced Policies",
                "type": "data",
                "query": "timeseries result = count(kyverno_policy_rule_info_total)\n,by: { policy_name }\n,filter: { k8s.cluster.name == $Cluster AND policy_type == \"namespaced\" }\n| summarize count()",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "countValue"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Namespaced Policies",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "count()",
                        "autoscale": true,
                        "sparklineSettings": {},
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "20": {
                "title": "Admission Review Results",
                "type": "data",
                "query": "timeseries kyverno_policy_results_total = sum(kyverno_policy_results_total, rate: 5m)\n,by: { rule_result}\n,filter: { k8s.cluster.name == $Cluster AND rule_execution_cause == \"admission_request\" }\n//| fieldsAdd errors_total = (kyverno_policy_results_total[] / 300) * 300\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "kyverno_policy_results_total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "kyverno_policy_results_total"
                                ],
                                "value": "sparkline",
                                "id": 1745835503627
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "21": {
                "title": "Admission Review Results (Per-Policy)",
                "type": "data",
                "query": "timeseries kyverno_policy_results_total = sum(kyverno_policy_results_total, rate: 5m)\n,by: { policy_name, rule_result}\n,filter: { k8s.cluster.name == $Cluster AND rule_execution_cause == \"admission_request\" }\n//| fieldsAdd errors_total = (kyverno_policy_results_total[] / 300) * 300\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "kyverno_policy_results_total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "kyverno_policy_results_total"
                                ],
                                "value": "sparkline",
                                "id": 1745836446271
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "22": {
                "title": "Background Scan Results",
                "type": "data",
                "query": "timeseries kyverno_policy_results_total = sum(kyverno_policy_results_total, rate: 5m)\n,by: { rule_result}\n,filter: { k8s.cluster.name == $Cluster AND rule_execution_cause == \"background_scan\" }\n//| fieldsAdd errors_total = (kyverno_policy_results_total[] / 300) * 300\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "kyverno_policy_results_total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "kyverno_policy_results_total"
                                ],
                                "value": "sparkline",
                                "id": 1745835503627
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "23": {
                "title": "Background Scan Results (Per-Policy)",
                "type": "data",
                "query": "timeseries kyverno_policy_results_total = sum(kyverno_policy_results_total, rate: 5m)\n,by: { policy_name, rule_result }\n,filter: { k8s.cluster.name == $Cluster AND rule_execution_cause == \"background_scan\" }\n//| fieldsAdd errors_total = (kyverno_policy_results_total[] / 300) * 300\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "kyverno_policy_results_total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "kyverno_policy_results_total"
                                ],
                                "value": "sparkline",
                                "id": 1745835503627
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "24": {
                "title": "Policy Failures (Per-Policy)",
                "type": "data",
                "query": "timeseries kyverno_policy_results_total = sum(kyverno_policy_results_total, rate: 5m)\n,by: { policy_name }\n,filter: { k8s.cluster.name == $Cluster AND rule_result == \"fail\" }\n//| fieldsAdd errors_total = (kyverno_policy_results_total[] / 300) * 300\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "kyverno_policy_results_total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "kyverno_policy_results_total"
                                ],
                                "value": "sparkline",
                                "id": 1745836446271
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "25": {
                "title": "Admission Review Latency",
                "type": "data",
                "query": "timeseries {\nlatency = percentile(kyverno_admission_review_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { k8s.cluster.name, request_webhook, request_allowed }\n}\n| filter k8s.cluster.name == $Cluster\n| fields timeframe, interval, latency, request_webhook, request_allowed\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "latency"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "latency"
                                ],
                                "value": "sparkline",
                                "id": 1745493389392
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "26": {
                "title": "Average Policy Execution Latency",
                "type": "data",
                "query": "timeseries {\nlatency = percentile(kyverno_policy_execution_duration_seconds, 95, rollup: avg, rate: 1m)\n,by: { policy_name }\n,filter: { k8s.cluster.name == $Cluster }\n}\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "latency"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kyverno_http_requests_duration_seconds"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "latency"
                                ],
                                "value": "sparkline",
                                "id": 1745836874334
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "27": {
                "title": "Admission Requests",
                "type": "data",
                "query": "timeseries kyverno_admission_requests_total = sum(kyverno_admission_requests_total, rate: 5m)\n,by: { request_webhook, resource_kind, resource_request_operation }\n,filter: { k8s.cluster.name == $Cluster }\n//| fieldsAdd errors_total = (kyverno_policy_results_total[] / 300) * 300\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "start",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "result"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "kyverno_admission_requests_total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "hidden": false,
                            "position": "bottom"
                        },
                        "tooltip": {
                            "variant": "shared"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "kyverno_admission_requests_total"
                                ],
                                "value": "sparkline",
                                "id": 1745839807796
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            }
        },
        "layouts": {
            "2": {
                "x": 4,
                "y": 0,
                "w": 8,
                "h": 4
            },
            "4": {
                "x": 0,
                "y": 4,
                "w": 8,
                "h": 4
            },
            "5": {
                "x": 8,
                "y": 4,
                "w": 8,
                "h": 4
            },
            "6": {
                "x": 16,
                "y": 4,
                "w": 8,
                "h": 4
            },
            "8": {
                "x": 0,
                "y": 8,
                "w": 24,
                "h": 8
            },
            "10": {
                "x": 12,
                "y": 16,
                "w": 12,
                "h": 6
            },
            "12": {
                "x": 12,
                "y": 46,
                "w": 12,
                "h": 6
            },
            "14": {
                "x": 0,
                "y": 58,
                "w": 24,
                "h": 7
            },
            "15": {
                "x": 0,
                "y": 16,
                "w": 12,
                "h": 6
            },
            "16": {
                "x": 12,
                "y": 52,
                "w": 12,
                "h": 6
            },
            "17": {
                "x": 0,
                "y": 22,
                "w": 12,
                "h": 6
            },
            "18": {
                "x": 12,
                "y": 22,
                "w": 12,
                "h": 6
            },
            "19": {
                "x": 12,
                "y": 0,
                "w": 8,
                "h": 4
            },
            "20": {
                "x": 0,
                "y": 28,
                "w": 12,
                "h": 6
            },
            "21": {
                "x": 0,
                "y": 34,
                "w": 12,
                "h": 6
            },
            "22": {
                "x": 12,
                "y": 28,
                "w": 12,
                "h": 6
            },
            "23": {
                "x": 12,
                "y": 34,
                "w": 12,
                "h": 6
            },
            "24": {
                "x": 0,
                "y": 40,
                "w": 12,
                "h": 6
            },
            "25": {
                "x": 0,
                "y": 46,
                "w": 12,
                "h": 6
            },
            "26": {
                "x": 0,
                "y": 52,
                "w": 12,
                "h": 6
            },
            "27": {
                "x": 12,
                "y": 40,
                "w": 12,
                "h": 6
            }
        },
        "importedWithCode": false,
        "settings": {}
    }
