---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: connect-360-dashboard
  labels:
    scope: global
spec:
  name: "Connect 360"
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.kubernetes_cluster\n| summarize clusters = collectDistinct(entity.name)\n| expand clusters\n| sort clusters",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "BifrostInstance",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries \n{\n  filter: { c_service != \"kong-ingress\"},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {c_service}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "BifrostService",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries \n{\n  filter: { matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {service}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "KongService",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries \n{\n  filter: { matchesValue(c_service, \"kong-ingress\")},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {service}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "MeshNamespace",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries total = max(istio_requests_total, rollup:avg, default:0), filter: {matchesValue(reporter, \"destination\")}, interval:1m , by: {destination_workload_namespace, destination_workload, k8s.cluster.name} | fieldsAdd namespace = destination_workload_namespace, deployment = destination_workload | fieldsRemove destination_workload_namespace, destination_workload\n| append  [timeseries total = sum(request_total, rollup:avg, default:0), nonempty:false, filter: {matchesValue(direction, \"inbound\")}, interval:1m , by: {namespace, deployment, k8s.cluster.name}] \n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name,\"-eks\") \n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {namespace}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "MeshDeployment",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries total = max(istio_requests_total, rollup:avg, default:0), filter: {matchesValue(reporter, \"destination\")}, interval:1m , by: {destination_workload_namespace, destination_workload, k8s.cluster.name} | fieldsAdd namespace = destination_workload_namespace, deployment = destination_workload | fieldsRemove destination_workload_namespace, destination_workload\n| append  [timeseries total = sum(request_total, rollup:avg, default:0), nonempty:false, filter: {matchesValue(direction, \"inbound\")}, interval:1m , by: {namespace, deployment, k8s.cluster.name}] \n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name,\"-eks\") \n| filter matchesValue(k8s_cluster_name, $Cluster)\n| filter matchesValue(namespace, $MeshNamespace)\n| summarize count=count(), by: {deployment}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            }
        ],
        "tiles": {
            "0": {
                "title": "Success Rate",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name, c_service }, [\n    timeseries \n    {\n      success = sum(kong_http_requests_total, rollup: avg, default: 0),\n      nonempty: true,\n      filter: { NOT matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n      by: { k8s.cluster.name, c_service },\n      interval: 1m\n    }\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n  ], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 99
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "<",
                                    "label": "",
                                    "value": 99
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1747904703436
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "1": {
                "type": "markdown",
                "content": "**Bifrost** \n--- --- \n "
            },
            "2": {
                "title": "5xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "3": {
                "title": "4xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "4": {
                "type": "markdown",
                "content": "**Kong** \n--- --- \n "
            },
            "5": {
                "title": "Success Rate",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(c_service, \"kong-ingress\")},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name }, [\n    timeseries \n    {\n      success = sum(kong_http_requests_total, rollup: avg, default: 0),\n      nonempty: true,\n      filter: { NOT matchesValue(code, \"5*\") AND matchesValue(c_service, \"kong-ingress\")},\n      by: { k8s.cluster.name },\n      interval: 1m\n    }\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n  ], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsRemove total, success, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 99
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "<",
                                    "label": "",
                                    "value": 99
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1747907454867
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "6": {
                "title": "5xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(c_service, \"kong-ingress\")},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove  k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "7": {
                "title": "4xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(c_service, \"kong-ingress\")},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "8": {
                "type": "markdown",
                "content": "**Service Mesh** \n--- --- \n "
            },
            "9": {
                "title": "Success Rate",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total_istio = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name }, [\n\n  // ── Successful Istio Responses ─────────────────────────────────────────\n  timeseries success_istio = sum(istio_requests_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(response_code, \"2*\") AND\n      matchesValue(reporter, \"destination\") AND\n      matchesValue(destination_workload_namespace, $MeshNamespace) AND\n      matchesValue(destination_workload, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s_cluster_name\n\n], fields: { success_istio }\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| join on: { timeframe, k8s.cluster.name }, [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total_linkerd = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n\n  | join on: { timeframe, k8s.cluster.name }, [\n\n    // ── Inbound Successful Responses ─────────────────────────────────────\n    timeseries success_linkerd = sum(response_total, rollup:avg, default:0),\n      filter: {\n        matchesValue(status_code, \"2*\") AND\n        matchesValue(direction, \"inbound\") AND\n        matchesValue(namespace, $MeshNamespace) AND\n        matchesValue(deployment, $MeshDeployment)\n      },\n      interval: 1m,\n      by: {k8s.cluster.name}\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n\n\n  ], fields: { success_linkerd }\n\n], fields: { success_linkerd, total_linkerd }\n| fieldsAdd successRate= 100*(success_linkerd[]+success_istio[])/(total_linkerd[]+total_istio[])\n| fieldsRemove success_linkerd, success_istio, total_linkerd, total_istio,k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 99
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "<",
                                    "label": "",
                                    "value": 99
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total",
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1748227253955
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "title": "5xx Errors",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries totalIstio = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(response_code, \"5*\") AND\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n|  join on: { timeframe, k8s_cluster_name },  [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries totalLinkerd = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(status_code, \"5*\") AND\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name\n\n], fields: {totalLinkerd}\n| fieldsAdd total = totalLinkerd[] + totalIstio[]\n| fieldsRemove totalLinkerd, totalIstio",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1748227459170
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "11": {
                "title": "4xx Errors",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries totalIstio = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(response_code, \"4*\") AND\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n|  join on: { timeframe, k8s_cluster_name },  [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries totalLinkerd = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(status_code, \"4*\") AND\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name\n\n], fields: {totalLinkerd}\n| fieldsAdd total = totalLinkerd[] + totalIstio[]\n| fieldsRemove totalLinkerd, totalIstio",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "type": "markdown",
                "content": "**Bifrost - By Service** \n--- --- \n "
            },
            "13": {
                "title": "Success Rate",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name, c_service, service}, [\n    timeseries \n    {\n      success = sum(kong_http_requests_total, rollup: avg, default: 0),\n      nonempty: true,\n      filter: { NOT matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n      by: { k8s.cluster.name, c_service, service },\n      interval: 1m\n    }\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n  ], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 99
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "<",
                                    "label": "",
                                    "value": 99
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1747904703436
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "14": {
                "title": "5xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "4xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service},\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "16": {
                "type": "markdown",
                "content": "**Kong - By Service** \n--- --- \n "
            },
            "17": {
                "title": "Success Rate",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(c_service, \"kong-ingress\") AND matchesValue(service, $KongService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name, service }, [\n    timeseries \n    {\n      success = sum(kong_http_requests_total, rollup: avg, default: 0),\n      nonempty: true,\n      filter: { NOT matchesValue(code, \"5*\") AND matchesValue(c_service, \"kong-ingress\")  AND matchesValue(service, $KongService)},\n      by: { k8s.cluster.name, service },\n      interval: 1m\n    }\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n  ], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 99
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "<",
                                    "label": "",
                                    "value": 99
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1747907454867
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "18": {
                "title": "5xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(c_service, \"kong-ingress\") AND matchesValue(service, $KongService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove  k8s.cluster.name, k8s_cluster_name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "19": {
                "title": "4xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(c_service, \"kong-ingress\") AND matchesValue(service, $KongService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service},\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "20": {
                "type": "markdown",
                "content": "**Service Mesh - By Namespace** \n--- --- \n "
            },
            "22": {
                "title": "5xx Errors",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(response_code, \"5*\") AND\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\"), namespace = destination_workload_namespace\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name, destination_workload_namespace, k8s_cluster_name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| append [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(status_code, \"5*\") AND\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name, k8s_cluster_name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "23": {
                "title": "4xx Errors",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(response_code, \"4*\") AND\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\"), namespace = destination_workload_namespace\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name, destination_workload_namespace, k8s_cluster_name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| append [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(status_code, \"4*\") AND\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name, k8s_cluster_name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "25": {
                "title": "Success Rate",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name, destination_workload_namespace }, [\n\n  // ── Successful Istio Responses ─────────────────────────────────────────\n  timeseries success = sum(istio_requests_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(response_code, \"2*\") AND\n      matchesValue(reporter, \"destination\") AND\n      matchesValue(destination_workload_namespace, $MeshNamespace) AND\n      matchesValue(destination_workload, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, destination_workload_namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s_cluster_name\n\n], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsAdd namespace = destination_workload_namespace\n| fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name, destination_workload_namespace\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| append [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n\n  | join on: { timeframe, k8s.cluster.name, namespace}, [\n\n    // ── Inbound Successful Responses ─────────────────────────────────────\n    timeseries success = sum(response_total, rollup:avg, default:0),\n      filter: {\n        matchesValue(status_code, \"2*\") AND\n        matchesValue(direction, \"inbound\") AND\n        matchesValue(namespace, $MeshNamespace) AND\n        matchesValue(deployment, $MeshDeployment)\n      },\n      interval: 1m,\n      by: {k8s.cluster.name, namespace}\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n\n\n  ], fields: { success }\n\n  | fieldsAdd successRate = 100 * (success[] / total[])\n  | fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 99
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "<",
                                    "label": "",
                                    "value": 99
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "istio_requests_total",
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1747913809632
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "26": {
                "title": "Traffic",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "27": {
                "title": "Traffic",
                "type": "data",
                "query": "timeseries \n{\n  filter: {  matchesValue(c_service, \"kong-ingress\")},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove  k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "28": {
                "title": "Traffic",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries totalIstio = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n|  join on: { timeframe, k8s_cluster_name },  [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries totalLinkerd = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name\n\n], fields: {totalLinkerd}\n| fieldsAdd total = totalLinkerd[] + totalIstio[]\n| fieldsRemove totalLinkerd, totalIstio",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1748227459170
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "29": {
                "title": "Traffic",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1748337262058
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "30": {
                "title": "Traffic",
                "type": "data",
                "query": "timeseries \n{\n  filter: {  matchesValue(c_service, \"kong-ingress\") AND matchesValue(service, $KongService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove  k8s.cluster.name, k8s_cluster_name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "31": {
                "title": "Traffic",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    \n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $MeshNamespace) AND\n    matchesValue(destination_workload, $MeshDeployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\"), namespace = destination_workload_namespace\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name, destination_workload_namespace, k8s_cluster_name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| append [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n     \n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $MeshNamespace) AND\n      matchesValue(deployment, $MeshDeployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name, k8s_cluster_name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            }
        },
        "layouts": {
            "0": {
                "x": 0,
                "y": 1,
                "w": 7,
                "h": 5
            },
            "1": {
                "x": 0,
                "y": 0,
                "w": 24,
                "h": 1
            },
            "2": {
                "x": 13,
                "y": 1,
                "w": 6,
                "h": 5
            },
            "3": {
                "x": 19,
                "y": 1,
                "w": 5,
                "h": 5
            },
            "4": {
                "x": 0,
                "y": 6,
                "w": 24,
                "h": 1
            },
            "5": {
                "x": 0,
                "y": 7,
                "w": 7,
                "h": 5
            },
            "6": {
                "x": 13,
                "y": 7,
                "w": 6,
                "h": 5
            },
            "7": {
                "x": 19,
                "y": 7,
                "w": 5,
                "h": 5
            },
            "8": {
                "x": 0,
                "y": 12,
                "w": 24,
                "h": 1
            },
            "9": {
                "x": 0,
                "y": 13,
                "w": 7,
                "h": 5
            },
            "10": {
                "x": 13,
                "y": 13,
                "w": 6,
                "h": 5
            },
            "11": {
                "x": 19,
                "y": 13,
                "w": 5,
                "h": 5
            },
            "12": {
                "x": 0,
                "y": 18,
                "w": 24,
                "h": 1
            },
            "13": {
                "x": 0,
                "y": 19,
                "w": 7,
                "h": 5
            },
            "14": {
                "x": 13,
                "y": 19,
                "w": 6,
                "h": 5
            },
            "15": {
                "x": 19,
                "y": 19,
                "w": 5,
                "h": 5
            },
            "16": {
                "x": 0,
                "y": 24,
                "w": 24,
                "h": 1
            },
            "17": {
                "x": 0,
                "y": 25,
                "w": 7,
                "h": 5
            },
            "18": {
                "x": 13,
                "y": 25,
                "w": 6,
                "h": 5
            },
            "19": {
                "x": 19,
                "y": 25,
                "w": 5,
                "h": 5
            },
            "20": {
                "x": 0,
                "y": 30,
                "w": 24,
                "h": 1
            },
            "22": {
                "x": 13,
                "y": 31,
                "w": 6,
                "h": 5
            },
            "23": {
                "x": 19,
                "y": 31,
                "w": 5,
                "h": 5
            },
            "25": {
                "x": 0,
                "y": 31,
                "w": 7,
                "h": 5
            },
            "26": {
                "x": 7,
                "y": 1,
                "w": 6,
                "h": 5
            },
            "27": {
                "x": 7,
                "y": 7,
                "w": 6,
                "h": 5
            },
            "28": {
                "x": 7,
                "y": 13,
                "w": 6,
                "h": 5
            },
            "29": {
                "x": 7,
                "y": 19,
                "w": 6,
                "h": 5
            },
            "30": {
                "x": 7,
                "y": 25,
                "w": 6,
                "h": 5
            },
            "31": {
                "x": 7,
                "y": 31,
                "w": 6,
                "h": 5
            }
        },
        "importedWithCode": false,
        "settings": {}
    }