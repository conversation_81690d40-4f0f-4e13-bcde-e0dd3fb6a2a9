---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: flagger-dashboard
  labels:
    scope: global
spec:
  name: "Flagger Metrics"
  json: |
    {
        "version": 17,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries a = min(flagger_canary_status), by: { k8s.cluster.name } \n| fields k8s.cluster.name",
                "multiple": false
            },
            {
                "key": "Namespace",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries a = count(dt.kubernetes.workloads)\n,by: { k8s.namespace.name, k8s.cluster.name }\n| filter contains(k8s.cluster.name, $Cluster)\n| fields k8s.namespace.name",
                "multiple": false
            },
            {
                "key": "Workload",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries a = count(dt.kubernetes.workloads)\n,by: { k8s.workload.kind, dt.entity.cloud_application_namespace, k8s.namespace.name, k8s.cluster.name }\n| fieldsAdd workloadName = entityName(dt.entity.cloud_application_namespace)\n| filter contains(k8s.cluster.name, $Cluster) AND $Namespace == k8s.namespace.name AND (k8s.workload.kind == \"deployment\" or k8s.workload.kind == \"statefulset\")\n| fields workloadName",
                "multiple": false,
                "defaultValue": "activities-notification-service"
            }
        ],
        "tiles": {
            "0": {
                "title": "Last Result Canary Status",
                "type": "data",
                "query": "timeseries flagger_canary_status = avg(flagger_canary_status) ,\nfilter: { $Cluster == k8s.cluster.name AND $Namespace == namespace AND $Workload == name}\n| fieldsAdd canary_status = toLong(arrayFirst(flagger_canary_status))\n| fieldsAdd canaryStatus = if(canary_status == 0, \"Running\")\n| fieldsAdd canaryStatus = if (isNull(canaryStatus), if(canary_status == 1, \"Successful\", else: \"Failed\"))\n",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "canaryStatus",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": "Successful"
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-loglevel-severe-default, #d56b1a)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": "Running"
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": "Failed"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "xAxisScaling": "auto",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "never",
                        "categoricalBarChartSettings": {
                            "layout": "vertical",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "canaryStatus",
                            "valueAxisLabel": "canary_status",
                            "tooltipVariant": "single",
                            "isCategoryLabelVisible": false,
                            "min": null,
                            "isValueLabelVisible": false,
                            "categoryAxis": [
                                "canaryStatus"
                            ],
                            "valueAxis": [
                                "canary_status"
                            ]
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "canary_status"
                        ],
                        "leftYAxisSettings": {
                            "scale": "log",
                            "isLabelVisible": false
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "single"
                        },
                        "seriesOverrides": [],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "flagger_canary_status"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "Last Result",
                        "prefixIcon": "",
                        "recordField": "canaryStatus",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "flagger_canary_status",
                            "variant": "line",
                            "isVisible": false,
                            "lineType": "linear",
                            "showTicks": false,
                            "color": {
                                "Default": "var(--dt-colors-charts-categorical-color-07-default, #438fb1)"
                            }
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "upward": {
                                "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                            },
                            "isVisible": false,
                            "isRelative": true,
                            "isLabelVisible": false,
                            "label": "",
                            "trendField": "canary_status"
                        },
                        "colorThresholdTarget": "background",
                        "isIconVisible": false
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "flagger_canary_status"
                                ],
                                "value": "sparkline",
                                "id": 1742218036487
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "canary_status"
                        },
                        "displayedFields": [
                            "canaryStatus"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "canary_status",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "canaryStatus"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "canary_status"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "canary_status"
                    },
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "3": {
                "title": "Primary: Flagger Weighting",
                "type": "data",
                "query": "timeseries flagger_weight = sum(flagger_canary_weight), by:{ k8s.cluster.name, workload, namespace }\n| filter $Cluster == k8s.cluster.name AND $Namespace == namespace AND concat($Workload, \"-\", \"primary\") == workload\n| fieldsAdd flagger_weight = arrayFirst(flagger_weight)\n//| fieldsAdd dt.kubernetes.pods = \"max(dt.kubernetes.pods)\"\n//| fieldsAdd value.A = arrayFirst(`max(dt.kubernetes.pods)`)",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "never",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxis": [
                                "k8s.cluster.name",
                                "workload",
                                "namespace"
                            ],
                            "categoryAxisLabel": "k8s.cluster.name,workload,namespace",
                            "valueAxis": [
                                "flagger_weight"
                            ],
                            "valueAxisLabel": "flagger_weight",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "flagger_weight"
                            ]
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster.name",
                            "namespace"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "Kubernetes: Workload - desired pod count",
                            "scale": "linear",
                            "min": "auto",
                            "max": "auto"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "single",
                            "seriesDisplayMode": "multi-line"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "activity-service-primary"
                                ],
                                "override": {
                                    "geometry": "bar"
                                }
                            }
                        ]
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "max(dt.kubernetes.workload.pods_desired)"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "flagger_weight"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "workload",
                            "namespace"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "flagger_weight",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name",
                            "workload",
                            "namespace"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "flagger_weight",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741702282320
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "4": {
                "title": "Primary: Success Rate",
                "type": "data",
                "query": "timeseries {\nreqestCount=sum(dt.service.request.count, rate: 1s),\nfailureCount=sum(dt.service.request.failure_count, rate: 1s)\n},\nby: { k8s.workload.name, k8s.namespace.name, k8s.cluster.name }\n,filter: { \n contains(k8s.cluster.name, $Cluster) \n AND $Namespace == k8s.namespace.name \n AND k8s.workload.name == concat($Workload, \"-primary\")\n}, interval: 1m\n| fieldsAdd successRate = (1 - failureCount[] / reqestCount[]) * 100\n| fields timeframe, interval, successRate, k8s.workload.name",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "never",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "k8s.cluster.name,namespace,name,metric",
                            "valueAxisLabel": "request_duration",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.namespace.name",
                            "interval",
                            "pod_count",
                            "request_duration"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "Kubernetes: Workload - desired pod count",
                            "scale": "linear",
                            "min": "auto",
                            "max": null
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "single",
                            "seriesDisplayMode": "single-line"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "activity-service-primary"
                                ],
                                "override": {
                                    "geometry": "bar"
                                }
                            }
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "max(dt.kubernetes.workload.pods_desired)"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1744803182799
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "request_duration"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace",
                            "name",
                            "metric"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "request_duration",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "request_duration",
                            "unitCategory": "time",
                            "baseUnit": "second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741779749026
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "7": {
                "title": "Canary: Flagger Weighting",
                "type": "data",
                "query": "timeseries flagger_weight = sum(flagger_canary_weight), by:{ k8s.cluster.name, workload, namespace }\n| filter $Cluster == k8s.cluster.name AND $Namespace == namespace AND $Workload == workload\n| fieldsAdd flagger_weight = arrayFirst(flagger_weight)\n//| fieldsAdd dt.kubernetes.pods = \"max(dt.kubernetes.pods)\"\n//| fieldsAdd value.A = arrayFirst(`max(dt.kubernetes.pods)`)",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "never",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxis": [
                                "k8s.cluster.name",
                                "workload",
                                "namespace"
                            ],
                            "categoryAxisLabel": "k8s.cluster.name,workload,namespace",
                            "valueAxis": [
                                "flagger_weight"
                            ],
                            "valueAxisLabel": "flagger_weight",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "flagger_weight"
                            ]
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster.name",
                            "namespace",
                            "interval",
                            "flagger_weight"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "Kubernetes: Workload - desired pod count",
                            "scale": "linear",
                            "min": "auto",
                            "max": "auto"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "single",
                            "seriesDisplayMode": "multi-line"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "activity-service-primary"
                                ],
                                "override": {
                                    "geometry": "bar"
                                }
                            }
                        ]
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "max(dt.kubernetes.workload.pods_desired)"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "flagger_weight"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "workload",
                            "namespace"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "flagger_weight",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name",
                            "workload",
                            "namespace"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "flagger_weight",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741702282320
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "title": "Canary: Success Rate",
                "type": "data",
                "query": "timeseries {\nreqestCount=sum(dt.service.request.count, rate: 1s),\nfailureCount=sum(dt.service.request.failure_count, rate: 1s)\n},\nby: { k8s.workload.name, k8s.namespace.name, k8s.cluster.name }\n,filter: { \n contains(k8s.cluster.name, $Cluster) \n AND $Namespace == k8s.namespace.name \n AND k8s.workload.name == $Workload\n}, interval: 1m\n| fieldsAdd successRate = (1 - failureCount[] / reqestCount[]) * 100\n| fields timeframe, interval, successRate, k8s.workload.name",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "never",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "k8s.cluster.name,namespace,name,metric",
                            "valueAxisLabel": "request_duration",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.namespace.name",
                            "interval",
                            "pod_count",
                            "request_duration"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "Kubernetes: Workload - desired pod count",
                            "scale": "linear",
                            "min": "auto",
                            "max": null
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "variant": "single",
                            "seriesDisplayMode": "single-line"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "activity-service-primary"
                                ],
                                "override": {
                                    "geometry": "bar"
                                }
                            }
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "max(dt.kubernetes.workload.pods_desired)"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1744803182799
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "request_duration"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace",
                            "name",
                            "metric"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "request_duration",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "request_duration",
                            "unitCategory": "time",
                            "baseUnit": "second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741779749026
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "11": {
                "title": "Primary: Healthy Pod Replicas",
                "type": "data",
                "query": "timeseries podsCount = count(dt.kubernetes.pods)\n,by: { k8s.workload.name, k8s.namespace.name, k8s.cluster.name, pod_status, pod_phase, pod_condition, k8s.pod.name }\n,filter: { \n contains(k8s.cluster.name, $Cluster) \n AND $Namespace == k8s.namespace.name \n AND k8s.workload.name == concat($Workload, \"-primary\")\n AND pod_status == \"Running\"\n AND pod_phase == \"Running\"\n AND pod_condition == \"Ready\"\n}, interval: 1m\n| fields timeframe, interval, podsCount",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "never",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "k8s.cluster.name,namespace,name,metric",
                            "valueAxisLabel": "request_duration",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.namespace.name",
                            "interval",
                            "pod_count",
                            "request_duration"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "Kubernetes: Workload - desired pod count",
                            "scale": "linear",
                            "min": "auto",
                            "max": null
                        },
                        "legend": {
                            "hidden": true
                        },
                        "tooltip": {
                            "variant": "shared",
                            "seriesDisplayMode": "single-line"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "activity-service-primary"
                                ],
                                "override": {
                                    "geometry": "bar"
                                }
                            }
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "podsCount"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "max(dt.kubernetes.workload.pods_desired)"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "podsCount"
                                ],
                                "value": "sparkline",
                                "id": 1744804220591
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "request_duration"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace",
                            "name",
                            "metric"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "request_duration",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "request_duration",
                            "unitCategory": "time",
                            "baseUnit": "second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741779749026
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "title": "Canary: Healthy Pod Replicas",
                "type": "data",
                "query": "timeseries podsCount = count(dt.kubernetes.pods)\n,by: { k8s.workload.name, k8s.namespace.name, k8s.cluster.name, pod_status, pod_phase, pod_condition, k8s.pod.name }\n,filter: { \n contains(k8s.cluster.name, $Cluster) \n AND $Namespace == k8s.namespace.name \n AND k8s.workload.name == $Workload\n AND pod_status == \"Running\"\n AND pod_phase == \"Running\"\n AND pod_condition == \"Ready\"\n}, interval: 1m\n| fields timeframe, interval, podsCount",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "never",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "categoryAxisLabel": "k8s.cluster.name,namespace,name,metric",
                            "valueAxisLabel": "request_duration",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.namespace.name",
                            "interval",
                            "pod_count",
                            "request_duration"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "Kubernetes: Workload - desired pod count",
                            "scale": "linear",
                            "min": "auto",
                            "max": null
                        },
                        "legend": {
                            "hidden": true
                        },
                        "tooltip": {
                            "variant": "shared",
                            "seriesDisplayMode": "single-line"
                        },
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "activity-service-primary"
                                ],
                                "override": {
                                    "geometry": "bar"
                                }
                            }
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "podsCount"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "sparklineSettings": {
                            "record": "max(dt.kubernetes.workload.pods_desired)"
                        },
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "podsCount"
                                ],
                                "value": "sparkline",
                                "id": 1744804220591
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "request_duration"
                        },
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace",
                            "name",
                            "metric"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "legend": "auto",
                        "yAxis": {
                            "label": "Frequency",
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "request_duration",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "k8s.cluster.name",
                            "namespace"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": false,
                        "label": "interval"
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "request_duration",
                            "unitCategory": "time",
                            "baseUnit": "second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1741779749026
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            }
        },
        "layouts": {
            "0": {
                "x": 0,
                "y": 0,
                "w": 24,
                "h": 4
            },
            "3": {
                "x": 0,
                "y": 4,
                "w": 6,
                "h": 5
            },
            "4": {
                "x": 0,
                "y": 9,
                "w": 12,
                "h": 6
            },
            "7": {
                "x": 18,
                "y": 4,
                "w": 6,
                "h": 5
            },
            "10": {
                "x": 12,
                "y": 9,
                "w": 12,
                "h": 6
            },
            "11": {
                "x": 6,
                "y": 4,
                "w": 6,
                "h": 5
            },
            "12": {
                "x": 12,
                "y": 4,
                "w": 6,
                "h": 5
            }
        },
        "importedWithCode": false,
        "settings": {}
    }