---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: load-testing-dashboard
  labels:
    scope: global
spec:
  name: "Load Testing Metrics"
  json: |
    {
    "version": 18,
    "variables": [
        {
        "key": "job_name",
        "visible": true,
        "type": "query",
        "version": 1,
        "editable": true,
        "input": "timeseries count(k6_iterations),by:{job_name}\n| fields job_name",
        "multiple": false
        }
    ],
    "tiles": {
        "0": {
        "title": "",
        "type": "data",
        "query": "timeseries k6_http_reqs=sum(k6_http_reqs), filter: { matchesValue(job_name, $job_name) }\n| fieldsAdd Total = arraySum(`k6_http_reqs`)\n| fields Total",
        "visualization": "gauge",
        "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval",
                "Total"
            ],
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_http_reqs"
            }
            },
            "singleValue": {
            "showLabel": true,
            "label": "Total reqs",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "recordField": "Total",
            "autoscale": true,
            "sparklineSettings": {},
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true
            },
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
            "value": "Total"
            },
            "label": {
            "showLabel": true,
            "label": "HTTP requests"
            }
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "1": {
        "title": "",
        "type": "data",
        "query": "timeseries max_vus=max(k6_vus_max), filter: { matchesValue(job_name, $job_name) }\n| fieldsAdd max = arrayMax(max_vus)\n| fields max",
        "visualization": "singleValue",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval",
                "Total"
            ],
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_http_reqs"
            }
            },
            "singleValue": {
            "showLabel": true,
            "label": "Max VUs",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "recordField": "max",
            "autoscale": true,
            "sparklineSettings": {
                "record": "max_vus"
            },
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true,
                "isRelative": false
            },
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [
                {
                "valueAxis": "interval",
                "rangeAxis": ""
                }
            ],
            "variant": "single",
            "truncationMode": "middle",
            "displayedFields": [
                "interval"
            ]
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "unitsOverrides": [
            {
                "identifier": "max",
                "unitCategory": "unspecified",
                "baseUnit": "none",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745484733612
            }
            ],
            "dataMapping": {
            "value": "max"
            },
            "label": {
            "showLabel": true,
            "label": "max"
            }
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "2": {
        "title": "",
        "type": "data",
        "query": "timeseries k6_iterations=sum(k6_iterations), filter: { matchesValue(job_name, $job_name) }\n| fieldsAdd Total = arraySum(`k6_iterations`)\n| fields Total",
        "visualization": "singleValue",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle"
            },
            "singleValue": {
            "showLabel": true,
            "label": "Journeys Iterations number",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "recordField": "Total",
            "autoscale": true,
            "sparklineSettings": {},
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true
            },
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            }
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "3": {
        "title": "",
        "type": "data",
        "query": "timeseries occurred=sum(k6_http_req_failed.occurred), filter: { matchesValue(job_name, $job_name) }\n| fieldsAdd Total = arraySum(`occurred`)\n| fields Total",
        "visualization": "singleValue",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval"
            ],
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_http_req_failed.occurred"
            }
            },
            "singleValue": {
            "showLabel": true,
            "label": "HTTP requests failures",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "recordField": "Total",
            "autoscale": true,
            "sparklineSettings": {
                "record": "k6_http_req_failed"
            },
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true,
                "isRelative": false
            },
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "unitsOverrides": [
            {
                "identifier": "Total",
                "unitCategory": "unspecified",
                "baseUnit": "none",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745497732669
            }
            ]
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "4": {
        "title": "Iteration Duration",
        "type": "data",
        "query": "timeseries k6_iteration_duration=avg(k6_iteration_duration)\n| fieldsAdd Total = arrayAvg(`k6_iteration_duration`)\n| fields Total",
        "visualization": "singleValue",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval"
            ],
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_iteration_duration"
            }
            },
            "singleValue": {
            "showLabel": true,
            "label": "Iteration Duration",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "recordField": "Total",
            "autoscale": true,
            "sparklineSettings": {},
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true
            },
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "unitsOverrides": [
            {
                "identifier": "Total",
                "unitCategory": "unspecified",
                "baseUnit": "none",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "ms",
                "delimiter": false,
                "added": 1745498682190
            }
            ]
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "6": {
        "title": "HTTP requests duration",
        "type": "data",
        "query": "timeseries k6_http_req_duration= percentile(k6_http_req_duration, 90, rollup:avg)\n| fieldsAdd total = arrayLast(k6_http_req_duration)\n| fields total",
        "visualization": "singleValue",
        "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval"
            ],
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_http_req_duration"
            }
            },
            "singleValue": {
            "showLabel": true,
            "label": "Total",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "recordField": "total",
            "autoscale": true,
            "sparklineSettings": {
                "record": "k6_http_req_duration"
            },
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true
            },
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "autoSelectVisualization": false,
            "recordView": {
            "fieldsWidth": 200
            },
            "unitsOverrides": [
            {
                "identifier": "total",
                "unitCategory": "unspecified",
                "baseUnit": "none",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "ms",
                "delimiter": false,
                "added": 1745499767967
            }
            ]
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "7": {
        "title": "Data Received",
        "type": "data",
        "query": "timeseries  k6_data_received=sum(k6_data_received),filter: { matchesValue(job_name, $job_name) }\n//| fieldsAdd Total = arraySum(`k6_data_received`)\n//| fields Total",
        "visualization": "lineChart",
        "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                "k6_data_received"
                ]
            },
            "xAxisScaling": "auto",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval"
            ],
            "leftYAxisSettings": {}
            },
            "singleValue": {
            "showLabel": true,
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "autoscale": true,
            "alignment": "center",
            "colorThresholdTarget": "value",
            "trend": {
                "trendType": "auto",
                "isVisible": true
            },
            "sparklineSettings": {
                "record": "k6_data_received"
            }
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": [
                {
                "fields": [
                    "k6_data_received"
                ],
                "value": "sparkline",
                "id": 1745502587335
                }
            ]
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [
                {
                "valueAxis": "interval",
                "rangeAxis": ""
                }
            ],
            "variant": "single",
            "truncationMode": "middle",
            "displayedFields": [
                "interval"
            ]
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "autoSelectVisualization": false
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "8": {
        "title": "Data sent",
        "type": "data",
        "query": "timeseries k6_data_sent=sum(k6_data_sent),filter: { matchesValue(job_name, $job_name) }",
        "visualization": "lineChart",
        "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "auto",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval"
            ],
            "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                "k6_data_sent"
                ]
            },
            "leftYAxisSettings": {}
            },
            "singleValue": {
            "showLabel": true,
            "label": "",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "autoscale": true,
            "alignment": "center",
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": [
                {
                "fields": [
                    "k6_data_sent"
                ],
                "value": "sparkline",
                "id": 1745502928116
                }
            ]
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "autoSelectVisualization": true
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "9": {
        "title": "Checks List",
        "type": "data",
        "query": "timeseries total=sum(k6_checks.total),by:{check}, filter: { matchesValue(job_name, $job_name) }",
        "visualization": "categoricalBarChart",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "check",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single",
                "categoryAxis": [
                "check"
                ],
                "valueAxis": [
                "interval"
                ]
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval"
            ],
            "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                "total"
                ]
            },
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_checks.total"
            }
            },
            "singleValue": {
            "showLabel": true,
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "autoscale": true,
            "sparklineSettings": {
                "record": "total"
            },
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true
            },
            "colorThresholdTarget": "value",
            "recordField": "check",
            "label": "check"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [
                [
                "timeframe"
                ],
                [
                "interval"
                ]
            ],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": [
                {
                "fields": [
                    "total"
                ],
                "value": "sparkline",
                "id": 1745585687833
                }
            ]
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {
                "value": "check"
            },
            "displayedFields": [
                "check"
            ],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [
                {
                "valueAxis": "interval",
                "rangeAxis": ""
                }
            ],
            "variant": "single",
            "truncationMode": "middle",
            "displayedFields": [
                "interval"
            ]
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            }
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "13": {
        "title": "RPS",
        "type": "data",
        "query": "timeseries { total = sum(k6_http_reqs, rate:1s) }",
        "visualization": "barChart",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxis": [
                "interval"
                ],
                "valueAxis": [
                "interval"
                ],
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval",
                "count"
            ],
            "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                "total"
                ]
            },
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_http_reqs"
            }
            },
            "singleValue": {
            "showLabel": true,
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "autoscale": true,
            "sparklineSettings": {
                "record": "total"
            },
            "alignment": "center",
            "trend": {
                "trendType": "auto",
                "isVisible": true
            },
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": [
                {
                "fields": [
                    "total"
                ],
                "value": "sparkline",
                "id": 1745846331542
                }
            ]
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [
                {
                "valueAxis": "interval",
                "rangeAxis": ""
                }
            ],
            "variant": "single",
            "truncationMode": "middle",
            "displayedFields": [
                "interval"
            ]
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            }
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "14": {
        "title": "Requests by URL http_req_waiting",
        "type": "data",
        "query": "timeseries {count = avg(k6_http_reqs, scalar: true) ,latency_avg = avg(k6_http_req_waiting,  scalar: true), \n    p50 = percentile(k6_http_req_waiting, 50, rollup:avg, scalar: true),\n    p75 = percentile(k6_http_req_waiting, 75, rollup:avg, scalar: true),\n    p90 = percentile(k6_http_req_waiting, 90, rollup:avg, scalar: true),\n    p95 = percentile(k6_http_req_waiting, 95, rollup:avg, scalar: true),\n    p99 = percentile(k6_http_req_waiting, 99, rollup:avg, scalar: true)\n  }, union: true,  by: { name, method, status } , filter: { matchesValue(job_name, $job_name) }",
        "visualization": "table",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval"
            ],
            "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                "interval"
                ]
            },
            "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "k6_http_req_waiting"
            }
            },
            "singleValue": {
            "showLabel": true,
            "label": "",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "autoscale": true,
            "alignment": "center",
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [
                [
                "timeframe"
                ],
                [
                "interval"
                ]
            ],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            }
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        },
        "15": {
        "title": "Requests by URL http_req_duration",
        "type": "data",
        "query": "timeseries {count = avg(k6_http_reqs, scalar: true) ,latency_avg = avg(k6_http_req_duration,  scalar: true), \n    p50 = percentile(k6_http_req_duration, 50, rollup:avg, scalar: true),\n    p75 = percentile(k6_http_req_duration, 75, rollup:avg, scalar: true),\n    p90 = percentile(k6_http_req_duration, 90, rollup:avg, scalar: true),\n    p95 = percentile(k6_http_req_duration, 95, rollup:avg, scalar: true),\n    p99 = percentile(k6_http_req_duration, 99, rollup:avg, scalar: true)\n  }, union: true,  by: { name, method, status } , filter: { matchesValue(job_name, $job_name) }",
        "visualization": "table",
        "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
            "gapPolicy": "gap",
            "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
            },
            "categoryOverrides": {},
            "curve": "linear",
            "pointsDisplay": "auto",
            "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
            },
            "colorPalette": "categorical",
            "valueRepresentation": "absolute",
            "truncationMode": "middle",
            "xAxisScaling": "analyzedTimeframe",
            "xAxisLabel": "timeframe",
            "xAxisIsLabelVisible": false,
            "hiddenLegendFields": [
                "interval",
                "count",
                "latency_avg",
                "p50",
                "p75",
                "p90",
                "p95",
                "p99"
            ],
            "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                "interval"
                ]
            },
            "leftYAxisSettings": {}
            },
            "singleValue": {
            "showLabel": true,
            "label": "",
            "prefixIcon": "AnalyticsIcon",
            "isIconVisible": false,
            "autoscale": true,
            "alignment": "center",
            "colorThresholdTarget": "value"
            },
            "table": {
            "rowDensity": "condensed",
            "enableSparklines": false,
            "hiddenColumns": [
                [
                "timeframe"
                ],
                [
                "interval"
                ]
            ],
            "linewrapEnabled": false,
            "lineWrapIds": [],
            "monospacedFontEnabled": false,
            "monospacedFontColumns": [],
            "columnWidths": {},
            "columnTypeOverrides": []
            },
            "honeycomb": {
            "shape": "hexagon",
            "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
            },
            "dataMappings": {},
            "displayedFields": [],
            "truncationMode": "middle",
            "colorMode": "color-palette",
            "colorPalette": "categorical"
            },
            "histogram": {
            "legend": {
                "position": "auto"
            },
            "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
            },
            "colorPalette": "categorical",
            "dataMappings": [],
            "variant": "single",
            "truncationMode": "middle"
            },
            "valueBoundaries": {
            "min": "auto",
            "max": "auto"
            },
            "unitsOverrides": [
            {
                "identifier": "count",
                "unitCategory": "unspecified",
                "baseUnit": "none",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745846729042
            }
            ]
        },
        "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
        },
        "davis": {
            "enabled": false,
            "davisVisualization": {
            "isAvailable": true
            }
        }
        }
    },
    "layouts": {
        "0": {
        "x": 0,
        "y": 0,
        "w": 8,
        "h": 6
        },
        "1": {
        "x": 8,
        "y": 0,
        "w": 8,
        "h": 6
        },
        "2": {
        "x": 16,
        "y": 0,
        "w": 8,
        "h": 6
        },
        "3": {
        "x": 0,
        "y": 6,
        "w": 8,
        "h": 3
        },
        "4": {
        "x": 8,
        "y": 6,
        "w": 8,
        "h": 3
        },
        "6": {
        "x": 16,
        "y": 6,
        "w": 8,
        "h": 3
        },
        "7": {
        "x": 8,
        "y": 9,
        "w": 8,
        "h": 6
        },
        "8": {
        "x": 16,
        "y": 9,
        "w": 8,
        "h": 6
        },
        "9": {
        "x": 0,
        "y": 15,
        "w": 24,
        "h": 6
        },
        "13": {
        "x": 0,
        "y": 9,
        "w": 8,
        "h": 6
        },
        "14": {
        "x": 0,
        "y": 21,
        "w": 24,
        "h": 5
        },
        "15": {
        "x": 0,
        "y": 26,
        "w": 24,
        "h": 6
        }
    },
    "importedWithCode": false,
    "settings": {}
    }