---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: aws-rds-anomaly
  labels:
    scope: production
spec:
  anomalies:
    staticThreshold:
      CPUUtilization:
        title: "[global] RDS High CPU Usage"
        description: "CPU utilization alarm for utilization greater than 80%"
        threshold: 80
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.cpu.usage), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "High CPU Usage"
      FreeStorageSpace:
        title: "[global] RDS Low Free Storage Space"
        description: "Free storage space is below 5% threshold"
        threshold: 5
        alertCondition: "BELOW"
        query: |
          timeseries avg(dt.cloud.aws.rds.free), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "Low Free Storage Space"
      FreeableMemory:
        title: "[global] RDS Low Freeable Memory"
        description: "Freeable memory alarm for lower memory (1GB threshold)"
        threshold: 1073741824
        alertCondition: "BELOW"
        query: |
          timeseries avg(dt.cloud.aws.rds.memory.freeable), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "Low Freeable Memory"
      ReadLatency:
        title: "[global] RDS High Read Latency"
        description: "Average read latency is greater than 100 milliseconds"
        threshold: 0.1
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.latency.read), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read Latency"
      WriteLatency:
        title: "[global] RDS High Write Latency"
        description: "Average write latency is greater than 100 milliseconds"
        threshold: 0.1
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.latency.write), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write Latency"
      SwapUsage:
        title: "[global] RDS High Swap Usage"
        description: "RDS swap usage is higher than normal (100MB threshold)"
        threshold: 104857600
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.memory.swap), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Swap Usage"
      DatabaseRestarts:
        title: "[global] RDS Database Restarts"
        description: "Database restart events detected"
        threshold: 0
        alertCondition: "ABOVE"
        query: |
          timeseries sum(dt.cloud.aws.rds.restarts), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "AVAILABILITY_EVENT"
        eventName: "Database Restart"
      ReplicaLag:
        title: "[global] RDS High Replica Lag"
        description: "Replica lag alarm if maximum lag over 2 minutes"
        threshold: 120
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.replicaLagByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Replica Lag"
      DiskQueueDepth:
        title: "[global] RDS High Disk Queue Depth"
        description: "Queue depth alarm if count is over 11 for extended period"
        threshold: 11
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.diskQueueDepthByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Disk Queue Depth"
      DeadLocks:
        title: "[global] RDS Deadlocks Detected"
        description: "Alert when deadlocks are detected in the database"
        threshold: 1
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.deadlocksByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "ERROR_EVENT"
        eventName: "Deadlocks Detected"
    adaptiveThreshold:
      DatabaseConnections:
        title: "[global] RDS High Database Connections"
        description: "Max DB connections alarm for 90% of maximum connection value"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.connections), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "High Database Connections"
      ReadIOPS:
        title: "[global] RDS High Read IOPS"
        description: "Average read IOPS is greater than based on instance capacity"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.ops.read), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read IOPS"
      WriteIOPS:
        title: "[global] RDS High Write IOPS"
        description: "Average write IOPS is greater than based on instance capacity"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.ops.write), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write IOPS"
      ReadThroughput:
        title: "[global] RDS High Read Throughput"
        description: "Read throughput is higher than normal baseline"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.throughput.read), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read Throughput"
      WriteThroughput:
        title: "[global] RDS High Write Throughput"
        description: "Write throughput is higher than normal baseline"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.throughput.write), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write Throughput"
      NetworkReceived:
        title: "[global] RDS High Network Received"
        description: "Network received throughput is higher than normal"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.net.rx), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Network Received"
      NetworkTransmitted:
        title: "[global] RDS High Network Transmitted"
        description: "Network transmitted throughput is higher than normal"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(dt.cloud.aws.rds.net.tx), by:{dt.entity.relational_database_service}
        entityIdProperty: "dt.entity.relational_database_service"
        entityNameProperty: "dt.entity.relational_database_service"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Network Transmitted"
      LVMReadIOPS:
        title: "[global] RDS LVM Read IOPS"
        description: "Average LVM read IOPS is greater than based on storage capacity"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.lvmReadIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High LVM Read IOPS"
      LVMWriteIOPS:
        title: "[global] RDS LVM Write IOPS"
        description: "Average LVM write IOPS is greater than based on storage capacity"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.lvmWriteIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High LVM Write IOPS"
      RowLockTime:
        title: "[global] RDS High Row Lock Time"
        description: "Row lock time is higher than normal baseline (MySQL/MariaDB only)"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.rowLockTimeByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Row Lock Time"
      MaximumUsedTransactionIDs:
        title: "[global] RDS High Transaction ID Usage"
        description: "Maximum used transaction IDs approaching limit (PostgreSQL only)"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.maximumUsedTransactionIDsByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Transaction ID Usage"
