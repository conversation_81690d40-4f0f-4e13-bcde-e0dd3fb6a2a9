---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: aws-rds-anomaly
  labels:
    scope: production
spec:
  anomalies:
    staticThreshold:
      CPUUtilization:
        title: "[global] RDS High CPU Usage"
        description: "Alert on RDS High CPU Usage"
        threshold: 80
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.cpuUtilizationByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "High CPU Usage"
      FreeStorageSpace:
        title: "[global] RDS Low Free Storage Space"
        description: "Alert on RDS Low Free Storage Space"
        threshold: 10
        alertCondition: "BELOW"
        query: |
          timeseries avg(cloud.aws.rds.freeStorageSpaceByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "Low Free Storage Space"
      FreeableMemory:
        title: "[global] RDS Low Freeable Memory"
        description: "Alert on RDS Low Freeable Memory"
        threshold: **********
        alertCondition: "BELOW"
        query: |
          timeseries avg(cloud.aws.rds.freeableMemoryByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "Low Freeable Memory"
      ReplicaLag:
        title: "[global] RDS High Replica Lag"
        description: "Alert on RDS High Replica Lag"
        threshold: 300
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.replicaLagByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Replica Lag"
      DeadLocks:
        title: "[global] RDS Deadlocks"
        description: "Alert on RDS Deadlocks"
        threshold: 0
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.deadlocksByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "ERROR_EVENT"
        eventName: "Deadlocks Detected"
    adaptiveThreshold:
      DatabaseConnections:
        title: "[global] RDS High Database Connections"
        description: "Alert on RDS High Database Connections"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.databaseConnectionsByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "High Database Connections"
      DiskQueueDepth:
        title: "[global] RDS High Disk Queue Depth"
        description: "Alert on RDS High Disk Queue Depth"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.diskQueueDepthByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Disk Queue Depth"
      ReadLatency:
        title: "[global] RDS High Read Latency"
        description: "Alert on RDS High Read Latency"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.readLatencyByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read Latency"
      WriteLatency:
        title: "[global] RDS High Write Latency"
        description: "Alert on RDS High Write Latency"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.writeLatencyByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write Latency"
      ReadIOPS:
        title: "[global] RDS High Read IOPS"
        description: "Alert on RDS High Read IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.readIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read IOPS"
      WriteIOPS:
        title: "[global] RDS High Write IOPS"
        description: "Alert on RDS High Write IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.writeIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write IOPS"
      RowLockTime:
        title: "[global] RDS Row Lock Time"
        description: "Alert on RDS Row Lock Time"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.rowLockTimeByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Row Lock Time"
      MaximumUsedTransactionIDs:
        title: "[global] RDS Maximum Used Transaction IDs"
        description: "Alert on RDS Maximum Used Transaction IDs"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.maximumUsedTransactionIDsByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Maximum Used Transaction IDs"
      LVMReadIOPS:
        title: "[global] RDS LVM Read IOPS"
        description: "Alert on RDS LVM Read IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.lvmReadIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High LVM Read IOPS"
      LVMWriteIOPS:
        title: "[global] RDS LVM Write IOPS"
        description: "Alert on RDS LVM Write IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.lvmWriteIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High LVM Write IOPS"
