---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: dev-rh-k8s
  labels:
    scope: non-production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: dev-shared-services
  labels:
    scope: non-production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: dev-sre
  labels:
    scope: non-production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: staging-pay
  labels:
    scope: non-production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: staging-mot
  labels:
    scope: non-production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: staging-rides
  labels:
    scope: non-production
spec:
  details:
    accountId: "************"
    region: "me-central-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: dev-rh
  labels:
    scope: non-production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"