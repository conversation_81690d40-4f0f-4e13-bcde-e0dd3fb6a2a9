---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: dev-rh
  labels:
    scope: non-production
spec:
  details:
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: dev-shared-services
  labels:
    scope: non-production
spec:
  details:
    subnets:
      - subnet-025b2961358fdec96
      - subnet-09bfd245288f30b2b
      - subnet-0f7f7fc372844ef87
    awsAccountId: "************"
    enableVictoriaMetrics: false
    dynatrace: 
      useCSIDriver: false
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"      
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: dev-sre
  labels:
    scope: non-production
spec:
  details:
    dynatrace: 
      useCSIDriver: false
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: staging-mot
  labels:
    scope: non-production
spec:
  details:
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: staging-pay
  labels:
    scope: non-production
spec:
  details:
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"   
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: staging-rides
  labels:
    scope: non-production
spec:
  details:
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"
