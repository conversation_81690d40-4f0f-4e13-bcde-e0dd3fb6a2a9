---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-rh-k8s
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-shared-services
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-pay
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-payvas
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-mot
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-rides
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "me-central-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: control-prod
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-logging
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"
---
apiVersion: accounts.k8s.careem.com/v1
kind: AwsAccount
metadata:
  name: prod-rh
  labels:
    scope: production
spec:
  details:
    accountId: "************"
    region: "eu-west-1"