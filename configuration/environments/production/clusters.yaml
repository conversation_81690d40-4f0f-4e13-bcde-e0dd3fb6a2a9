---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: control-prod
  labels:
    scope: production
spec:
  details:
    dynatrace: 
      useCSIDriver: false
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: data-prod-rh
  labels:
    scope: production
spec:
  details: 
    dynatrace: 
      useCSIDriver: false
      activeGate:
        computeResources: medium
      oneAgentVersion: "stable"
    enablePriorityNamespaces: true
    priorityNamespaces:
      - mp-health-.+
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: prod-mot
  labels:
    scope: production
spec:
  details: 
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: medium
      oneAgentVersion: "stable"        
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: prod-pay
  labels:
    scope: production
spec:
  details: 
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: medium
      oneAgentVersion: "stable"        
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: prod-rh
  labels:
    scope: production
spec:
  details: 
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: medium
      oneAgentVersion: "stable"
    enableEbsSimpleScraping: true
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: prod-rides
  labels:
    scope: production
spec:
  details: 
    dynatrace: 
      useCSIDriver: true
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"      
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: prod-shared-services
  labels:
    scope: production
spec:
  details:
    subnets:
      - subnet-0ef3211b28ebc2221
      - subnet-0fbb653bd168971dd
      - subnet-01c948aac4723005d
    awsAccountId: "************"
    dynatrace: 
      useCSIDriver: false
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"
---
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: control-prod
  labels:
    scope: production
spec:
  details: 
    dynatrace: 
      useCSIDriver: false
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"
---      
apiVersion: clusters.k8s.careem.com/v1
kind: Cluster
metadata:
  name: prod-logging
  labels:
    scope: production
spec:
  details: 
    dynatrace: 
      useCSIDriver: false
      activeGate:
        computeResources: small
      oneAgentVersion: "stable"      