apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: assignment-service-slo
  labels:
    service: assignment-service
    scope: global
spec:
  global:
    latency: 5726
  keyRequests:
  - name: bookinguid-offer-status
    path: /v<apiversion>/booking/uuid/<bookingUid>/offer/<status>
    target: 99.95
    latency: 2505
  - name: manual-assignments
    path: /manual/assignments
    target: 99.95
    latency: 805
  - name: booking-assign-reply
    path: /v<apiVersion>/public/booking/assign/reply
    target: 99.95
    latency: 33
  - name: uuid-bookinguid-assign
    path: /v<apiVersion>/public/booking/uuid/<bookingUid>/assign
    target: 99.95
    latency: 5726
  - name: assignment-redispatch
    path: /assignment/redispatch
    target: 99.95
    latency: 3579
  - name: vapiversion-negotiated-assign
    path: /v<apiVersion>/negotiated/assign
    target: 99.95
    latency: 894
  - name: assignments-acknowledgements
    path: /assignments/acknowledgements
    target: 99.95
    latency: 268
  failureDetection:
    exceptions:
      ignoredExceptions:
      - classPattern: com.careem.lib.commons.exception.v2.FacadeException
        messagePattern: ''
      - classPattern: com.careem.lib.commons.exception.v2.ServiceException
        messagePattern: ''
