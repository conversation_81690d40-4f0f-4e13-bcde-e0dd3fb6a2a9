---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: bike-master-dashboard
  labels:
    scope: global
spec:
  name: "[BIKE] Custom Dashboard"
  json: |
    {
      "version": 18,
      "variables": [],
      "tiles": {
        "0": {
          "title": "Connected Device Count",
          "type": "data",
          "query": "timeseries failures = sum(bike_experience_requests_gauge), by:{c_service, name, status}\n| fieldsAdd entity.name = c_service\n| join [fetch dt.entity.service], prefix:\"service.\", on:{entity.name}\n| filter c_service == \"sm-omni-iot-service\"\n| fields interval, timeframe, failures, name, service.id, service.entity.name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxis": [
                  "name",
                  "service.id",
                  "service.entity.name"
                ],
                "categoryAxisLabel": "name,service.id,service.entity.name",
                "valueAxis": [
                  "interval"
                ],
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "failures"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "name",
              "prefixIcon": "",
              "recordField": "name",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value",
              "sparklineSettings": {
                "record": "failures"
              }
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "failures"
                  ],
                  "value": "sparkline",
                  "id": 1742206647552
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {
                "value": "name"
              },
              "displayedFields": [
                "name",
                "service.id",
                "service.entity.name"
              ],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "name",
                "service.id",
                "service.entity.name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "label": {
              "showLabel": false,
              "label": "interval"
            },
            "icon": {
              "showIcon": false,
              "icon": ""
            },
            "autoSelectVisualization": true,
            "dataMapping": {
              "value": "interval"
            },
            "legend": {
              "ratio": 20,
              "showLegend": false,
              "position": "auto"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "1": {
          "title": "Omni Device Locks",
          "type": "data",
          "query": "timeseries requests = sum(\n        bike_experience_requests_total,\n        filter:{name == \"lock_device\" AND c_service == \"sm-omni-iot-service\"}\n      ), by:{c_service, status}\n| fieldsAdd entity.name = c_service\n| join [fetch dt.entity.service], prefix:\"service.\", on:{entity.name}\n| fields interval, timeframe, status, requests, service.id, service.entity.name\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "requests"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "bike_experience_failure_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "requests"
                  ],
                  "value": "sparkline",
                  "id": 1746773686900
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 12
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "2": {
          "title": "Omni Device Unlocks",
          "type": "data",
          "query": "timeseries requests = sum(\n        bike_experience_requests_total,\n        filter:{name == \"unlock_device\" AND c_service == \"sm-omni-iot-service\"}\n      ), by:{c_service, status}\n| fieldsAdd entity.name = c_service\n| join [fetch dt.entity.service], prefix:\"service.\", on:{entity.name}\n| fields interval, timeframe, status, requests, service.id, service.entity.name\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "requests"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "bike_experience_failure_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "requests"
                  ],
                  "value": "sparkline",
                  "id": 1746773686900
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 12
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "3": {
          "title": "Omni Failures",
          "type": "data",
          "query": "timeseries failures = sum(\n  bike_experience_requests_total,\n  filter:{c_service == \"sm-omni-iot-service\" AND status   == \"failure\"}\n), by:{name}\n| fields interval, timeframe, name, failures\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "failures"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "bike_experience_failure_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 19
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "4": {
          "title": "Underpayments",
          "type": "data",
          "query": "timeseries union:true,\n  {total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"customer.underpayment\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"customer.underpayment\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"customer.underpayment\"}\n  )}\n| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "5": {
          "title": "Settlements",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"underpayments.settlement\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"underpayments.settlement\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"underpayments.settlement\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "6": {
          "title": "Suspensions",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"customer.suspension\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"customer.suspension\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"customer.suspension\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "7": {
          "title": "Reactivations",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"customer.reactivation\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"customer.reactivation\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"customer.reactivation\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "8": {
          "title": "Unlocks",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"vehicles.unlock\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"vehicles.unlock\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"vehicles.unlock\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "9": {
          "title": "Stations Details Update",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"stations.update.details\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"stations.update.details\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"stations.update.details\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "10": {
          "title": "Saved Trips",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"trips.persistence\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"trips.persistence\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"trips.persistence\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "11": {
          "title": "CPay Callbacks",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"payments.callback\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"payments.callback\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"payments.callback\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "12": {
          "title": "Kafka Messages",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"kafka.message\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"kafka.message\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"kafka.message\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "13": {
          "title": "Kafka Drops",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"kafka.message.exhausted\"}\n  )\n}| fields interval, timeframe,\n         total_requests\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "14": {
          "title": "BES Jobs",
          "type": "data",
          "query": "timeseries union:true,\n { invoices_unpaid = sum(\n      bike_experience_requests_total,\n      filter:{name == \"invoices.process-unpaid.started\"}\n  ),\n  pass_activities = sum(\n      bike_experience_requests_total,\n      filter:{name == \"jobs.activities.pass.started\"}\n  ),\n  pass_tax_reports = sum(\n      bike_experience_requests_total,\n      filter:{name == \"jobs.pass_tax_reports.started\"}\n  ),\n  vehicle_location = sum(\n      bike_experience_requests_total,\n      filter:{name == \"jobs.vehicles.location.started\"}\n  )\n}| fields interval, timeframe,\n         invoices_unpaid, pass_activities, pass_tax_reports, vehicle_location\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "invoices_unpaid",
                  "pass_activities",
                  "pass_tax_reports",
                  "vehicle_location"
                ]
              },
              "bandChartSettings": {
                "lower": "invoices_unpaid",
                "upper": "pass_activities"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "invoices_unpaid",
                    "pass_activities",
                    "pass_tax_reports",
                    "vehicle_location"
                  ],
                  "value": "sparkline",
                  "id": 1747030577266
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "15": {
          "title": "Overage trips",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"trips.overage\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"trips.overage\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"trips.overage\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "16": {
          "title": "Activities (trips)",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"activities.trip\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"activities.trip\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"activities.trip\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "17": {
          "title": "Tax Report (trips)",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"reports.tax.trip\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"reports.tax.trip\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"reports.tax.trip\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "18": {
          "title": "CPlus Savings",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"cplus.savings.trip\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"cplus.savings.trip\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"cplus.savings.trip\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "19": {
          "title": "Trip Start Notifications",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"trips.start.notification\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"trips.start.notification\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"trips.start.notification\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "20": {
          "title": "Trip End Notifications",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"trips.end.notification\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"trips.end.notification\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"trips.end.notification\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "21": {
          "title": "Activities (pass)",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"activities.pass\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"activities.pass\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"activities.pass\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "22": {
          "title": "Tax Report (pass)",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"reports.tax.pass\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"reports.tax.pass\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"reports.tax.pass\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "23": {
          "title": "Tax Report (pass)",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"cplus.subscriptions\"}\n  ),\n  failure = sum(\n      bike_experience_failure_seconds_count,\n      filter:{name == \"cplus.subscriptions\"}\n  ),\n  success = sum(\n      bike_experience_success_seconds_count,\n      filter:{name == \"cplus.subscriptions\"}\n  )\n}| fields interval, timeframe,\n         total_requests, failure, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "failure",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "failure"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "24": {
          "title": "BEC Kafka",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      bike_experience_requests_total,\n      filter:{name == \"processing_request_total\"}\n  ),\n  success = sum(\n      bike_experience_requests_total,\n      filter:{name == \"processing_success_total\"}\n  )\n}| fields interval, timeframe,\n         total_requests, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "success"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "25": {
          "title": "BEC Kafka Events received/processed",
          "type": "data",
          "query": "timeseries union:true,\n { total_requests = sum(\n      kafka_consumer_message_seconds,\n      filter:{c_service == \"bike-pbsc-events-consumer\"}\n  ),\n  success = sum(\n      bike_experience_requests_total,\n      filter:{name == \"processing_success_total\"}\n  )\n}| fields interval, timeframe,\n         total_requests, success\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "total_requests",
                  "success"
                ]
              },
              "bandChartSettings": {
                "lower": "total_requests",
                "upper": "success"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "total_requests"
                  ],
                  "value": "sparkline",
                  "id": 1746776784442
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "26": {
          "title": "BEC Kafka Errors",
          "type": "data",
          "query": "timeseries union:true, requests = sum(\n    bike_experience_requests_total,\n    filter:{status == \"failure\"\n            AND c_service == \"bike-pbsc-events-consumer\"}\n), by:{name}\n| fields interval, timeframe, name, requests\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "requests"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "bike_experience_failure_seconds_count • bike_experience_success_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "27": {
          "title": "Okai Connected Device Count",
          "type": "data",
          "query": "timeseries devices = sum(bike_experience_requests_gauge), by:{c_service, name, status}\n| fieldsAdd entity.name = c_service\n| join [fetch dt.entity.service], prefix:\"service.\", on:{entity.name}\n| filter c_service == \"sm-okai-iot-service\"\n| fields interval, timeframe, devices, name, service.id, service.entity.name",
          "visualization": "recordView",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "name,service.id,service.entity.name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "name",
              "prefixIcon": "",
              "recordField": "name",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value",
              "sparklineSettings": {
                "record": "failures"
              }
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {
                "value": "name"
              },
              "displayedFields": [
                "name",
                "service.id",
                "service.entity.name"
              ],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "name",
                "service.id",
                "service.entity.name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "label": {
              "showLabel": false,
              "label": "interval"
            },
            "icon": {
              "showIcon": false,
              "icon": ""
            },
            "autoSelectVisualization": true,
            "dataMapping": {
              "value": "interval"
            },
            "legend": {
              "ratio": 20,
              "showLegend": false,
              "position": "auto"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "28": {
          "title": "Okai Device Locks",
          "type": "data",
          "query": "timeseries requests = sum(\n        bike_experience_requests_total,\n        filter:{name == \"lock_device\" AND c_service == \"sm-okai-iot-service\"}\n      ), by:{c_service, status}\n| fieldsAdd entity.name = c_service\n| join [fetch dt.entity.service], prefix:\"service.\", on:{entity.name}\n| fields interval, timeframe, status, requests, service.id, service.entity.name\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "requests"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "bike_experience_failure_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "requests"
                  ],
                  "value": "sparkline",
                  "id": 1746773686900
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 12
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "29": {
          "title": "Okai Device Unlocks",
          "type": "data",
          "query": "timeseries requests = sum(\n        bike_experience_requests_total,\n        filter:{name == \"unlock_device\" AND c_service == \"sm-okai-iot-service\"}\n      ), by:{c_service, status}\n| fieldsAdd entity.name = c_service\n| join [fetch dt.entity.service], prefix:\"service.\", on:{entity.name}\n| fields interval, timeframe, status, requests, service.id, service.entity.name\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "requests"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "bike_experience_failure_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "requests"
                  ],
                  "value": "sparkline",
                  "id": 1746773686900
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 12
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "30": {
          "title": "Okai Failures",
          "type": "data",
          "query": "timeseries failures = sum(\n  bike_experience_requests_total,\n  filter:{c_service == \"sm-okai-iot-service\" AND status   == \"failure\"}\n), by:{name}\n\n| fields interval, timeframe, name, failures\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "service.id",
                "service.entity.name"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "failures"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "bike_experience_failure_seconds_count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "failures"
                  ],
                  "value": "sparkline",
                  "id": 1747126388468
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 19
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        }
      },
      "layouts": {
        "0": {
          "x": 0,
          "y": 0,
          "w": 23,
          "h": 7
        },
        "1": {
          "x": 0,
          "y": 7,
          "w": 23,
          "h": 7
        },
        "2": {
          "x": 0,
          "y": 14,
          "w": 23,
          "h": 7
        },
        "3": {
          "x": 0,
          "y": 21,
          "w": 23,
          "h": 7
        },
        "4": {
          "x": 0,
          "y": 28,
          "w": 23,
          "h": 7
        },
        "5": {
          "x": 0,
          "y": 35,
          "w": 23,
          "h": 7
        },
        "6": {
          "x": 0,
          "y": 42,
          "w": 23,
          "h": 7
        },
        "7": {
          "x": 0,
          "y": 49,
          "w": 23,
          "h": 7
        },
        "8": {
          "x": 0,
          "y": 56,
          "w": 23,
          "h": 7
        },
        "9": {
          "x": 0,
          "y": 63,
          "w": 23,
          "h": 7
        },
        "10": {
          "x": 0,
          "y": 70,
          "w": 23,
          "h": 7
        },
        "11": {
          "x": 0,
          "y": 77,
          "w": 23,
          "h": 7
        },
        "12": {
          "x": 0,
          "y": 84,
          "w": 23,
          "h": 7
        },
        "13": {
          "x": 0,
          "y": 91,
          "w": 23,
          "h": 7
        },
        "14": {
          "x": 0,
          "y": 98,
          "w": 23,
          "h": 7
        },
        "15": {
          "x": 0,
          "y": 105,
          "w": 23,
          "h": 7
        },
        "16": {
          "x": 0,
          "y": 112,
          "w": 23,
          "h": 7
        },
        "17": {
          "x": 0,
          "y": 119,
          "w": 23,
          "h": 7
        },
        "18": {
          "x": 0,
          "y": 126,
          "w": 23,
          "h": 7
        },
        "19": {
          "x": 0,
          "y": 133,
          "w": 23,
          "h": 7
        },
        "20": {
          "x": 0,
          "y": 140,
          "w": 23,
          "h": 7
        },
        "21": {
          "x": 0,
          "y": 147,
          "w": 23,
          "h": 7
        },
        "22": {
          "x": 0,
          "y": 154,
          "w": 23,
          "h": 7
        },
        "23": {
          "x": 0,
          "y": 161,
          "w": 23,
          "h": 7
        },
        "24": {
          "x": 0,
          "y": 168,
          "w": 23,
          "h": 7
        },
        "25": {
          "x": 0,
          "y": 175,
          "w": 23,
          "h": 7
        },
        "26": {
          "x": 0,
          "y": 182,
          "w": 23,
          "h": 7
        },
        "27": {
          "x": 0,
          "y": 189,
          "w": 23,
          "h": 7
        },
        "28": {
          "x": 0,
          "y": 196,
          "w": 23,
          "h": 7
        },
        "29": {
          "x": 0,
          "y": 203,
          "w": 23,
          "h": 7
        },
        "30": {
          "x": 0,
          "y": 210,
          "w": 23,
          "h": 7
        }
      },
      "importedWithCode": false,
      "settings": {}
    }