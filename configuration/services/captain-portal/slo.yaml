---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: captain-portal-slo
  labels:
    service: captain-portal
    scope: global
spec:
  global:
    latency: 10000
  keyRequests:
    - name: "api-admaauth-adma"
      path: /api/admaAuth/ADMA
      target: 99.95
      latency: 300
    - name: "api-admaauth-idma"
      path: /api/admaAuth/IDMA
      target: 99.95
      latency: 300
    - name: "api-auth-js"
      path: /api/auth.js
      target: 99.95
      latency: 300
    - name: "api-cloudauth"
      path: /api/cloudAuth
      target: 99.95
      latency: 300
    - name: "listing-val-val"
      path: /api/acceptance-rate/listing/<val>/<val>
      target: 99.95
      latency: 10000
    - name: "completion-rate-getcompletion-val"
      path: /api/completion-rate/getCompletion/<val>
      target: 99.95
      latency: 10000
    - name: "completion-rate-getcompletiondetail-val"
      path: /api/completion-rate/getCompletionDetail/<val>
      target: 99.95
      latency: 10000
    - name: "loyalty-getloyaltystatus-val"
      path: /api/loyalty/getloyaltyStatus/<val>
      target: 99.95
      latency: 10000
    - name: "api-profile-val"
      path: /api/profile/<val>
      target: 99.95
      latency: 10000
    - name: "listing-val-val"
      path: /api/available-hours/listing/<val>/<val>
      target: 99.95
      latency: 10000