---
apiVersion: observability.careem.com/v1
kind: CustomMetrics
metadata:
  name: captain-comms-service-dynatrace-metrics
  labels:
    service: captain-comms-service
    scope: global
spec:
    metrics:
      - name: "fabric_channel_event_total"
        aggregationLabels:
          - "version"
          - "channel"
          - "result"
          - "event"

      - name: "fabric_message_received_total"
        aggregationLabels:
          - "version"
          - "channel"
          - "result"
          - "type"

      - name: "fabric_message_sent_total"
        aggregationLabels:
          - "version"
          - "channel"
