---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: captain-comms-service-dashboard
  labels:
    scope: global
spec:
  name: "[captain-comms-service] Custom Dashboard"
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.kubernetes_cluster\n| fields entity.name\n| sort entity.name asc",
                "multiple": false,
                "defaultValue": "prod-rh-eks"
            },
            {
                "key": "Namespace",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.cloud_application_namespace\n| fields id, name = entity.name\n| filter in(id, classicEntitySelector(concat(\"type(CLOUD_APPLICATION_NAMESPACE),toRelationship.isClusterOfNamespace(type(KUBERNETES_CLUSTER),entityName.equals(\", $Cluster, \"))\")))\n| fields name\n| sort name asc",
                "multiple": false,
                "defaultValue": "captain-comms-service"
            },
            {
                "key": "NamespaceID",
                "visible": false,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.cloud_application_namespace\n| filter entity.name==$Namespace\n| filter in(id, classicEntitySelector(concat(\"type(CLOUD_APPLICATION_NAMESPACE),toRelationship.isClusterOfNamespace(type(KUBERNETES_CLUSTER),entityName.equals(\", $Cluster,\"))\")))\n| fields id",
                "multiple": false
            }
        ],
        "tiles": {
            "6": {
                "title": "Cluster CPU Utilization Contribution",
                "type": "data",
                "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage)\n}, filter: {\n  k8s.cluster.name == $Cluster\n}, by: { k8s.cluster.name },\nfrom: -2m\n| fieldsAdd cpu_usage_cluster = arrayFirst(cpu_usage)\n| fieldsRemove cpu_usage\n| join [\n  timeseries {\n    cpu_usage = sum(dt.kubernetes.container.cpu_usage)\n  }, filter: {\n    k8s.cluster.name == $Cluster AND\n    k8s.namespace.name == $Namespace\n  }, by: { k8s.namespace.name, k8s.cluster.name },\n  from: -2m\n], executionOrder: leftFirst, on: { k8s.cluster.name }, fields: { cpu_usage }\n| fieldsAdd cpu_usage_namespace = arrayFirst(cpu_usage)\n| fieldsAdd cpu_usage_percent = cpu_usage_namespace * 100  / cpu_usage_cluster",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "cpu_usage_percent",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 70
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 90
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.cluster.name",
                            "categoryAxisLabel": "k8s.cluster.name",
                            "valueAxis": "cpu_usage_cluster",
                            "valueAxisLabel": "cpu_usage_cluster"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "cpu_usage"
                            ],
                            "leftAxisDimensions": [
                                "k8s.cluster.name",
                                "cpu_usage_cluster",
                                "cpu_usage_namespace",
                                "cpu_usage_percent"
                            ]
                        },
                        "hiddenLegendFields": [
                            "interval",
                            "cpu_usage_cluster",
                            "cpu_usage_namespace",
                            "cpu_usage_percent"
                        ],
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - CPU usage"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "cpu_usage_percent",
                        "prefixIcon": "",
                        "recordField": "cpu_usage_percent",
                        "autoscale": true,
                        "sparklineSettings": {
                            "isVisible": false,
                            "showTicks": false
                        },
                        "alignment": "center",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto",
                            "upward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            }
                        },
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "cpu_usage"
                                ],
                                "value": "sparkline",
                                "id": 1734614775828
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "k8s.cluster.name"
                        },
                        "displayedFields": [
                            "k8s.cluster.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "cpu_usage_cluster",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "cpu_usage_namespace",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "cpu_usage_percent",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "k8s.cluster.name"
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "cpu_usage_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1716814972871
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "7": {
                "title": "Cluster Memory Utilization Contribution",
                "type": "data",
                "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set)\n}, filter: {\n  k8s.cluster.name == $Cluster\n}, by: { k8s.cluster.name },\nfrom: -2m\n| fieldsAdd memory_usage_cluster = arrayFirst(memory_usage)\n| fieldsRemove memory_usage\n| join [\n  timeseries {\n    memory_usage = sum(dt.kubernetes.container.memory_working_set)\n  }, filter: {\n    k8s.cluster.name == $Cluster AND\n    k8s.namespace.name == $Namespace\n  }, by: { k8s.namespace.name, k8s.cluster.name },\n  from: -2m\n], executionOrder: leftFirst, on: { k8s.cluster.name }, fields: { memory_usage }\n| fieldsAdd memory_usage_namespace = arrayFirst(memory_usage)\n| fieldsAdd memory_usage_percent = memory_usage_namespace * 100  / memory_usage_cluster",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "memory_usage_percent",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 70
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 90
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.cluster.name",
                            "categoryAxisLabel": "k8s.cluster.name",
                            "valueAxis": "memory_usage_cluster",
                            "valueAxisLabel": "memory_usage_cluster"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "memory_usage"
                            ],
                            "leftAxisDimensions": [
                                "k8s.cluster.name",
                                "memory_usage_cluster",
                                "memory_usage_namespace",
                                "memory_usage_percent"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "memory_usage_cluster",
                            "memory_usage_namespace",
                            "memory_usage_percent"
                        ],
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - working set memory"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "memory_usage_percent",
                        "prefixIcon": "",
                        "recordField": "memory_usage_percent",
                        "autoscale": true,
                        "sparklineSettings": {
                            "isVisible": false,
                            "showTicks": false
                        },
                        "alignment": "center",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto",
                            "neutral": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "upward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            }
                        },
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "memory_usage"
                                ],
                                "value": "sparkline",
                                "id": 1734615651470
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "k8s.cluster.name"
                        },
                        "displayedFields": [
                            "k8s.cluster.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "memory_usage_cluster",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "memory_usage_namespace",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "memory_usage_percent",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "k8s.cluster.name"
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "memory_usage_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715164486934
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "8": {
                "title": "Pods",
                "type": "data",
                "query": "timeseries {\n  pods_namespace = sum(dt.kubernetes.pods)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace AND\n  if(isNotNull(pod_phase), pod_phase == \"Running\", else: true)\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd pods_namespace = arrayFirst(pods_namespace)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": [
                                "k8s.namespace.name"
                            ],
                            "categoryAxisLabel": "k8s.namespace.name",
                            "valueAxis": [
                                "pods_namespace"
                            ],
                            "valueAxisLabel": "pods_namespace",
                            "tooltipVariant": "single"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "interval"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8sspace.name"
                        ],
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "valueRepresentation": "absolute",
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "pods_namespace",
                        "prefixIcon": "",
                        "recordField": "pods_namespace",
                        "autoscale": true,
                        "sparklineSettings": {
                            "isVisible": true,
                            "showTicks": false
                        },
                        "alignment": "center",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto",
                            "neutral": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "upward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "trendField": "pods_namespace"
                        },
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "pods_namespace"
                        },
                        "displayedFields": [
                            "k8s.namespace.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "pods_namespace",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "k8s.namespace.name"
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "pods_namespace",
                            "unitCategory": "unspecified",
                            "baseUnit": "count",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1734622122485
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 20000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "9": {
                "title": "CPU Requests Utilization",
                "type": "data",
                "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage),\n  requests_cpu = sum(dt.kubernetes.container.requests_cpu)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd cpu_usage_namespace = arrayFirst(cpu_usage)\n| fieldsAdd cpu_requests_namespace = arrayFirst(requests_cpu)\n| fieldsAdd cpu_requests_utilization_percent = cpu_usage_namespace * 100 / cpu_requests_namespace",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "cpu_requests_utilization_percent",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 100
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 130
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.namespace.name",
                            "categoryAxisLabel": "k8s.namespace.name",
                            "valueAxis": "cpu_usage_namespace",
                            "valueAxisLabel": "cpu_usage_namespace"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "cpu_usage",
                                "requests_cpu"
                            ],
                            "leftAxisDimensions": [
                                "k8s.namespace.name",
                                "cpu_usage_namespace",
                                "cpu_requests_namespace",
                                "cpu_requests_utilization_percent"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8sspace.name",
                            "interval",
                            "cpu_usage_namespace",
                            "cpu_requests_namespace",
                            "cpu_requests_utilization_percent"
                        ],
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - CPU usage • Kubernetes: Container - CPU requests"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "cpu_requests_utilization_percent",
                        "prefixIcon": "",
                        "recordField": "cpu_requests_utilization_percent",
                        "autoscale": true,
                        "sparklineSettings": {
                            "isVisible": false,
                            "showTicks": false
                        },
                        "alignment": "center",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto",
                            "upward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            }
                        },
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "k8s.namespace.name"
                        },
                        "displayedFields": [
                            "k8s.namespace.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "cpu_requests_utilization_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715164486934
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 20000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "title": "Memory Requests Utilization",
                "type": "data",
                "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set),\n  requests_memory = sum(dt.kubernetes.container.requests_memory)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd memory_usage_namespace = arrayFirst(memory_usage)\n| fieldsAdd memory_requests_namespace = arrayFirst(requests_memory)\n| fieldsAdd memory_requests_utilization_percent = memory_usage_namespace * 100 / memory_requests_namespace",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "memory_requests_utilization_percent",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 100
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 130
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.namespace.name",
                            "categoryAxisLabel": "k8s.namespace.name",
                            "valueAxis": "memory_usage_namespace",
                            "valueAxisLabel": "memory_usage_namespace"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "memory_usage",
                                "requests_memory"
                            ],
                            "leftAxisDimensions": [
                                "k8s.namespace.name",
                                "memory_usage_namespace",
                                "memory_requests_namespace",
                                "memory_requests_utilization_percent"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8sspace.name",
                            "interval",
                            "memory_usage_namespace",
                            "memory_requests_namespace",
                            "memory_requests_utilization_percent"
                        ],
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - working set memory • Kubernetes: Container - memory requests"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "memory_requests_utilization_percent",
                        "prefixIcon": "",
                        "recordField": "memory_requests_utilization_percent",
                        "autoscale": true,
                        "sparklineSettings": {
                            "isVisible": false,
                            "showTicks": false
                        },
                        "alignment": "center",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto",
                            "upward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "neutral": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            }
                        },
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "k8s.namespace.name"
                        },
                        "displayedFields": [
                            "k8s.namespace.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "memory_requests_utilization_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715164486934
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 20000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "11": {
                "title": "CPU Limits Utilization",
                "type": "data",
                "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage),\n  limits_cpu = sum(dt.kubernetes.container.limits_cpu)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd cpu_usage_namespace = arrayFirst(cpu_usage)\n| fieldsAdd cpu_limits_namespace = arrayFirst(limits_cpu)\n| fieldsAdd cpu_limits_utilization_percent = cpu_usage_namespace * 100 / cpu_limits_namespace",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "cpu_limits_utilization_percent",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 100
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 150
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.namespace.name",
                            "categoryAxisLabel": "k8s.namespace.name",
                            "valueAxis": "cpu_usage_namespace",
                            "valueAxisLabel": "cpu_usage_namespace"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "cpu_usage",
                                "limits_cpu"
                            ],
                            "leftAxisDimensions": [
                                "k8s.namespace.name",
                                "cpu_usage_namespace",
                                "cpu_limits_namespace",
                                "cpu_limits_utilization_percent"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8sspace.name",
                            "interval",
                            "cpu_usage_namespace",
                            "cpu_limits_namespace",
                            "cpu_limits_utilization_percent"
                        ],
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - CPU usage • Kubernetes: Container - CPU limits"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "cpu_limits_utilization_percent",
                        "prefixIcon": "",
                        "recordField": "cpu_limits_utilization_percent",
                        "autoscale": true,
                        "sparklineSettings": {
                            "isVisible": false,
                            "showTicks": false
                        },
                        "alignment": "center",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto",
                            "neutral": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "upward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            }
                        },
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "k8s.namespace.name"
                        },
                        "displayedFields": [
                            "k8s.namespace.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "cpu_limits_utilization_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715164486934
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 20000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "title": "Memory Limits Utilization",
                "type": "data",
                "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set),\n  limits_memory = sum(dt.kubernetes.container.limits_memory)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd memory_usage_namespace = arrayFirst(memory_usage)\n| fieldsAdd memory_limits_namespace = arrayFirst(limits_memory)\n| fieldsAdd memory_limits_utilization_percent = memory_usage_namespace * 100 / memory_limits_namespace",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "memory_limits_utilization_percent",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 70
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 90
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.namespace.name",
                            "categoryAxisLabel": "k8s.namespace.name",
                            "valueAxis": "memory_usage_namespace",
                            "valueAxisLabel": "memory_usage_namespace"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "memory_usage",
                                "limits_memory"
                            ],
                            "leftAxisDimensions": [
                                "k8s.namespace.name",
                                "memory_usage_namespace",
                                "memory_limits_namespace",
                                "memory_limits_utilization_percent"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8sspace.name",
                            "interval",
                            "memory_usage_namespace",
                            "memory_limits_namespace",
                            "memory_limits_utilization_percent"
                        ],
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - working set memory • Kubernetes: Container - memory limits"
                        }
                    },
                    "singleValue": {
                        "showLabel": false,
                        "label": "memory_limits_utilization_percent",
                        "prefixIcon": "",
                        "recordField": "memory_limits_utilization_percent",
                        "autoscale": true,
                        "sparklineSettings": {
                            "isVisible": false,
                            "showTicks": false
                        },
                        "alignment": "center",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto",
                            "neutral": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "downward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            },
                            "upward": {
                                "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                            }
                        },
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "k8s.namespace.name"
                        },
                        "displayedFields": [
                            "k8s.namespace.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "memory_limits_utilization_percent",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715164486934
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 20000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "13": {
                "title": "CPU Usage per Pod",
                "type": "data",
                "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage, rollup:avg)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name }\n| filter isNotNull(cpu_usage)\n| sort cpu_usage desc\n| limit 20",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.pod.name",
                            "categoryAxisLabel": "k8s.pod.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "cpu_usage"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.cloud_application_instance",
                                "k8s.pod.name"
                            ]
                        },
                        "hiddenLegendFields": [
                            "dt.entity.cloud_application_instance",
                            "interval"
                        ],
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - CPU usage"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "id",
                        "prefixIcon": "",
                        "recordField": "id",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "cpu_usage"
                                ],
                                "value": "sparkline",
                                "id": 1734615651544
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "dt.entity.cloud_application_instance"
                        },
                        "displayedFields": [
                            "dt.entity.cloud_application_instance"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "cpu",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715178821410
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "CPU Quota",
                "type": "data",
                "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage),\n  cpu_throttled = sum(dt.kubernetes.container.cpu_throttled),\n  requests_cpu = sum(dt.kubernetes.container.requests_cpu),\n  limits_cpu = sum(dt.kubernetes.container.limits_cpu)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name },\nfrom: -2m,\nnonempty: true,\nunion: true\n| fieldsRemove interval, timeframe\n| fieldsAdd cpu_usage = arrayFirst(cpu_usage)\n| fieldsAdd cpu_throttled = arrayFirst(cpu_throttled)\n| fieldsAdd requests_cpu = arrayFirst(requests_cpu)\n| fieldsAdd limits_cpu = arrayFirst(limits_cpu)\n| fieldsAdd requests_cpu_percent = cpu_usage / requests_cpu * 100\n| fieldsAdd limits_cpu_percent = cpu_usage / limits_cpu * 100\n| fieldsAdd cpu_slack = (requests_cpu - cpu_usage) / 1000\n| sort cpu_usage desc\n| fieldsRename `Name` = k8s.pod.name, `CPU Usage` = cpu_usage, `CPU Throttled` = cpu_throttled, `CPU Requests` = requests_cpu, `CPU Requests %` = requests_cpu_percent, `CPU Limits` = limits_cpu, `CPU Limits %` = limits_cpu_percent, `CPU Slack` = cpu_slack\n",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.cloud_application_instance",
                            "categoryAxisLabel": "dt.entity.cloud_application_instance",
                            "valueAxis": "CPU Usage",
                            "valueAxisLabel": "CPU Usage"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "id",
                        "prefixIcon": "",
                        "recordField": "id",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "id"
                            ],
                            [
                                "namespace.id"
                            ],
                            [
                                "namespace.labels"
                            ],
                            [
                                "namespace.annotations"
                            ],
                            [
                                "namespace.age"
                            ],
                            [
                                "cluster.id"
                            ],
                            [
                                "k8s.pod.uid"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {
                            "[\"limits_cpu\"]": 124.046875,
                            "[\"requests_cpu\"]": 142.453125,
                            "[\"namespace.name\"]": 231.140625,
                            "[\"cpu_usage\"]": 157.8125,
                            "[\"cpu_throttled\"]": 164.46875,
                            "[\"requests_cpu_percent\"]": 191.28125,
                            "[\"limits_cpu_percent\"]": 175.859375
                        },
                        "sortBy": {
                            "columnId": "[\"CPU Usage\"]",
                            "direction": "descending"
                        },
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "dt.entity.cloud_application_instance"
                        },
                        "displayedFields": [
                            "dt.entity.cloud_application_instance"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "CPU Usage",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "Name"
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "CPU Usage",
                            "unitCategory": "unspecified",
                            "baseUnit": "millicore",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715618539759
                        },
                        {
                            "identifier": "CPU Throttled",
                            "unitCategory": "unspecified",
                            "baseUnit": "millicore",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715618579313
                        },
                        {
                            "identifier": "CPU Requests",
                            "unitCategory": "unspecified",
                            "baseUnit": "millicore",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715618597827
                        },
                        {
                            "identifier": "CPU Limits",
                            "unitCategory": "unspecified",
                            "baseUnit": "millicore",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715618609828
                        },
                        {
                            "identifier": "CPU Slack",
                            "unitCategory": "unspecified",
                            "baseUnit": "core",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715618618693
                        },
                        {
                            "identifier": "CPU Requests %",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715686358414
                        },
                        {
                            "identifier": "CPU Limits %",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715687708509
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "17": {
                "type": "markdown",
                "content": "### CPU\n"
            },
            "18": {
                "type": "markdown",
                "content": "### Memory"
            },
            "19": {
                "title": "Memory Usage per Pod",
                "type": "data",
                "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set, rollup:avg)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name }\n| sort memory_usage desc\n| limit 20",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.pod.name",
                            "categoryAxisLabel": "k8s.pod.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "memory_usage"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.cloud_application_instance",
                                "k8s.pod.name"
                            ]
                        },
                        "hiddenLegendFields": [
                            "dt.entity.cloud_application_instance"
                        ],
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "valueRepresentation": "absolute",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - working set memory"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "id",
                        "prefixIcon": "",
                        "recordField": "id",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "memory_usage"
                                ],
                                "value": "sparkline",
                                "id": 1734614775475
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "dt.entity.cloud_application_instance"
                        },
                        "displayedFields": [
                            "dt.entity.cloud_application_instance"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ]
                    },
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "20": {
                "title": "Memory Quota",
                "type": "data",
                "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set),\n  requests_memory = sum(dt.kubernetes.container.requests_memory),\n  limits_memory = sum(dt.kubernetes.container.limits_memory)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name },\nfrom: -2m,\nnonempty: true,\nunion: true\n| fieldsRemove interval, timeframe\n| fieldsAdd memory_usage = arrayFirst(memory_usage)\n| fieldsAdd requests_memory = arrayFirst(requests_memory)\n| fieldsAdd limits_memory = arrayFirst(limits_memory)\n| fieldsAdd requests_memory_percent = memory_usage / requests_memory * 100\n| fieldsAdd limits_memory_percent = memory_usage / limits_memory * 100\n| fieldsAdd memory_slack = (requests_memory - memory_usage)\n| sort memory_usage desc\n| fieldsRename `Name` = k8s.pod.name, `Memory Usage` = memory_usage, `Memory Requests` = requests_memory, `Memory Requests %` = requests_memory_percent, `Memory Limits` = limits_memory, `Memory Limits %` = limits_memory_percent, `Memory Slack` = memory_slack",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.cloud_application_instance",
                            "categoryAxisLabel": "dt.entity.cloud_application_instance",
                            "valueAxis": "Memory Usage",
                            "valueAxisLabel": "Memory Usage"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "id",
                        "prefixIcon": "",
                        "recordField": "id",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "id"
                            ],
                            [
                                "namespace.id"
                            ],
                            [
                                "namespace.labels"
                            ],
                            [
                                "namespace.annotations"
                            ],
                            [
                                "namespace.age"
                            ],
                            [
                                "cluster.id"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {
                            "[\"limits_cpu\"]": 124.046875,
                            "[\"requests_cpu\"]": 142.453125,
                            "[\"namespace.name\"]": 231.140625,
                            "[\"cpu_usage\"]": 157.8125,
                            "[\"cpu_throttled\"]": 164.46875,
                            "[\"requests_cpu_percent\"]": 191.28125,
                            "[\"limits_cpu_percent\"]": 175.859375
                        },
                        "sortBy": {
                            "columnId": "[\"Memory Usage\"]",
                            "direction": "descending"
                        },
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "dt.entity.cloud_application_instance"
                        },
                        "displayedFields": [
                            "dt.entity.cloud_application_instance"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "Memory Usage",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "Memory Requests",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "Memory Limits",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "Memory Requests %",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "Memory Limits %",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "Memory Slack",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "Name"
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "Memory Usage",
                            "unitCategory": "data",
                            "baseUnit": "byte",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698159493
                        },
                        {
                            "identifier": "Memory Requests",
                            "unitCategory": "data",
                            "baseUnit": "byte",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698181310
                        },
                        {
                            "identifier": "Memory Requests %",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698189862
                        },
                        {
                            "identifier": "Memory Limits",
                            "unitCategory": "data",
                            "baseUnit": "byte",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698204816
                        },
                        {
                            "identifier": "Memory Limits %",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698213152
                        },
                        {
                            "identifier": "Memory Slack",
                            "unitCategory": "data",
                            "baseUnit": "byte",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698222294
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "22": {
                "title": "Receive Bandwidth",
                "type": "data",
                "query": "timeseries {\n  received_data = sum(dt.kubernetes.pod.network_received_data, rollup:avg)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name }\n| sort received_data desc\n| limit 20",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": [
                                "dt.entity.cloud_application_instance",
                                "k8s.pod.name"
                            ],
                            "categoryAxisLabel": "dt.entity.cloud_application_instance,k8s.pod.name",
                            "valueAxis": [
                                "interval"
                            ],
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "received_data"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.cloud_application_instance",
                                "k8s.pod.name"
                            ]
                        },
                        "hiddenLegendFields": [
                            "dt.entity.cloud_application_instance"
                        ],
                        "legend": {
                            "position": "right",
                            "hidden": false
                        },
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Pod - network received data"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "id",
                        "prefixIcon": "",
                        "recordField": "id",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "received_data"
                                ],
                                "value": "sparkline",
                                "id": 1734614820139
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ]
                    },
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "23": {
                "title": "Transmit Bandwidth",
                "type": "data",
                "query": "timeseries {\n  transmitted_data = sum(dt.kubernetes.pod.network_transmitted_data, rollup:avg)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name }\n| sort transmitted_data desc\n| limit 20",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": [
                                "dt.entity.cloud_application_instance",
                                "k8s.pod.name"
                            ],
                            "categoryAxisLabel": "dt.entity.cloud_application_instance,k8s.pod.name",
                            "valueAxis": [
                                "interval"
                            ],
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "transmitted_data"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.cloud_application_instance",
                                "k8s.pod.name"
                            ]
                        },
                        "hiddenLegendFields": [
                            "dt.entity.cloud_application_instance"
                        ],
                        "legend": {
                            "position": "right",
                            "hidden": false
                        },
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Pod - network transmitted data"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "id",
                        "prefixIcon": "",
                        "recordField": "id",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "transmitted_data"
                                ],
                                "value": "sparkline",
                                "id": 1734614820153
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ]
                    },
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "33": {
                "type": "markdown",
                "content": "### Network"
            },
            "34": {
                "title": "Network Usage",
                "type": "data",
                "query": "timeseries {\n  receiveData = sum(dt.kubernetes.pod.network_received_data),\n  transmitData = sum(dt.kubernetes.pod.network_transmitted_data),\n  receivePacketDrop = sum(dt.kubernetes.pod.network_received_packets_dropped),\n  transmitPacketDrop = sum(dt.kubernetes.pod.network_transmitted_packets_dropped),\n  receiveError = sum(dt.kubernetes.pod.network_received_errors),\n  transmitError = sum(dt.kubernetes.pod.network_transmitted_errors)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name },\nfrom: -2m,\nnonempty: true,\nunion: true\n| fieldsRemove interval, timeframe\n| fieldsAdd receiveData = arrayFirst(receiveData)\n| fieldsAdd transmitData = arrayFirst(transmitData)\n| fieldsAdd receivePacketDrop = arrayFirst(receivePacketDrop)\n| fieldsAdd transmitPacketDrop = arrayFirst(transmitPacketDrop)\n| fieldsAdd receiveError = arrayFirst(receiveError)\n| fieldsAdd transmitError = arrayFirst(transmitError)\n| sort receiveData desc\n| fieldsRename `Name` = k8s.pod.name\n, `Current Receive Bandwidth` = receiveData\n, `Current Transmit Bandwidth` = transmitData\n, `Rate of Received Packets Dropped` = receivePacketDrop\n, `Rate of Transmitted Packets Dropped` = transmitPacketDrop\n, `Rate of Received Errors` = receiveError\n, `Rate of Transmitted Errors` = transmitError",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": [
                                "dt.entity.cloud_application_instance",
                                "Name"
                            ],
                            "categoryAxisLabel": "dt.entity.cloud_application_instance,Name",
                            "valueAxis": [
                                "Current Receive Bandwidth",
                                "Current Transmit Bandwidth",
                                "Rate of Received Packets Dropped",
                                "Rate of Transmitted Packets Dropped",
                                "Rate of Received Errors",
                                "Rate of Transmitted Errors"
                            ],
                            "valueAxisLabel": "Current Receive Bandwidth,Current Transmit Bandwidth,Rate of Received Packets Dropped,Rate of Transmitted Packets Dropped,Rate of Received Errors,Rate of Transmitted Errors",
                            "tooltipVariant": "single"
                        },
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "id",
                        "prefixIcon": "",
                        "recordField": "id",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": false
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "id"
                            ],
                            [
                                "namespace.id"
                            ],
                            [
                                "namespace.labels"
                            ],
                            [
                                "namespace.annotations"
                            ],
                            [
                                "namespace.age"
                            ],
                            [
                                "cluster.id"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {
                            "[\"limits_cpu\"]": 124.046875,
                            "[\"requests_cpu\"]": 142.453125,
                            "[\"namespace.name\"]": 231.140625,
                            "[\"cpu_usage\"]": 157.8125,
                            "[\"cpu_throttled\"]": 164.46875,
                            "[\"requests_cpu_percent\"]": 191.28125,
                            "[\"limits_cpu_percent\"]": 175.859375,
                            "[\"Name\"]": 204.78125
                        },
                        "sortBy": {
                            "columnId": "[\"Memory Usage\"]",
                            "direction": "descending"
                        },
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": "auto",
                        "dataMappings": {
                            "value": "Current Receive Bandwidth"
                        },
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "Name"
                        ],
                        "colorMode": "color-palette",
                        "colorPalette": "blue"
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "Current Receive Bandwidth",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "Current Transmit Bandwidth",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "dt.entity.cloud_application_instance",
                            "Name"
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "Current Receive Bandwidth",
                            "unitCategory": "datarate",
                            "baseUnit": "Bps",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698159493
                        },
                        {
                            "identifier": "Current Transmit Bandwidth",
                            "unitCategory": "datarate",
                            "baseUnit": "Bps",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1715698181310
                        },
                        {
                            "identifier": "Rate of Received Packets Dropped",
                            "unitCategory": "unspecified",
                            "baseUnit": "count_per_second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "p/s",
                            "delimiter": false,
                            "added": 1727770868052
                        },
                        {
                            "identifier": "Rate of Transmitted Packets Dropped",
                            "unitCategory": "unspecified",
                            "baseUnit": "count_per_second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "p/s",
                            "delimiter": false,
                            "added": 1727770889671
                        },
                        {
                            "identifier": "Rate of Received Errors",
                            "unitCategory": "unspecified",
                            "baseUnit": "count_per_second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "e/s",
                            "delimiter": false,
                            "added": 1727770905889
                        },
                        {
                            "identifier": "Rate of Transmitted Errors",
                            "unitCategory": "unspecified",
                            "baseUnit": "count_per_second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "e/s",
                            "delimiter": false,
                            "added": 1727770919779
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 100,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "35": {
                "type": "markdown",
                "content": "### Pods in Namespace: [$Namespace](/ui/intent/dynatrace.kubernetes/entityKubernetesNamespace/#{\"id\":\"$NamespaceID\"})"
            },
            "37": {
                "title": "Request Rates (per min)",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(grpc_server_handled_total, rate: 1m), value.A = avg(grpc_server_handled_total, rate: 1m, scalar: true) }, filter: { matchesValue(kubernetes_namespace, $Namespace) }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(grpc_server_handled_total, rate:1m)"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(grpc_server_handled_total, rate:1m)"
                                ],
                                "value": "sparkline",
                                "id": 1744968169530
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.5.1",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "grpc_server_handled_total",
                                "aggregation": "sum"
                            },
                            "rate": "1m",
                            "filter": "kubernetes_namespace =$Namespace "
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "38": {
                "title": "",
                "type": "data",
                "query": "timeseries avg(grpc_server_handling_seconds),\nfilter: { k8s.namespace.name == $Namespace }, by: { dt.entity.cloud_application_instance, k8s.pod.name }\n| fieldsAdd value.A = arrayAvg(`avg(grpc_server_handling_seconds)`)",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "avg(grpc_server_handling_seconds)"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.pod",
                            "interval",
                            "value.A",
                            "avg(grpc_server_handling_seconds)"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "grpc_server_handling_seconds"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "avg(grpc_server_handling_seconds)"
                                ],
                                "value": "sparkline",
                                "id": 1744979452789
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "39": {
                "type": "data",
                "title": "Fabric Channel Event Total",
                "query": "timeseries { sum(fabric_channel_event_total), value.A = avg(fabric_channel_event_total, scalar: true) }, by: { k8s.pod.name }",
                "queryConfig": {
                    "version": "13.5.1",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "fabric_channel_event_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "k8s.pod.name"
                            ]
                        }
                    ]
                },
                "subType": "dql-builder-metrics",
                "visualization": "lineChart",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.pod",
                            "interval",
                            "value.A",
                            "sum(fabric_channel_event_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(fabric_channel_event_total)"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(fabric_channel_event_total)"
                                ],
                                "value": "sparkline",
                                "id": 1746796984340
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "40": {
                "type": "data",
                "title": "Fabric Message Received Total",
                "query": "timeseries { sum(fabric_message_received_total), value.A = avg(fabric_message_received_total, scalar: true) }, by: { k8s.pod.name }",
                "queryConfig": {
                    "version": "13.5.1",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "fabric_message_received_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "k8s.pod.name"
                            ]
                        }
                    ]
                },
                "subType": "dql-builder-metrics",
                "visualization": "lineChart",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(fabric_message_received_total)"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(fabric_message_received_total)"
                                ],
                                "value": "sparkline",
                                "id": 1746797041485
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "41": {
                "type": "data",
                "title": "Fabric Message Sent Total",
                "query": "timeseries { sum(fabric_message_sent_total), value.A = avg(fabric_message_sent_total, scalar: true) }, by: { k8s.pod.name }",
                "queryConfig": {
                    "version": "13.5.1",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "fabric_message_sent_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "k8s.pod.name"
                            ]
                        }
                    ]
                },
                "subType": "dql-builder-metrics",
                "visualization": "lineChart",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(fabric_message_sent_total)"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(fabric_message_sent_total)"
                                ],
                                "value": "sparkline",
                                "id": 1746797134109
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            }
        },
        "layouts": {
            "6": {
                "x": 0,
                "y": 22,
                "w": 6,
                "h": 2
            },
            "7": {
                "x": 0,
                "y": 24,
                "w": 6,
                "h": 2
            },
            "8": {
                "x": 18,
                "y": 22,
                "w": 6,
                "h": 4
            },
            "9": {
                "x": 6,
                "y": 22,
                "w": 6,
                "h": 2
            },
            "10": {
                "x": 6,
                "y": 24,
                "w": 6,
                "h": 2
            },
            "11": {
                "x": 12,
                "y": 22,
                "w": 6,
                "h": 2
            },
            "12": {
                "x": 12,
                "y": 24,
                "w": 6,
                "h": 2
            },
            "13": {
                "x": 0,
                "y": 27,
                "w": 24,
                "h": 5
            },
            "15": {
                "x": 0,
                "y": 32,
                "w": 24,
                "h": 4
            },
            "17": {
                "x": 0,
                "y": 26,
                "w": 24,
                "h": 1
            },
            "18": {
                "x": 0,
                "y": 36,
                "w": 24,
                "h": 1
            },
            "19": {
                "x": 0,
                "y": 37,
                "w": 24,
                "h": 5
            },
            "20": {
                "x": 0,
                "y": 42,
                "w": 24,
                "h": 4
            },
            "22": {
                "x": 0,
                "y": 47,
                "w": 12,
                "h": 4
            },
            "23": {
                "x": 12,
                "y": 47,
                "w": 12,
                "h": 4
            },
            "33": {
                "x": 0,
                "y": 46,
                "w": 24,
                "h": 1
            },
            "34": {
                "x": 0,
                "y": 51,
                "w": 24,
                "h": 5
            },
            "35": {
                "x": 0,
                "y": 21,
                "w": 24,
                "h": 1
            },
            "37": {
                "x": 0,
                "y": 0,
                "w": 24,
                "h": 7
            },
            "38": {
                "x": 0,
                "y": 56,
                "w": 24,
                "h": 6
            },
            "39": {
                "x": 0,
                "y": 7,
                "w": 12,
                "h": 7
            },
            "40": {
                "x": 0,
                "y": 14,
                "w": 12,
                "h": 7
            },
            "41": {
                "x": 12,
                "y": 7,
                "w": 12,
                "h": 7
            }
        },
        "importedWithCode": false,
        "settings": {}
    }