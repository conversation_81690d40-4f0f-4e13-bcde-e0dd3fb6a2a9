---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: galileo-metrics
  labels:
    scope: global
spec:
  name: "Galileo Server Metrics"
  json: |
    {
      "version": 18,
      "variables": [],
      "tiles":
        {
          "0":
            {
              "title": "Request Count",
              "type": "data",
              "subType": "dql-builder-metrics",
              "query": "timeseries { sum(galileo_server_service_request), value.A = avg(galileo_server_service_request, scalar: true) }",
              "visualization": "lineChart",
              "visualizationSettings":
                {
                  "thresholds": [],
                  "chartSettings":
                    {
                      "gapPolicy": "gap",
                      "circleChartSettings":
                        {
                          "groupingThresholdType": "relative",
                          "groupingThresholdValue": 0,
                          "valueType": "relative",
                        },
                      "categoryOverrides": {},
                      "curve": "linear",
                      "pointsDisplay": "auto",
                      "categoricalBarChartSettings":
                        {
                          "layout": "horizontal",
                          "categoryAxisTickLayout": "horizontal",
                          "scale": "absolute",
                          "groupMode": "stacked",
                          "colorPaletteMode": "multi-color",
                          "valueAxisScale": "linear",
                        },
                      "colorPalette": "categorical",
                      "valueRepresentation": "absolute",
                      "truncationMode": "middle",
                      "xAxisScaling": "analyzedTimeframe",
                      "xAxisLabel": "timeframe",
                      "xAxisIsLabelVisible": false,
                      "hiddenLegendFields": ["interval", "value.A"],
                      "fieldMapping":
                        {
                          "timestamp": "timeframe",
                          "leftAxisValues": ["sum(galileo_server_service_request)"],
                        },
                      "leftYAxisSettings":
                        {
                          "isLabelVisible": true,
                          "label": "galileo_server_service_request",
                        },
                    },
                  "singleValue":
                    {
                      "showLabel": true,
                      "label": "",
                      "prefixIcon": "AnalyticsIcon",
                      "isIconVisible": false,
                      "autoscale": true,
                      "alignment": "center",
                      "colorThresholdTarget": "value",
                    },
                  "table":
                    {
                      "rowDensity": "condensed",
                      "enableSparklines": false,
                      "hiddenColumns": [],
                      "linewrapEnabled": false,
                      "lineWrapIds": [],
                      "monospacedFontEnabled": false,
                      "monospacedFontColumns": [],
                      "columnWidths": {},
                      "columnTypeOverrides":
                        [
                          {
                            "fields": ["sum(galileo_server_service_request)"],
                            "value": "sparkline",
                            "id": 1747747479749,
                          },
                        ],
                    },
                  "honeycomb":
                    {
                      "shape": "hexagon",
                      "legend":
                        { "hidden": false, "position": "auto", "ratio": "auto" },
                      "dataMappings": {},
                      "displayedFields": [],
                      "truncationMode": "middle",
                      "colorMode": "color-palette",
                      "colorPalette": "categorical",
                    },
                  "histogram":
                    {
                      "legend": { "position": "auto" },
                      "yAxis":
                        {
                          "label": "Frequency",
                          "isLabelVisible": true,
                          "scale": "linear",
                        },
                      "colorPalette": "categorical",
                      "dataMappings": [],
                      "variant": "single",
                      "truncationMode": "middle",
                    },
                  "valueBoundaries": { "min": "auto", "max": "auto" },
                  "autoSelectVisualization": true,
                },
              "querySettings":
                {
                  "maxResultRecords": 1000,
                  "defaultScanLimitGbytes": 500,
                  "maxResultMegaBytes": 1,
                  "defaultSamplingRatio": 10,
                  "enableSampling": false,
                },
              "queryConfig":
                {
                  "version": "13.5.2",
                  "subQueries":
                    [
                      {
                        "id": "A",
                        "isEnabled": true,
                        "datatype": "metrics",
                        "metric":
                          {
                            "key": "galileo_server_service_request",
                            "aggregation": "sum",
                          },
                      },
                    ],
                },
              "davis":
                { "enabled": false, "davisVisualization": { "isAvailable": true } },
            },
          "1":
            {
              "title": "Server Responses",
              "type": "data",
              "subType": "dql-builder-metrics",
              "query": "timeseries { avg(galileo_server_service_response), value.A = avg(galileo_server_service_response, scalar: true) }",
              "visualization": "lineChart",
              "visualizationSettings":
                {
                  "thresholds": [],
                  "chartSettings":
                    {
                      "gapPolicy": "gap",
                      "circleChartSettings":
                        {
                          "groupingThresholdType": "relative",
                          "groupingThresholdValue": 0,
                          "valueType": "relative",
                        },
                      "categoryOverrides": {},
                      "curve": "linear",
                      "pointsDisplay": "auto",
                      "categoricalBarChartSettings":
                        {
                          "layout": "horizontal",
                          "categoryAxisTickLayout": "horizontal",
                          "scale": "absolute",
                          "groupMode": "stacked",
                          "colorPaletteMode": "multi-color",
                          "valueAxisScale": "linear",
                        },
                      "colorPalette": "categorical",
                      "valueRepresentation": "absolute",
                      "truncationMode": "middle",
                      "xAxisScaling": "analyzedTimeframe",
                      "xAxisLabel": "timeframe",
                      "xAxisIsLabelVisible": false,
                      "hiddenLegendFields": ["interval", "value.A"],
                      "fieldMapping":
                        {
                          "timestamp": "timeframe",
                          "leftAxisValues":
                            ["avg(galileo_server_service_response)"],
                        },
                      "leftYAxisSettings":
                        {
                          "isLabelVisible": true,
                          "label": "galileo_server_service_response",
                        },
                    },
                  "singleValue":
                    {
                      "showLabel": true,
                      "label": "",
                      "prefixIcon": "AnalyticsIcon",
                      "isIconVisible": false,
                      "autoscale": true,
                      "alignment": "center",
                      "colorThresholdTarget": "value",
                    },
                  "table":
                    {
                      "rowDensity": "condensed",
                      "enableSparklines": false,
                      "hiddenColumns": [],
                      "linewrapEnabled": false,
                      "lineWrapIds": [],
                      "monospacedFontEnabled": false,
                      "monospacedFontColumns": [],
                      "columnWidths": {},
                      "columnTypeOverrides":
                        [
                          {
                            "fields": ["avg(galileo_server_service_response)"],
                            "value": "sparkline",
                            "id": 1747747520986,
                          },
                        ],
                    },
                  "honeycomb":
                    {
                      "shape": "hexagon",
                      "legend":
                        { "hidden": false, "position": "auto", "ratio": "auto" },
                      "dataMappings": {},
                      "displayedFields": [],
                      "truncationMode": "middle",
                      "colorMode": "color-palette",
                      "colorPalette": "categorical",
                    },
                  "histogram":
                    {
                      "legend": { "position": "auto" },
                      "yAxis":
                        {
                          "label": "Frequency",
                          "isLabelVisible": true,
                          "scale": "linear",
                        },
                      "colorPalette": "categorical",
                      "dataMappings": [],
                      "variant": "single",
                      "truncationMode": "middle",
                    },
                  "valueBoundaries": { "min": "auto", "max": "auto" },
                  "autoSelectVisualization": true,
                },
              "querySettings":
                {
                  "maxResultRecords": 1000,
                  "defaultScanLimitGbytes": 500,
                  "maxResultMegaBytes": 1,
                  "defaultSamplingRatio": 10,
                  "enableSampling": false,
                },
              "queryConfig":
                {
                  "version": "13.5.2",
                  "subQueries":
                    [
                      {
                        "id": "A",
                        "isEnabled": true,
                        "datatype": "metrics",
                        "metric":
                          {
                            "key": "galileo_server_service_response",
                            "aggregation": "avg",
                          },
                      },
                    ],
                },
              "davis":
                { "enabled": false, "davisVisualization": { "isAvailable": true } },
            },
          "2":
            {
              "title": "Go Routines",
              "type": "data",
              "subType": "dql-builder-metrics",
              "query": "timeseries { avg(go_goroutines), value.A = avg(go_goroutines, scalar: true) }",
              "visualization": "lineChart",
              "visualizationSettings":
                {
                  "thresholds": [],
                  "chartSettings":
                    {
                      "gapPolicy": "gap",
                      "circleChartSettings":
                        {
                          "groupingThresholdType": "relative",
                          "groupingThresholdValue": 0,
                          "valueType": "relative",
                        },
                      "categoryOverrides": {},
                      "curve": "linear",
                      "pointsDisplay": "auto",
                      "categoricalBarChartSettings":
                        {
                          "layout": "horizontal",
                          "categoryAxisTickLayout": "horizontal",
                          "scale": "absolute",
                          "groupMode": "stacked",
                          "colorPaletteMode": "multi-color",
                          "valueAxisScale": "linear",
                        },
                      "colorPalette": "categorical",
                      "valueRepresentation": "absolute",
                      "truncationMode": "middle",
                      "xAxisScaling": "analyzedTimeframe",
                      "xAxisLabel": "timeframe",
                      "xAxisIsLabelVisible": false,
                      "hiddenLegendFields": ["interval", "value.A"],
                      "fieldMapping":
                        {
                          "timestamp": "timeframe",
                          "leftAxisValues": ["avg(go_goroutines)"],
                        },
                      "leftYAxisSettings": {},
                    },
                  "singleValue":
                    {
                      "showLabel": true,
                      "label": "",
                      "prefixIcon": "AnalyticsIcon",
                      "isIconVisible": false,
                      "autoscale": true,
                      "alignment": "center",
                      "colorThresholdTarget": "value",
                    },
                  "table":
                    {
                      "rowDensity": "condensed",
                      "enableSparklines": false,
                      "hiddenColumns": [],
                      "linewrapEnabled": false,
                      "lineWrapIds": [],
                      "monospacedFontEnabled": false,
                      "monospacedFontColumns": [],
                      "columnWidths": {},
                      "columnTypeOverrides":
                        [
                          {
                            "fields": ["avg(go_goroutines)"],
                            "value": "sparkline",
                            "id": 1747830209444,
                          },
                        ],
                    },
                  "honeycomb":
                    {
                      "shape": "hexagon",
                      "legend":
                        { "hidden": false, "position": "auto", "ratio": "auto" },
                      "dataMappings": {},
                      "displayedFields": [],
                      "truncationMode": "middle",
                      "colorMode": "color-palette",
                      "colorPalette": "categorical",
                    },
                  "histogram":
                    {
                      "legend": { "position": "auto" },
                      "yAxis":
                        {
                          "label": "Frequency",
                          "isLabelVisible": true,
                          "scale": "linear",
                        },
                      "colorPalette": "categorical",
                      "dataMappings": [],
                      "variant": "single",
                      "truncationMode": "middle",
                    },
                  "valueBoundaries": { "min": "auto", "max": "auto" },
                  "autoSelectVisualization": true,
                },
              "querySettings":
                {
                  "maxResultRecords": 1000,
                  "defaultScanLimitGbytes": 500,
                  "maxResultMegaBytes": 1,
                  "defaultSamplingRatio": 10,
                  "enableSampling": false,
                },
              "queryConfig":
                {
                  "version": "13.5.2",
                  "subQueries":
                    [
                      {
                        "id": "A",
                        "isEnabled": true,
                        "datatype": "metrics",
                        "metric": { "key": "go_goroutines", "aggregation": "avg" },
                      },
                    ],
                },
              "davis":
                { "enabled": false, "davisVisualization": { "isAvailable": true } },
            },
          "3":
            {
              "title": "Memory Allocated",
              "type": "data",
              "subType": "dql-builder-metrics",
              "query": "timeseries { sum(go_memstats_mallocs_total), value.A = avg(go_memstats_mallocs_total, scalar: true) }",
              "visualization": "lineChart",
              "visualizationSettings":
                {
                  "thresholds": [],
                  "chartSettings":
                    {
                      "gapPolicy": "gap",
                      "circleChartSettings":
                        {
                          "groupingThresholdType": "relative",
                          "groupingThresholdValue": 0,
                          "valueType": "relative",
                        },
                      "categoryOverrides": {},
                      "curve": "linear",
                      "pointsDisplay": "auto",
                      "categoricalBarChartSettings":
                        {
                          "layout": "horizontal",
                          "categoryAxisTickLayout": "horizontal",
                          "scale": "absolute",
                          "groupMode": "stacked",
                          "colorPaletteMode": "multi-color",
                          "valueAxisScale": "linear",
                        },
                      "colorPalette": "categorical",
                      "valueRepresentation": "absolute",
                      "truncationMode": "middle",
                      "xAxisScaling": "analyzedTimeframe",
                      "xAxisLabel": "timeframe",
                      "xAxisIsLabelVisible": false,
                      "hiddenLegendFields": ["interval", "value.A"],
                      "fieldMapping":
                        {
                          "timestamp": "timeframe",
                          "leftAxisValues": ["sum(go_memstats_mallocs_total)"],
                        },
                      "leftYAxisSettings": {},
                    },
                  "singleValue":
                    {
                      "showLabel": true,
                      "label": "",
                      "prefixIcon": "AnalyticsIcon",
                      "isIconVisible": false,
                      "autoscale": true,
                      "alignment": "center",
                      "colorThresholdTarget": "value",
                    },
                  "table":
                    {
                      "rowDensity": "condensed",
                      "enableSparklines": false,
                      "hiddenColumns": [],
                      "linewrapEnabled": false,
                      "lineWrapIds": [],
                      "monospacedFontEnabled": false,
                      "monospacedFontColumns": [],
                      "columnWidths": {},
                      "columnTypeOverrides":
                        [
                          {
                            "fields": ["sum(go_memstats_mallocs_total)"],
                            "value": "sparkline",
                            "id": 1747999059125,
                          },
                        ],
                    },
                  "honeycomb":
                    {
                      "shape": "hexagon",
                      "legend":
                        { "hidden": false, "position": "auto", "ratio": "auto" },
                      "dataMappings": {},
                      "displayedFields": [],
                      "truncationMode": "middle",
                      "colorMode": "color-palette",
                      "colorPalette": "categorical",
                    },
                  "histogram":
                    {
                      "legend": { "position": "auto" },
                      "yAxis":
                        {
                          "label": "Frequency",
                          "isLabelVisible": true,
                          "scale": "linear",
                        },
                      "colorPalette": "categorical",
                      "dataMappings": [],
                      "variant": "single",
                      "truncationMode": "middle",
                    },
                  "valueBoundaries": { "min": "auto", "max": "auto" },
                  "autoSelectVisualization": true,
                },
              "querySettings":
                {
                  "maxResultRecords": 1000,
                  "defaultScanLimitGbytes": 500,
                  "maxResultMegaBytes": 1,
                  "defaultSamplingRatio": 10,
                  "enableSampling": false,
                },
              "queryConfig":
                {
                  "version": "13.5.2",
                  "subQueries":
                    [
                      {
                        "id": "A",
                        "isEnabled": true,
                        "datatype": "metrics",
                        "metric":
                          {
                            "key": "go_memstats_mallocs_total",
                            "aggregation": "sum",
                          },
                      },
                    ],
                },
              "davis":
                { "enabled": false, "davisVisualization": { "isAvailable": true } },
            },
          "4":
            {
              "title": "Memory Free",
              "type": "data",
              "subType": "dql-builder-metrics",
              "query": "timeseries { sum(go_memstats_frees_total), value.A = avg(go_memstats_frees_total, scalar: true) }",
              "visualization": "lineChart",
              "visualizationSettings":
                {
                  "thresholds": [],
                  "chartSettings":
                    {
                      "gapPolicy": "gap",
                      "circleChartSettings":
                        {
                          "groupingThresholdType": "relative",
                          "groupingThresholdValue": 0,
                          "valueType": "relative",
                        },
                      "categoryOverrides": {},
                      "curve": "linear",
                      "pointsDisplay": "auto",
                      "categoricalBarChartSettings":
                        {
                          "layout": "horizontal",
                          "categoryAxisTickLayout": "horizontal",
                          "scale": "absolute",
                          "groupMode": "stacked",
                          "colorPaletteMode": "multi-color",
                          "valueAxisScale": "linear",
                        },
                      "colorPalette": "categorical",
                      "valueRepresentation": "absolute",
                      "truncationMode": "middle",
                      "xAxisScaling": "analyzedTimeframe",
                      "xAxisLabel": "timeframe",
                      "xAxisIsLabelVisible": false,
                      "hiddenLegendFields": ["interval", "value.A"],
                      "fieldMapping":
                        {
                          "timestamp": "timeframe",
                          "leftAxisValues": ["sum(go_memstats_frees_total)"],
                        },
                      "leftYAxisSettings": {},
                    },
                  "singleValue":
                    {
                      "showLabel": true,
                      "label": "",
                      "prefixIcon": "AnalyticsIcon",
                      "isIconVisible": false,
                      "autoscale": true,
                      "alignment": "center",
                      "colorThresholdTarget": "value",
                    },
                  "table":
                    {
                      "rowDensity": "condensed",
                      "enableSparklines": false,
                      "hiddenColumns": [],
                      "linewrapEnabled": false,
                      "lineWrapIds": [],
                      "monospacedFontEnabled": false,
                      "monospacedFontColumns": [],
                      "columnWidths": {},
                      "columnTypeOverrides":
                        [
                          {
                            "fields": ["sum(go_memstats_frees_total)"],
                            "value": "sparkline",
                            "id": 1747999129949,
                          },
                        ],
                    },
                  "honeycomb":
                    {
                      "shape": "hexagon",
                      "legend":
                        { "hidden": false, "position": "auto", "ratio": "auto" },
                      "dataMappings": {},
                      "displayedFields": [],
                      "truncationMode": "middle",
                      "colorMode": "color-palette",
                      "colorPalette": "categorical",
                    },
                  "histogram":
                    {
                      "legend": { "position": "auto" },
                      "yAxis":
                        {
                          "label": "Frequency",
                          "isLabelVisible": true,
                          "scale": "linear",
                        },
                      "colorPalette": "categorical",
                      "dataMappings": [],
                      "variant": "single",
                      "truncationMode": "middle",
                    },
                  "valueBoundaries": { "min": "auto", "max": "auto" },
                  "autoSelectVisualization": true,
                },
              "querySettings":
                {
                  "maxResultRecords": 1000,
                  "defaultScanLimitGbytes": 500,
                  "maxResultMegaBytes": 1,
                  "defaultSamplingRatio": 10,
                  "enableSampling": false,
                },
              "queryConfig":
                {
                  "version": "13.5.2",
                  "subQueries":
                    [
                      {
                        "id": "A",
                        "isEnabled": true,
                        "datatype": "metrics",
                        "metric":
                          {
                            "key": "go_memstats_frees_total",
                            "aggregation": "sum",
                          },
                      },
                    ],
                },
              "davis":
                { "enabled": false, "davisVisualization": { "isAvailable": true } },
            },
          "5":
            {
              "title": "Heap In Use",
              "type": "data",
              "subType": "dql-builder-metrics",
              "query": 'timeseries { avg(go_memstats_heap_inuse_bytes), value.A = avg(go_memstats_heap_inuse_bytes, scalar: true) }, filter: { matchesValue(k8s.namespace.name, "galileo-server") }',
              "visualization": "lineChart",
              "visualizationSettings":
                {
                  "thresholds": [],
                  "chartSettings":
                    {
                      "gapPolicy": "gap",
                      "circleChartSettings":
                        {
                          "groupingThresholdType": "relative",
                          "groupingThresholdValue": 0,
                          "valueType": "relative",
                        },
                      "categoryOverrides": {},
                      "curve": "linear",
                      "pointsDisplay": "auto",
                      "categoricalBarChartSettings":
                        {
                          "layout": "horizontal",
                          "categoryAxisTickLayout": "horizontal",
                          "scale": "absolute",
                          "groupMode": "stacked",
                          "colorPaletteMode": "multi-color",
                          "valueAxisScale": "linear",
                        },
                      "colorPalette": "categorical",
                      "valueRepresentation": "absolute",
                      "truncationMode": "middle",
                      "xAxisScaling": "analyzedTimeframe",
                      "xAxisLabel": "timeframe",
                      "xAxisIsLabelVisible": false,
                      "hiddenLegendFields": ["interval", "value.A"],
                      "fieldMapping":
                        {
                          "timestamp": "timeframe",
                          "leftAxisValues": ["avg(go_memstats_heap_inuse_bytes)"],
                        },
                      "leftYAxisSettings": {},
                    },
                  "singleValue":
                    {
                      "showLabel": true,
                      "label": "",
                      "prefixIcon": "AnalyticsIcon",
                      "isIconVisible": false,
                      "autoscale": true,
                      "alignment": "center",
                      "colorThresholdTarget": "value",
                    },
                  "table":
                    {
                      "rowDensity": "condensed",
                      "enableSparklines": false,
                      "hiddenColumns": [],
                      "linewrapEnabled": false,
                      "lineWrapIds": [],
                      "monospacedFontEnabled": false,
                      "monospacedFontColumns": [],
                      "columnWidths": {},
                      "columnTypeOverrides":
                        [
                          {
                            "fields": ["avg(go_memstats_heap_inuse_bytes)"],
                            "value": "sparkline",
                            "id": 1747999176109,
                          },
                        ],
                    },
                  "honeycomb":
                    {
                      "shape": "hexagon",
                      "legend":
                        { "hidden": false, "position": "auto", "ratio": "auto" },
                      "dataMappings": {},
                      "displayedFields": [],
                      "truncationMode": "middle",
                      "colorMode": "color-palette",
                      "colorPalette": "categorical",
                    },
                  "histogram":
                    {
                      "legend": { "position": "auto" },
                      "yAxis":
                        {
                          "label": "Frequency",
                          "isLabelVisible": true,
                          "scale": "linear",
                        },
                      "colorPalette": "categorical",
                      "dataMappings": [],
                      "variant": "single",
                      "truncationMode": "middle",
                    },
                  "valueBoundaries": { "min": "auto", "max": "auto" },
                  "autoSelectVisualization": true,
                },
              "querySettings":
                {
                  "maxResultRecords": 1000,
                  "defaultScanLimitGbytes": 500,
                  "maxResultMegaBytes": 1,
                  "defaultSamplingRatio": 10,
                  "enableSampling": false,
                },
              "queryConfig":
                {
                  "version": "13.5.2",
                  "subQueries":
                    [
                      {
                        "id": "A",
                        "isEnabled": true,
                        "datatype": "metrics",
                        "metric":
                          {
                            "key": "go_memstats_heap_inuse_bytes",
                            "aggregation": "avg",
                          },
                        "filter": "k8s.namespace.name = galileo-server ",
                      },
                    ],
                },
              "davis":
                { "enabled": false, "davisVisualization": { "isAvailable": true } },
            },
        },
      "layouts":
        {
          "0": { "x": 0, "y": 0, "w": 12, "h": 6 },
          "1": { "x": 12, "y": 0, "w": 12, "h": 6 },
          "2": { "x": 0, "y": 6, "w": 12, "h": 6 },
          "3": { "x": 12, "y": 6, "w": 12, "h": 6 },
          "4": { "x": 0, "y": 12, "w": 12, "h": 6 },
          "5": { "x": 12, "y": 12, "w": 12, "h": 6 },
        },
      "importedWithCode": false,
      "settings": {},
    }
