---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: baskets-slo
  labels:
    service: baskets
    scope: global
spec:
  global:
    latency: 10000
  keyRequests:
    - name: "baskets-id0-9-unavailable-items"
      path: /v1/internal/baskets/<id>/unavailable-items
      target: 99.95
      latency: 250
    - name: "admin-baskets-id0-9"
      path: /v1/admin/baskets/<id>
      target: 99.95
      latency: 250
    - name: "groupuuid-remove-userid0-9"
      path: /v1/baskets/<groupUuid>/remove/<userId>
      target: 99.95
      latency: 250
    - name: "baskets-basketid0-9-replace-items"
      path: /v1/internal/baskets/<basketId>/replace-items
      target: 99.95
      latency: 2500
    - name: "baskets-basketid0-9-replace-items-v2"
      path: /v1/internal/baskets/<basketId>/replace-items-v2
      target: 99.95
      latency: 2500
    - name: "baskets-id0-9-share"
      path: /v1/baskets/<id>/share
      target: 99.95
      latency: 2500
    - name: "baskets-id0-9-revert-checkout"
      path: /v1/internal/baskets/<id>/revert-checkout
      target: 99.95
      latency: 2500
    - name: "baskets-groupuuid-leave"
      path: /v1/baskets/<groupUuid>/leave
      target: 99.95
      latency: 2500
    - name: "baskets-id0-9-items"
      path: /v1/admin/baskets/<id>/items
      target: 99.95
      latency: 2500
    - name: "baskets-id0-9-item-suggestions"
      path: /v1/admin/baskets/<id>/item-suggestions
      target: 99.95
      latency: 5000
    - name: "v1-baskets"
      path: /v1/baskets
      target: 99.95
      latency: 5000
    - name: "baskets-groupuuid-join"
      path: /v1/baskets/<groupUuid>/join
      target: 99.95
      latency: 5000
    - name: "baskets-groupuuid-update-group-owner-status"
      path: /v1/baskets/<groupUuid>/update-group-owner-status
      target: 99.95
      latency: 5000
    - name: "baskets-basketid0-9-item-suggestions"
      path: /v1/baskets/<basketId>/item-suggestions
      target: 99.95
      latency: 10000
    - name: "baskets-reorder-orderid0-9"
      path: /v1/baskets/reorder/<orderId>
      target: 99.95
      latency: 10000
    - name: "baskets-checkout-id0-9"
      path: /v1/internal/baskets/checkout/<id>
      target: 99.95
      latency: 10000
    - name: "baskets-merchant-merchantid0-9"
      path: /v1/baskets/merchant/<merchantId>
      target: 99.95
      latency: 10000
    - name: "baskets-basketid0-9-poll"
      path: /v1/baskets/<basketId>/poll
      target: 99.95
      latency: 10000
    - name: "baskets-id0-9-items"
      path: /v1/baskets/<id>/items
      target: 99.95
      latency: 10000
    - name: "id0-9-items-itemid0-9"
      path: /v1/baskets/<id>/items/<itemId>
      target: 99.95
      latency: 10000
    - name: "baskets-checkout-id0-9"
      path: /v2/internal/baskets/checkout/<id>
      target: 99.95
      latency: 10000
    - name: "admin-baskets-duplicate"
      path: /v1/admin/baskets/duplicate
      target: 99.95
      latency: 10000
    - name: "baskets-id0-9-minimum-details"
      path: /v1/internal/baskets/<id>/minimum-details
      target: 99.95
      latency: 10000