---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: payment-processing-payment-service-slo
  labels:
    service: payment-processing-payment-service
    scope: global
spec:
  global:
    latency: 32143
  keyRequests:
    - name: "apiversion-nickname-paymentinformationid"
      path: /payment/card/<apiVersion>/nickname/<paymentInformationId>
      target: 99.95
      latency: 687
    - name: "transactions-identifier-refund"
      path: /v1/transactions/<identifier>/refund
      target: 99.95
      latency: 687
    - name: "vapiversion-paymentinformations-getduplicatepaymentinformation"
      path: /payment/v<apiVersion>/paymentInformations/getDuplicatePaymentInformation
      target: 99.95
      latency: 687
    - name: "transactions-transactionreference-release"
      path: /payment/user/<apiVersion>/transactions/<transactionReference>/release
      target: 99.95
      latency: 687
    - name: "redirect-3ds-echo"
      path: /payment/public/<apiVersion>/redirect/3ds/echo
      target: 99.95
      latency: 687
    - name: "apiversion-verifications-savecard"
      path: /payment/card/<apiVersion>/verifications/saveCard
      target: 99.95
      latency: 687
    - name: "apiversion-publish-cardexpiryevent"
      path: /payment/card/<apiVersion>/publish/cardExpiryEvent
      target: 99.95
      latency: 687
    - name: "apiversion-bins-bin"
      path: /<apiVersion>/bins/<bin>
      target: 99.95
      latency: 687
    - name: "admin-cards-transactions"
      path: /v1/admin/cards/transactions
      target: 99.95
      latency: 687
    - name: "vapiversion-paymentinformations-paymentinformationid"
      path: /payment/v<apiVersion>/paymentInformations/<paymentInformationId>
      target: 99.95
      latency: 687
    - name: "apiversion-getcardtransactionstatus-creditcardtransactionid"
      path: /payment/user/<apiVersion>/getCardTransactionStatus/<creditCardTransactionId>
      target: 99.95
      latency: 687
    - name: "apiversion-getinformation-paymentinfoid"
      path: /payment/user/<apiVersion>/getInformation/<paymentInfoId>
      target: 99.95
      latency: 687
    - name: "user-apiversion-getwallet"
      path: /payment/user/<apiVersion>/getWallet
      target: 99.95
      latency: 687
    - name: "payment-vapiversion-paymentinformations"
      path: /payment/v<apiVersion>/paymentInformations
      target: 99.95
      latency: 687
    - name: "card-apiversion-verifications"
      path: /payment/card/<apiVersion>/verifications
      target: 99.95
      latency: 1662
    - name: "v1-transactions"
      path: /v1/transactions
      target: 99.95
      latency: 1662
    - name: "admin-getcardtransactionstatus-creditcardtransactionid"
      path: /admin/getCardTransactionStatus/<creditCardTransactionId>
      target: 99.95
      latency: 1662
    - name: "vapiversion-user-userid"
      path: /payment/options/v<apiVersion>/user/<userId>
      target: 99.95
      latency: 1662
    - name: "apiversion-properties-list"
      path: /payment/card/<apiVersion>/properties/list
      target: 99.95
      latency: 1662
    - name: "user-apiversion-chargecard"
      path: /payment/user/<apiVersion>/chargeCard
      target: 99.95
      latency: 4334
    - name: "user-apiversion-deletepaymentoption"
      path: /payment/user/<apiVersion>/deletePaymentOption
      target: 99.95
      latency: 4334
    - name: "apiversion-cpay-notificationhandler"
      path: /payment/public/<apiVersion>/cpay/notificationHandler
      target: 99.95
      latency: 4334
    - name: "addcard-identifier-followup"
      path: /payment/user/<apiVersion>/addCard/<identifier>/followup
      target: 99.95
      latency: 12143
    - name: "transactions-transactionreference-capture"
      path: /payment/user/<apiVersion>/transactions/<transactionReference>/capture
      target: 99.95
      latency: 12143
    - name: "card-charge-followup"
      path: /payment/user/<apiVersion>/card/charge/followup
      target: 99.95
      latency: 12143
    - name: "vapiversion-paymentinformations-verifytoken"
      path: /payment/v<apiVersion>/paymentInformations/verifyToken
      target: 99.95
      latency: 12143
    - name: "user-apiversion-addcard"
      path: /payment/user/<apiVersion>/addCard
      target: 99.95
      latency: 32143
    - name: "cards-user-userid"
      path: /payment/options/v<apiVersion>/cards/user/<userId>
      target: 99.95
      latency: 32143
    - name: "card-charge-3ds"
      path: /payment/user/<apiVersion>/card/charge/3ds
      target: 99.95
      latency: 32143