apiVersion: observability.careem.com/v1
kind: CustomMetrics
metadata:
  name: payment-processing-payment-service-metrics
  labels:
    scope: global
spec:
  metrics:
    - name: payment_service_counter_kafka_transaction_history_total
      aggregationLabels:
        - application
        - status
    - name: payment_service_vgs_tokenize_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_counter_core_charge_notification_health_total
      aggregationLabels:
        - application
        - health_status
    - name: payment_service_cpay_event_health_seconds
      aggregationLabels:
        - application
        - service_name
        - exception
        - class
        - ip
        - method
        - quantile
    - name: payment_service_counter_payment_gateway_health_total
      aggregationLabels:
        - application
        - health_status
    - name: account_management_service_health
      aggregationLabels:
        - health_status
    - name: payment_instrument_block_engine_failed
    - name: payment_instrument_block_rule
      aggregationLabels:
        - rule_priority
    - name: invalid_bin_format
    - name: invalid_currency_code_exceptions
    - name: get_card_property_list_decryption_exception
    - name: get_card_property_list_validation_exception
    - name: get_card_property_list
    - name: kafka_transaction_history
      aggregationLabels:
        - STATUS
    - name: kafka_updates
    - name: charge_request_failure
      aggregationLabels:
        - TYPE
        - GATEWAY
        - CURRENCY
        - MERCHANT
    - name: charge_apple_pay
      aggregationLabels:
        - TYPE
        - IS_HOLD_TRANSACTION
    - name: release
      aggregationLabels:
        - GATEWAY
        - AUTHORIZE_STATUS
    - name: capture
      aggregationLabels:
        - GATEWAY
        - AUTHORIZE_STATUS
    - name: delete_card
    - name: add_authorize_request_status
      aggregationLabels:
        - GATEWAY
        - AUTHORIZE_STATUS
    - name: charge_authorize_request_status
      aggregationLabels:
        - GATEWAY
        - AUTHORIZE_STATUS
        - TYPE
        - MERCHANT
    - name: payment_gateway_health
      aggregationLabels:
        - ENDPOINT
        - health_status
        - GATEWAY
    - name: charge_with_add_card
      aggregationLabels:
        - IS_3DS
        - TYPE
        - CURRENCY
        - IS_HOLD_TRANSACTION
    - name: charge_card
      aggregationLabels:
        - IS_3DS
        - TYPE
        - CURRENCY
        - IS_HOLD_TRANSACTION
    - name: payment_service_cpay_event_health_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_event_health_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_event_health_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_paymentInfo_duplicate_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: payment_service_cpay_gateway_paymentInfo_duplicate_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_paymentInfo_duplicate_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_paymentInfo_duplicate_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_counter_charge_authorize_request_status_total
      aggregationLabels:
        - application
    - name: payment_service_counter_charge_request_failure_total
      aggregationLabels:
        - application
    - name: payment_service_counter_payment_instrument_block_engine_failed_total
      aggregationLabels:
        - application
    - name: payment_service_counter_add_with_sale_authorize_request_status_total
      aggregationLabels:
        - application
    - name: payment_service_counter_rejected_by_fraud_total
      aggregationLabels:
        - application
    - name: payment_service_cpay_gateway_applicable_merchant_v2_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_applicable_merchant_v2_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: payment_service_cpay_gateway_applicable_merchant_v2_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_applicable_merchant_v2_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_counter_charge_with_previous_transaction_id_total
      aggregationLabels:
        - application
    - name: payment_service_cpay_galileo_cache_variables_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_galileo_cache_variables_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: payment_service_cpay_galileo_cache_variables_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_galileo_cache_variables_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: resilience4j_circuitbreaker_not_permitted_calls_total
      aggregationLabels:
        - application
        - ip
        - kind
        - name
        - service_name
    - name: payment_service_counter_charge_with_add_card_total
      aggregationLabels:
        - application
    - name: payment_service_counter_invalid_currency_code_exceptions_total
      aggregationLabels:
        - application
    - name: payment_service_counter_account_management_service_health_total
      aggregationLabels:
        - application
    - name: payment_service_counter_maximum_limit_exceeded_total
      aggregationLabels:
        - application
    - name: payment_service_counter_get_card_property_list_decryption_exception_total
      aggregationLabels:
        - application
    - name: payment_service_vgs_tokenize_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: payment_service_vgs_tokenize_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_vgs_tokenize_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_counter_invalid_bin_format_total
      aggregationLabels:
        - application
    - name: payment_service_cpay_gateway_fetch_shopper_details_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: payment_service_cpay_gateway_fetch_shopper_details_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_fetch_shopper_details_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_fetch_shopper_details_seconds_max
      aggregationLabels:
        - application
    - name: payment_service_counter_add_authorize_request_status_total
      aggregationLabels:
        - application
    - name: payment_service_counter_get_card_property_list_validation_exception_total
      aggregationLabels:
        - application
    - name: payment_service_counter_get_card_property_list_total
      aggregationLabels:
        - application
    - name: cpay_pp_fraud_requests_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: cpay_pp_fraud_requests_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: cpay_pp_fraud_requests_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: cpay_pp_fraud_requests_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: cpay_pp_account_management_service_requests_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: cpay_pp_account_management_service_requests_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: cpay_pp_account_management_service_requests_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: cpay_pp_account_management_service_requests_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_counter_kafka_updates_total
      aggregationLabels:
        - application
    - name: payment_service_counter_charge_apple_pay_total
      aggregationLabels:
        - application
    - name: payment_service_counter_capture_total
      aggregationLabels:
        - application
    - name: payment_service_counter_payment_instrument_block_rule_total
      aggregationLabels:
        - application
    - name: payment_service_cpay_gateway_fetch_bins_list_seconds
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - quantile
        - service_name
    - name: payment_service_cpay_gateway_fetch_bins_list_seconds_count
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_fetch_bins_list_seconds_sum
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_cpay_gateway_fetch_bins_list_seconds_max
      aggregationLabels:
        - application
        - class
        - exception
        - ip
        - method
        - service_name
    - name: payment_service_counter_delete_card_total
      aggregationLabels:
        - application
    - name: payment_service_counter_release_total
      aggregationLabels:
        - application
