---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: payment-processing-payment-service-anomaly-detection
  labels:
    scope: global
    service: payment-processing-payment-service
spec:
  anomalies:
    staticThreshold:
      kafka-transaction-history-failure:
        title: "[payment-service] Kafka Transaction History Failure Rate High"
        description: "Kafka transaction history failure count >= 1 over 3m"
        alertCondition: "ABOVE"
        threshold: 1
        query: |
          timeseries failures = sum(payment_service_counter_kafka_transaction_history_total, default: 0, rate: 1m),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-payment-service") AND matchesValue(status, "FAILURE") }
          | fieldsAdd event_name = "Kafka Transaction History Failure >= 1"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, failures, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        evaluationWindow: "3"
        eventName: "Kafka Transaction History Failure"
        dimensionNameProperty: "event_name"

      vgs-tokenize-failure:
        title: "[payment-service] VGS Tokenize Failure > 5"
        description: "VGS Tokenize failure > 5 in last 3 minutes"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries failures = sum(payment_service_vgs_tokenize_seconds_count, default: 0, rate: 1m),
            by: {exception},
            interval: 1m,
            filter: { NOT matchesValue(exception, "none") }
          | fieldsAdd event_name = "VGS Tokenize Failures Increased In Last 3 mins"
          | fieldsAdd entity.name = "payment-processing-payment-service"
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, failures, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "1"
        evaluationWindow: "3"
        eventName: "VGS Tokenize Failure"
        dimensionNameProperty: "event_name"

      core-charge-notification-error:
        title: "[payment-service] Core-Charge Notification Error Count >= 1"
        description: "Core-charge notification failures >= 1 in last 5 minutes"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries errors = sum(payment_service_counter_core_charge_notification_health_total, default: 0, rate: 1m),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-payment-service") AND matchesValue(health_status, {"ERROR" , "FAILURE"}) }
          | fieldsAdd event_name = "Core-Charge Notification Errors >= 1"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, errors, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "1"
        evaluationWindow: "3"
        eventName: "Core-Charge Notification Failure"
        dimensionNameProperty: "event_name"

      producer-failure-rate-high:
        title: "[payment-service] One Or More Producer Failures > 0"
        description: "At least one producer has a failure > 0 in last 1 minute"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries failures = sum(payment_service_cpay_event_health_seconds_count, default: 0, rate: 1m),
            by: {class},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-payment-service") AND NOT matchesValue(exception, "none") }
          | fieldsAdd event_name = concat("Producer Failure Rate High - ", class)
          | fieldsAdd entity.name = "payment-processing-payment-service"
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, failures, event_name, class, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        evaluationWindow: "3"
        eventName: "Producer Failure Rate High"
        dimensionNameProperty: "event_name"

      gateway-success-rate-low:
        title: "[payment-service] CPAY Gateway Success Rate < 80%"
        description: "Gateway success rate < 80% over 10m window"
        alertCondition: "BELOW"
        threshold: 80
        query: |
          timeseries success = sum(payment_service_counter_payment_gateway_health_total, default: 0, rate: 1m),
            by: {application},
            interval: 1m,
            filter: { matchesValue(health_status, "SUCCESS") }
          | join [
              timeseries total = sum(payment_service_counter_payment_gateway_health_total, default: 0, rate: 1m),
                by: {application},
                interval: 1m
          ], kind: inner, on: {application}, prefix: "total."
          | fieldsAdd success_rate = 100 * success[] / total.total[]
          | fieldsAdd event_name = "Gateway Success Rate < 80%"
          | fieldsAdd entity.name = "payment-processing-payment-service"
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, success_rate, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "8"
        evaluationWindow: "10"
        eventName: "Gateway Success Rate Low"
        dimensionNameProperty: "event_name"
