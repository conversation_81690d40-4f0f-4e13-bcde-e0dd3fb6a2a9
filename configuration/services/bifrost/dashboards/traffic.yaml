---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: bifrost-traffic-dashboard
  labels:
    scope: global
spec:
  name: "Bifrost Traffic"
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.kubernetes_cluster\n| summarize clusters = collectDistinct(entity.name)\n| expand clusters\n| sort clusters",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "BifrostInstance",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries \n{\n  filter: { c_service != \"kong-ingress\"},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {c_service}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "BifrostService",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries \n{\n  filter: { matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {service}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "BifrostRoute",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries \n{\n  filter: { matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {service}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            }
        ],
        "tiles": {
            "0": {
                "title": "Success Rate",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name, c_service }, [\n    timeseries \n    {\n      success = sum(kong_http_requests_total, rollup: avg, default: 0),\n      nonempty: true,\n      filter: { NOT matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n      by: { k8s.cluster.name, c_service },\n      interval: 1m\n    }\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n  ], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1747904703436
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "1": {
                "type": "markdown",
                "content": "**Traffic** \n--- --- \n "
            },
            "2": {
                "title": "5xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "3": {
                "title": "4xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "type": "markdown",
                "content": "**Traffic - By Service** \n--- --- \n "
            },
            "13": {
                "title": "Success Rate",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name, c_service, service}, [\n    timeseries \n    {\n      success = sum(kong_http_requests_total, rollup: avg, default: 0),\n      nonempty: true,\n      filter: { NOT matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n      by: { k8s.cluster.name, c_service, service },\n      interval: 1m\n    }\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n  ], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-green-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "leftYAxisSettings": {
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "successRate"
                                ],
                                "value": "sparkline",
                                "id": 1747904703436
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "14": {
                "title": "5xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "4xx Errors",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service},\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "16": {
                "type": "markdown",
                "content": "**Traffic - By Code** \n--- --- \n "
            },
            "17": {
                "title": "All",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, code },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "blue-moss-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "min": "auto"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979623565
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "18": {
                "title": "4xx",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService) AND matchesValue(code, \"4*\")},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, code },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979186167
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "19": {
                "title": "5xx",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService) AND matchesValue(code, \"5*\")},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, code },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979186167
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "20": {
                "type": "markdown",
                "content": "**Traffic - By Source** \n--- --- \n "
            },
            "21": {
                "title": "All",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, source },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "blue-moss-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979398152
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "22": {
                "title": "4xx",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, source },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979398152
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "23": {
                "title": "5xx",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, source },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979398152
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "24": {
                "type": "markdown",
                "content": "**Health** \n--- --- \n "
            },
            "25": {
                "title": "Unhealthy",
                "type": "data",
                "query": "timeseries \n{\n  filter: {NOT matchesValue(state, \"healthchecks_off\") AND NOT matchesValue(state, \"healthy\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(upstream, $BifrostService)},\n  health = max(kong_upstream_target_health,  default: 0),\n  by: { k8s.cluster.name, upstream, state },\n  interval: 1m\n}\n| filter toLong(health) == 1\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": 1
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "health"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "health"
                                ],
                                "value": "sparkline",
                                "id": 1747980075375
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "26": {
                "type": "markdown",
                "content": "**Latency - Service** \n--- --- \n "
            },
            "27": {
                "title": "P99 Request Latency",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  p99 = percentile(kong_request_latency_ms_latency, 99, rollup: avg, default: 0.001),\n  by: { k8s.cluster.name, c_service, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "purple-rain-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "p99"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "p99",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "28": {
                "title": "P99 Kong Latency",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  p99 = percentile(kong_kong_latency_ms_latency, 99, rollup: avg, default: 0.001),\n  by: { k8s.cluster.name, c_service, service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "p99"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "p99"
                                ],
                                "value": "sparkline",
                                "id": 1747980510462
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "p99",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "29": {
                "type": "markdown",
                "content": "**Traffic - By Route** \n--- --- \n "
            },
            "30": {
                "title": "5xx",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"5*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)AND matchesValue(route, $BifrostRoute)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, route },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979398152
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "31": {
                "title": "4xx",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(code, \"4*\") AND matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService) AND matchesValue(route, $BifrostRoute)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, route },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979398152
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "32": {
                "title": "All",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService) AND matchesValue(service, $BifrostRoute)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, route },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "blue-moss-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747979398152
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": []
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "33": {
                "type": "markdown",
                "content": "**Latency - Route** \n--- --- \n "
            },
            "34": {
                "title": "P99 Kong Latency",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService) AND matchesValue(route, $BifrostRoute)},\n  p99 = percentile(kong_kong_latency_ms_latency, 99, rollup: avg, default: 0.001),\n  by: { k8s.cluster.name, c_service, route },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "red-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "p99"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "p99"
                                ],
                                "value": "sparkline",
                                "id": 1747980510462
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "p99",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "35": {
                "title": "P99 Request Latency",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService) AND matchesValue(route, $BifrostRoute)},\n  p99 = percentile(kong_request_latency_ms_latency, 99, rollup: avg, default: 0.001),\n  by: { k8s.cluster.name, c_service, route },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "purple-rain-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "p99"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "p99"
                                ],
                                "value": "sparkline",
                                "id": 1747981668285
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "p99",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "36": {
                "title": "Traffic",
                "type": "data",
                "query": "timeseries \n{\n  filter: {  matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service },\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "37": {
                "title": "Traffic",
                "type": "data",
                "query": "timeseries \n{\n  filter: { matchesValue(careem_com_kind, \"apigateway\") AND matchesValue(c_service, $BifrostInstance) AND matchesValue(service, $BifrostService)},\n  total = sum(kong_http_requests_total, rollup: avg, default: 0),\n  by: { k8s.cluster.name, c_service, service},\n  interval: 1m\n}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": []
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "orange",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "k8s.cluster",
                            "interval",
                            "requests"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747904989452
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "total",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747904858233
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            }
        },
        "layouts": {
            "0": {
                "x": 0,
                "y": 1,
                "w": 6,
                "h": 5
            },
            "1": {
                "x": 0,
                "y": 0,
                "w": 24,
                "h": 1
            },
            "2": {
                "x": 18,
                "y": 1,
                "w": 6,
                "h": 5
            },
            "3": {
                "x": 12,
                "y": 1,
                "w": 6,
                "h": 5
            },
            "12": {
                "x": 0,
                "y": 6,
                "w": 24,
                "h": 1
            },
            "13": {
                "x": 0,
                "y": 7,
                "w": 6,
                "h": 5
            },
            "14": {
                "x": 18,
                "y": 7,
                "w": 6,
                "h": 5
            },
            "15": {
                "x": 12,
                "y": 7,
                "w": 6,
                "h": 5
            },
            "16": {
                "x": 0,
                "y": 12,
                "w": 24,
                "h": 1
            },
            "17": {
                "x": 0,
                "y": 13,
                "w": 8,
                "h": 5
            },
            "18": {
                "x": 8,
                "y": 13,
                "w": 8,
                "h": 5
            },
            "19": {
                "x": 16,
                "y": 13,
                "w": 8,
                "h": 5
            },
            "20": {
                "x": 0,
                "y": 18,
                "w": 24,
                "h": 1
            },
            "21": {
                "x": 0,
                "y": 19,
                "w": 8,
                "h": 5
            },
            "22": {
                "x": 8,
                "y": 19,
                "w": 8,
                "h": 5
            },
            "23": {
                "x": 16,
                "y": 19,
                "w": 8,
                "h": 5
            },
            "24": {
                "x": 0,
                "y": 42,
                "w": 24,
                "h": 1
            },
            "25": {
                "x": 0,
                "y": 43,
                "w": 24,
                "h": 5
            },
            "26": {
                "x": 0,
                "y": 30,
                "w": 24,
                "h": 1
            },
            "27": {
                "x": 0,
                "y": 31,
                "w": 12,
                "h": 5
            },
            "28": {
                "x": 12,
                "y": 31,
                "w": 12,
                "h": 5
            },
            "29": {
                "x": 0,
                "y": 24,
                "w": 24,
                "h": 1
            },
            "30": {
                "x": 16,
                "y": 25,
                "w": 8,
                "h": 5
            },
            "31": {
                "x": 8,
                "y": 25,
                "w": 8,
                "h": 5
            },
            "32": {
                "x": 0,
                "y": 25,
                "w": 8,
                "h": 5
            },
            "33": {
                "x": 0,
                "y": 36,
                "w": 24,
                "h": 1
            },
            "34": {
                "x": 12,
                "y": 37,
                "w": 12,
                "h": 5
            },
            "35": {
                "x": 0,
                "y": 37,
                "w": 12,
                "h": 5
            },
            "36": {
                "x": 6,
                "y": 1,
                "w": 6,
                "h": 5
            },
            "37": {
                "x": 6,
                "y": 7,
                "w": 6,
                "h": 5
            }
        },
        "importedWithCode": false,
        "settings": {}
    }