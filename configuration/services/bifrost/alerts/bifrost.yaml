---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: bifrost-alerts
  labels:
    service: bifrost
    scope: global
spec:
  anomalies:
    staticThreshold:
      BifrostUpstreamUnhealthy:
        title: "[Bifrost] Upstream unhealthy"
        description: "Health checks for a Bifrost upstream have failed, and the upstream has been marked as unhealthy. This may impact request routing and service availability. Please investigate whether the upstream service is reachable, responding correctly, and functioning as expected."
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries max(kong_upstream_target_health, default: 0), nonempty: true, 
            by: {
              upstream,
              target,
              state,
              careem_com_instance,
              careem_com_name,
              k8s.namespace.name,
              k8s.cluster.name
            },
            filter: {
              matchesValue(state, "dns_error") OR matchesValue(state, "unhealthy")
            }
          | fieldsAdd domain = replaceString(k8s.cluster.name, "prod-", "")
          | fieldsAdd instanceName = replaceString(k8s.namespace.name, "-v2", "")
          | fieldsAdd dtAppId = concat(instanceName, "-", domain)
          | join [
              fetch dt.entity.service
              | filter matchesValue(entity.name, "*bifrost*")
            ],
            on: { left[dtAppId] == right[entity.name] },
            fields: { entity.name, id }
          | fieldsRemove domain, instanceName, careem_com_name, k8s.cluster.name, k8s.namespace.name
        entityIdProperty: "id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        eventName: "[Bifrost] Upstream unhealthy"
        dimensionNameProperty: upstream
