apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: booking-update-service-slo
  labels:
    service: booking-update-service
    scope: global
spec:
  global:
    latency: 4000
  keyRequests:
  - name: booking-bookingid-dropoff
    path: /<apiVersion>/booking/<bookingId>/dropoff
    target: 99.95
    latency: 1250
  - name: unassignment-timestamp-bookingid
    path: /booking/unassignment/timestamp/<bookingId>
    target: 99.95
    latency: 4000
  - name: booking-book-later
    path: /booking/book/later
    target: 99.95
    latency: 4000
  - name: booking-book-now
    path: /booking/book/now
    target: 99.95
    latency: 4000
  - name: booking-book-repeat
    path: /booking/book/repeat
    target: 99.95
    latency: 4000
  - name: v1-deliveries
    path: /v1/deliveries/
    target: 99.95
    latency: 4000
  - name: booking-bookingid-pickup
    path: /booking/<bookingId>/pickup
    target: 99.95
    latency: 4000
  - name: estimates-bookinguid-cancel
    path: /v<apiVersion>/booking/estimates/<bookingUid>/cancel
    target: 99.95
    latency: 2000
  - name: booking-book
    path: /booking/book
    target: 99.95
    latency: 2000
  - name: unassignment-redispatch-bookinguid
    path: /booking/unassignment/redispatch/<bookingUid>
    target: 99.95
    latency: 2000
  - name: update-payment
    path: /update/payment
    target: 99.95
    latency: 400
  - name: booking-bookingid-destinationchange
    path: /<apiVersion>/booking/<bookingId>/destinationChange
    target: 99.95
    latency: 800
  - name: update-bookingdetail
    path: /update/bookingdetail
    target: 99.95
    latency: 100
  failureDetection:
    exceptions:
      ignoredExceptions:
      - classPattern: com.careem.lib.commons.exception.FacadeException
        messagePattern: ''
      - classPattern: com.careem.lib.commons.exception.v2.ServiceException
        messagePattern: ''
      - classPattern: com.careem.lib.commons.exception.v2.GatewayServiceClientException
        messagePattern: ''
