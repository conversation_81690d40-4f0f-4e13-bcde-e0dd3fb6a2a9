---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: arbitration-service-slo
  labels:
    service: arbitration-service
    scope: global
spec:
  global:
    latency: 1073
  keyRequests:
    - name: "arbitration-captain-captainid"
      path: /v1/arbitration/captain/<captainId>
      target: 99.95
      latency: 1073
    - name: "arbitration-customer-customerid"
      path: /v1/arbitration/customer/<customerId>
      target: 99.95
      latency: 1073
    - name: "booking-bookingid-cancellation-waiver"
      path: /v1/arbitration/customer/<customerId>/booking/<bookingId>/cancellation-waiver
      target: 99.95
      latency: 111
    - name: "queryid-identifier-id"
      path: /v1/raptor/multiple/query/<queryId>/identifier/<id>
      target: 99.95
      latency: 111
    - name: "queryid-identifier-id"
      path: /v1/raptor/query/<queryId>/identifier/<id>
      target: 99.95
      latency: 111
    - name: "cancellation-bookingid-waiver"
      path: /v1/arbitration/customer/<customerId>/cancellation/<bookingId>/waiver
      target: 99.95
      latency: 67