---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: business-recharge-service-anomaly-detection
  labels:
    scope: global
    service: business-recharge-service
spec:
  anomalies:
    staticThreshold:
      business-recharge-service-revert-failed:
        title: "[business-recharge-service] Count of REVERT_FAILED entries more than 0 in 1 minute"
        description: "The number of entries to manually revert on STS are more than 0"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries failures = sum(business_recharge_service_revert_failed_total), by:{c_service, name}, interval:1m
          | fieldsAdd entity.name = c_service
          | join [fetch dt.entity.service], prefix:"service.", on:{entity.name}
          | fields interval, timeframe, failures, name, service.id, service.entity.name
        entityIdProperty: "service.id"
        entityNameProperty: "service.entity.name"
        dimensionNameProperty: "name"
        eventType: "ERROR_EVENT"
        eventName: "has failure"
