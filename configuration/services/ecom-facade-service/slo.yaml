---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: ecom-facade-service-slo
  labels:
    service: ecom-facade-service
    scope: global
spec:
  global:
    latency: 100
  keyRequests:
    - name: "merchants"
      path: /v1/merchants 
      target: 99.95
    - name: "merchant-id-items"
      path: /v1/merchant/<id>/items
      target: 99.95
    - name: "merchants-id-items"
      path: /v1/merchants/<id>/items
      target: 99.95
    - name: "merchants-items"
      path: /v1/merchants/items
      target: 99.95
    - name: "tags"
      path: /v1/tags
      target: 99.95
    - name: "merchants-id-root-categories"
      path: /v1/merchants/<id>/root-categories
      target: 99.95
    - name: "merchants-categories"
      path: /v1/merchants/categories
      target: 99.95
    - name: "merchants-id-catalog"
      path: /v1/merchants/<id>/catalog
      target: 99.95
      latency: 1000
  kafka:
    target: 99.95
    groups:
      - name: marketplace
        topics:
          - dynamic-delivery-radius
          - marketplace-health-index
        latency: 0.2
        max_age: 60.0
      - name: catalog-update
        topics:
          - catalog-update
        latency: 0.2
        max_age: 600.0
      - name: hyperstore-feature-updates
        topics:
          - hyperstore-feature-updates
        latency: 30.0
        max_age: 1800.0
      - name: generic-kafka-consumers
        topics:
          - merchant-update
          - area
          - city-operational-hours
          - country
          - city
          - merchant-review
          - merchant-bayesian-rating
          - tag
          - brand
          - ims-item-stock-updates
          - vector-updates
          - merchant-busyness
          - item-query-score
        latency: 0.2
        max_age: 300.0