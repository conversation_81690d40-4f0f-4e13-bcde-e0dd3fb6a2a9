---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: bank-account-management-service-slo
  labels:
    service: bank-account-management-service
    scope: global
spec:
  global:
    latency: 1000
  keyRequests:
    - name: "users-bank-accounts-id"
      path: /users/bank-accounts/<id>
      target: 99.95
      latency: 300
    - name: "users-bank-accounts"
      path: /users/bank-accounts
      target: 99.95
      latency: 300
    - name: "use-banks"
      path: /users/banks
      target: 99.9
      latency: 20
    - name: "user-bank-account-details"
      path: users/bank-accounts/<id>
      target: 99.9
      latency: 200
    - name: "user-bank-account-validate"
      path: /users/bank-accounts/validate
      target: 99.9
      latency: 50
  failureDetection:
    exceptions:
      ignoredExceptions:
        - classPattern: com.careem
    httpErrorCodes: 500-599