---
apiVersion: observability.careem.com/v1
kind: CustomMetrics
metadata:
  name: kamino-metrics
  labels:
    service: kamino
    scope: global
spec:
  metrics:
    - name: "kamino_pipeline_status"
      aggregationLabels:
        - "project"
        - "name"
        - "status"
    - name: "kamino_run_status"
      aggregationLabels:
        - "project"
        - "status"
    - name: "kamino_model_version_status"
      aggregationLabels:
        - "project"
        - "model"
        - "status"
    - name: "kamino_documents_count"
      aggregationLabels:
        - "project"
        - "kind"
    - name: "kamino_servingV2_replicas"
      aggregationLabels:
        - "project"
        - "serving"
    - name: "kamino_servingV2_cpu_limits_total"
      aggregationLabels:
        - "project"
        - "serving"
    - name: "kamino_servingV2_memory_limits_total"
      aggregationLabels:
        - "project"
        - "serving"
    - name: "kamino_pipeline_run_duration"
      aggregationLabels:
        - "project"
        - "name"
    - name: "kamino_calculation_status"
      aggregationLabels:
        - "project"
        - "name"
        - "status"
