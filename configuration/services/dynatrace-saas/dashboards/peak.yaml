---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: plt-marketplace-health-and-peak-peak-dashboard
  labels:
    scope: production
spec:
  name: "[plt-marketplace-health-and-peak] Peak Dashboard"
  json: |
    {
      "version": 18,
      "variables": [
        {
          "key": "namespace",
          "visible": true,
          "type": "query",
          "version": 1,
          "editable": true,
          "input": "fetch dt.entity.kubernetes_cluster\n| filter in(entity.name, \"prod-rh-eks\")\n| join [fetch dt.entity.cloud_application_namespace\n| fieldsAdd clusterId=clustered_by[dt.entity.kubernetes_cluster]\n| filter contains(entity.name, \"peak\")], on:{left[id]==right[clusterId]}, fields:{nsName=entity.name}\n| fields nsName\n| sort nsName",
          "multiple": false,
          "defaultValue": "peak-octopus"
        }
      ],
      "tiles": {
        "0": {
          "title": "Customer Peak API Throughput Baseline ( Seasonality)",
          "type": "data",
          "query": "          timeseries api_throughput = sum(http_request_duration_seconds, rate: 1m, rollup:total),\n           by:{path,c_service}, filter:{c_service==\"peak-octopus\"} | filter {path==\"/api/v1/domain/RIDE_HAILING/mp-customer-view/cctId/#val/customer/#val\"}\n            | fieldsAdd entity.name = c_service\n            | join [fetch dt.entity.cloud_application], kind:leftOuter, prefix:\"dt_application.\", on:{entity.name}\n            | fields timeframe, interval, api_throughput, dt_application.id, dt_application.entity.name, c_service",
          "visualization": "davis",
          "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "dt_application.entity",
                "interval",
                "api_throughput"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "api_throughput"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "api_throughput"
                  ],
                  "value": "sparkline",
                  "id": 1745471186476
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": true,
            "componentState": {
              "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.SeasonalBaselineAnomalyDetectionAnalyzer",
              "inputData": {
                "dt.statistics.ui.anomaly_detection.SeasonalBaselineAnomalyDetectionAnalyzer": {
                  "generalParameters": {
                    "timeframe": {
                      "startTime": "2025-05-07T09:10:33.138Z",
                      "endTime": "2025-05-07T11:10:33.138Z"
                    },
                    "resolveDimensionalQueryData": true,
                    "logVerbosity": "INFO"
                  },
                  "query": "          timeseries api_throughput = sum(http_request_duration_seconds, rate: 1m, rollup:total),\n           by:{path,c_service}, filter:{c_service==\"peak-octopus\"} | filter {path==\"/api/v1/domain/RIDE_HAILING/mp-customer-view/cctId/#val/customer/#val\"}\n            | fieldsAdd entity.name = c_service\n            | join [fetch dt.entity.cloud_application], kind:leftOuter, prefix:\"dt_application.\", on:{entity.name}\n            | fields timeframe, interval, api_throughput, dt_application.id, dt_application.entity.name, c_service",
                  "tolerance": 2,
                  "alertCondition": "OUTSIDE",
                  "alertOnMissingData": false,
                  "violatingSamples": 3,
                  "slidingWindow": 5,
                  "dealertingSamples": 5
                }
              },
              "analyzerHints": {}
            },
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "1": {
          "title": "Captain Peak API Throughput Baseline ( Seasonality)",
          "type": "data",
          "query": "          timeseries api_throughput = sum(http_request_duration_seconds, rate: 1m, rollup:total),\n           by:{path,c_service}, filter:{c_service==\"peak-octopus\"} | filter {path==\"/api/v1/domain/RIDE_HAILING/mp-captain-view/captain/#val\"}\n            | fieldsAdd entity.name = c_service\n            | join [fetch dt.entity.cloud_application], kind:leftOuter, prefix:\"dt_application.\", on:{entity.name}\n            | fields timeframe, interval, api_throughput, dt_application.id, dt_application.entity.name, c_service",
          "visualization": "davis",
          "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "api_throughput"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "dt_application.entity",
                "interval",
                "api_throughput"
              ],
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "api_throughput"
                  ],
                  "value": "sparkline",
                  "id": 1745471458476
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": true,
            "componentState": {
              "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.SeasonalBaselineAnomalyDetectionAnalyzer",
              "inputData": {
                "dt.statistics.ui.anomaly_detection.SeasonalBaselineAnomalyDetectionAnalyzer": {
                  "generalParameters": {
                    "timeframe": {
                      "startTime": "2025-05-07T09:10:25.270Z",
                      "endTime": "2025-05-07T11:10:25.271Z"
                    },
                    "resolveDimensionalQueryData": true,
                    "logVerbosity": "INFO"
                  },
                  "query": "          timeseries api_throughput = sum(http_request_duration_seconds, rate: 1m, rollup:total),\n           by:{path,c_service}, filter:{c_service==\"peak-octopus\"} | filter {path==\"/api/v1/domain/RIDE_HAILING/mp-captain-view/captain/#val\"}\n            | fieldsAdd entity.name = c_service\n            | join [fetch dt.entity.cloud_application], kind:leftOuter, prefix:\"dt_application.\", on:{entity.name}\n            | fields timeframe, interval, api_throughput, dt_application.id, dt_application.entity.name, c_service",
                  "tolerance": 2,
                  "alertCondition": "OUTSIDE",
                  "alertOnMissingData": false,
                  "violatingSamples": 3,
                  "slidingWindow": 5,
                  "dealertingSamples": 5
                }
              },
              "analyzerHints": {}
            },
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "2": {
          "title": "Octopus tilemap fallbacks",
          "type": "data",
          "query": "          timeseries interval: 1m, count = sum(octopus_tilemap_lookups),\n          by: {app,success, c_service}\n          | summarize {\n            failure = sum(if( success == \"false\", count[], else: count[]*0)),\n            total = sum(count[])\n            },\n            by: {timeframe, interval, app,c_service  }  \n           | fieldsAdd failure_over_5m = arrayMovingSum(failure, 5),\n                      total_5m   = arrayMovingSum(total, 5)\n          | fieldsAdd failure_rate_over_5m = (failure_over_5m[] / total_5m[] ) * 100\n          | fieldsAdd entity.name = c_service\n          | join [fetch dt.entity.cloud_application], kind:leftOuter, prefix:\"dt_application.\", on:{entity.name}\n          | fields timeframe, interval, failure_rate_over_5m",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "fireplace",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "dt_application.entity",
                "interval",
                "failure_rate_over_5m"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "failure_rate_over_5m"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": ""
              },
              "seriesOverrides": [
                {
                  "seriesId": [
                    "failure_rate_over_5m"
                  ],
                  "override": {
                    "color": "#b3007d"
                  }
                }
              ]
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "failure_rate_over_5m"
                  ],
                  "value": "sparkline",
                  "id": 1745471816962
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 13
            },
            "unitsOverrides": [
              {
                "identifier": "failure_rate_over_5m",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": "percent",
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1745486195891
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "3": {
          "title": "Peak Worker Throughput (K8s)",
          "type": "data",
          "query": "          timeseries value_success=sum(peak_worker_job_success, rate : 1m), by:{c_service},\n          filter: {c_service==\"peak-worker\"} , interval: 1m , value_failure=sum(peak_worker_job_failed)\n          | fieldsAdd value_success_3m=arrayMovingSum(value_success,3)\n          | fieldsAdd value_failed_3m=arrayMovingSum(value_failure,3)\n          | fields timeframe, interval, value_success_3m, value_failed_3m",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "log-level",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "bandChartSettings": {
                "lower": "value_success_3m",
                "upper": "value_failed_3m"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "value_success_3m",
                  "value_failed_3m"
                ]
              },
              "leftYAxisSettings": {},
              "seriesOverrides": [
                {
                  "seriesId": [
                    "value_failed_3m"
                  ],
                  "override": {
                    "color": {
                      "Default": "var(--dt-colors-charts-loglevel-emergency-default, #ae132d)"
                    }
                  }
                },
                {
                  "seriesId": [
                    "value_success_3m"
                  ],
                  "override": {
                    "color": {
                      "Default": "var(--dt-colors-charts-apdex-good-default, #1c520a)"
                    }
                  }
                }
              ]
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "value_success_3m",
                    "value_failed_3m"
                  ],
                  "value": "sparkline",
                  "id": 1745472033835
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "4": {
          "title": "",
          "type": "data",
          "query": "          timeseries value_success=sum(peak_worker_dynamic_peak_executions, rate : 1m), by:{peak_function,c_service},\n          filter: {c_service==\"peak-worker\"} , interval: 1m \n          | fieldsAdd value_success_3m=arrayMovingSum(value_success,3)\n          | fieldsAdd peak_function\n          | fields timeframe, interval, value_success_3m , peak_function",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "value_success_3m"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "value_success_3m"
                  ],
                  "value": "sparkline",
                  "id": *********9184
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "5": {
          "title": "PeakV2 data availability",
          "type": "data",
          "query": "         timeseries missing_cell_peak_data=sum(peak_worker_missing_cycle_peak_data, rate : 1m), by:{c_service},\n          filter: {c_service==\"peak-worker\"}\n          | fieldsAdd entity.name = c_service\n          | join [fetch dt.entity.cloud_application], kind:leftOuter, prefix:\"dt_application.\", on:{entity.name}\n          | fields timeframe, interval, missing_cell_peak_data, dt_application.id, dt_application.entity.name, c_service",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "missing_cell_peak_data"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {},
              "seriesOverrides": [
                {
                  "seriesId": [
                    "CLOUD_APPLICATION-34649BFD1056452F",
                    "peak-worker",
                    "peak-worker"
                  ],
                  "override": {
                    "color": {
                      "Default": "var(--dt-colors-charts-categorical-color-06-default, #a9780f)"
                    }
                  }
                }
              ]
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "missing_cell_peak_data"
                  ],
                  "value": "sparkline",
                  "id": 1745472541895
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "6": {
          "title": "Missing cycle data (service_area, cct)",
          "type": "data",
          "query": "timeseries value=sum(peak_worker_missing_cycle_peak_data, rate : 1m), by:{cct,service_area,c_service},\nfilter: {c_service==\"peak-worker\"} , interval: 1m \n| fieldsAdd value_2m=arrayMovingSum(value,2), cct,service_area\n| fields timeframe, interval, value_2m , service_area,cct",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "value_2m"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value_success_3m"
              ],
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "value_2m"
                  ],
                  "value": "sparkline",
                  "id": 1745472823616
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "7": {
          "title": "Redis Write Failures By Worker",
          "type": "data",
          "query": "          timeseries value=sum(peak_worker_redis_write_failure, rate : 1m)\n          | fields timeframe, interval, value",
          "visualization": "recordView",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle"
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "8": {
          "title": "Data freshness",
          "type": "data",
          "query": "timeseries avg(peak_worker_result_peak_data_freshness) \n ",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "avg(peak_worker_result_peak_data_freshness)"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "peak_worker_result_peak_data_freshness"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "9": {
          "title": "APM",
          "type": "data",
          "query": "timeseries api_throughput = sum(http_request_duration_seconds, rate: 1m, rollup:total),\nby:{path,c_service}\n| filter c_service==$namespace\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "api_throughput"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "api_throughput"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "api_throughput"
                  ],
                  "value": "sparkline",
                  "id": 1745473540004
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 16
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "10": {
          "title": "Response status",
          "type": "data",
          "query": "timeseries api_throughput = sum(http_request_duration_seconds, rate: 1m, rollup:total),\nby:{status_code,c_service}\n| filter c_service==$namespace\n ",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "api_throughput"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "api_throughput"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "api_throughput"
                  ],
                  "value": "sparkline",
                  "id": 1745473854840
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "12": {
          "title": "Response time p99",
          "type": "data",
          "query": "timeseries response_time = avg(http_request_duration_seconds_latency, rate: 1m), \nby:{c_service,path,percentile}, filter:{c_service==$namespace}\n| filter percentile == \"99\"\n| fieldsAdd entity.name = c_service\n| fieldsAdd response_time_ms = response_time[]*1000\n| join [fetch dt.entity.cloud_application], kind:leftOuter, prefix:\"dt_application.\", on:{entity.name}\n| fields timeframe, interval, response_time_ms,path",
          "visualization": "lineChart",
          "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "response_time_ms"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "response time ms"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "response_time_ms"
                  ],
                  "value": "sparkline",
                  "id": 1746616491679
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-7d",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "13": {
          "title": "",
          "description": "Number of pods",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { count(dt.kubernetes.pods), value.A = avg(dt.kubernetes.pods, scalar: true) }, filter: { matchesValue(entityAttr(dt.entity.cloud_application_namespace, \"entity.name\"), $namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "autoSelectVisualization": true,
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxis": [
                  "interval",
                  "value.A"
                ],
                "valueAxis": [
                  "value.A"
                ],
                "categoryAxisLabel": "interval,value.A",
                "valueAxisLabel": "value.A",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "count(dt.kubernetes.pods)"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Kubernetes: Pod count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "value.A",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "count(dt.kubernetes.pods)",
              "autoscale": true,
              "sparklineSettings": {
                "record": "count(dt.kubernetes.pods)",
                "variant": "area",
                "lineType": "smooth"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": false,
                "trendField": "value.A",
                "isRelative": false,
                "isLabelVisible": false,
                "label": "",
                "upward": {
                  "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                },
                "downward": {
                  "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                },
                "neutral": {
                  "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                }
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "count(dt.kubernetes.pods)"
                  ],
                  "value": "sparkline",
                  "id": 1745486672380
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {
                "value": "value.A"
              },
              "displayedFields": [
                "interval",
                "value.A"
              ],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "blue"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "value.A",
                  "rangeAxis": ""
                },
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "interval",
                "value.A"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "dataMapping": {
              "value": "value.A",
              "dimension": "interval",
              "displayedFields": [
                "interval",
                "value.A"
              ],
              "latitude": "value.A",
              "longitude": "interval",
              "latitudeList": "count(dt.kubernetes.pods)"
            },
            "label": {
              "showLabel": true,
              "label": "value.A"
            },
            "unitsOverrides": [],
            "legend": {
              "textTruncationMode": "middle",
              "showLegend": true,
              "position": "auto"
            },
            "tooltip": {
              "showCustomFields": false
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.3.1",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.pods",
                  "aggregation": "count"
                },
                "filter": "dt.entity.cloud_application_namespace.name = $namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "14": {
          "title": "Pod restart",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { sum(dt.kubernetes.container.restarts), value.A = avg(dt.kubernetes.container.restarts, scalar: true) }, filter: { matchesValue(entityAttr(dt.entity.cloud_application_namespace, \"entity.name\"), $namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "sum(dt.kubernetes.container.restarts)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Kubernetes: Container - restart count"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "sum(dt.kubernetes.container.restarts)"
                  ],
                  "value": "sparkline",
                  "id": 1745487108504
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.3.1",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.restarts",
                  "aggregation": "sum"
                },
                "filter": "dt.entity.cloud_application_namespace.name = $namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        }
      },
      "layouts": {
        "0": {
          "x": 0,
          "y": 0,
          "w": 11,
          "h": 6
        },
        "1": {
          "x": 11,
          "y": 0,
          "w": 13,
          "h": 6
        },
        "2": {
          "x": 0,
          "y": 6,
          "w": 24,
          "h": 5
        },
        "3": {
          "x": 0,
          "y": 11,
          "w": 24,
          "h": 5
        },
        "4": {
          "x": 0,
          "y": 16,
          "w": 23,
          "h": 7
        },
        "5": {
          "x": 0,
          "y": 23,
          "w": 23,
          "h": 6
        },
        "6": {
          "x": 0,
          "y": 29,
          "w": 24,
          "h": 7
        },
        "7": {
          "x": 14,
          "y": 36,
          "w": 10,
          "h": 7
        },
        "8": {
          "x": 0,
          "y": 36,
          "w": 14,
          "h": 7
        },
        "9": {
          "x": 0,
          "y": 43,
          "w": 23,
          "h": 6
        },
        "10": {
          "x": 0,
          "y": 49,
          "w": 23,
          "h": 6
        },
        "12": {
          "x": 0,
          "y": 55,
          "w": 24,
          "h": 6
        },
        "13": {
          "x": 0,
          "y": 61,
          "w": 21,
          "h": 6
        },
        "14": {
          "x": 0,
          "y": 67,
          "w": 24,
          "h": 6
        }
      },
      "importedWithCode": false,
      "settings": {}
    }
