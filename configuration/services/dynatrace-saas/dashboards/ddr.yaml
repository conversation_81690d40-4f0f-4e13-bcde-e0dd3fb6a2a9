---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: plt-marketplace-health-and-peak-ddr-dashboard
  labels:
    scope: production
spec:
  name: "[plt-marketplace-health-and-peak] DDR Dashboard"
  json: |
    {
        "version":18,
        "variables":[
          {
             "key":"Namespace",
             "visible":true,
             "type":"query",
             "version":1,
             "editable":true,
             "input":"fetch dt.entity.kubernetes_cluster\n| filter in(entity.name, \"data-prod-rh-eks\")\n| join [fetch dt.entity.cloud_application_namespace\n| fieldsAdd clusterId=clustered_by[dt.entity.kubernetes_cluster]\n| filter contains(entity.name, \"mp-health-mot-ddr\")], on:{left[id]==right[clusterId]}, fields:{nsName=entity.name}\n| fields nsName\n| sort nsName",
             "multiple":false
          }
        ],
        "tiles":{
          "16":{
             "type":"markdown",
             "content":"# MHI Common"
          },
          "38":{
             "type":"markdown",
             "content":"# Job Metrics"
          },
          "42":{
             "title":"Checkpoints Completed",
             "description":"",
             "type":"data",
             "query":"timeseries {\n    completed = avg(flink_jobmanager_job_numberOfCompletedCheckpoints, rate:1m), interval:5m,\n    inProgress = avg(flink_jobmanager_job_numberOfInProgressCheckpoints, rate:1m),\n    failed = avg(flink_jobmanager_job_numberOfFailedCheckpoints, rate:1m)\n}, by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd Completed = (arrayDelta(completed)[])\n| fieldsAdd `In Progress` = (arrayDelta(inProgress)[])\n| fieldsAdd Failed = (arrayDelta(failed)[])\n| fields interval, timeframe, Completed, `In Progress`, Failed\n\n",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "xAxisScaling":"auto",
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"smooth",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxisLabel":"interval",
                      "valueAxisLabel":"interval",
                      "tooltipVariant":"single"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
        
                   ],
                   "leftYAxisSettings":{
                      "isLabelVisible":false,
                      "label":"Service request count"
                   },
                   "seriesOverrides":[
        
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "Completed",
                         "In Progress",
                         "Failed"
                      ]
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "bandChartSettings":{
                      "lower":"Completed",
                      "upper":"In Progress"
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
                   "prefixIcon":"",
                   "recordField":"flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
                   "autoscale":true,
                   "alignment":"center",
                   "trend":{
                      "trendType":"auto",
                      "isVisible":true
                   },
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
                      "[\"k8s.pod.name\"]":313.328125
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "In Progress",
                            "Failed"
                         ],
                         "value":"sparkline",
                         "id":1746123209992
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto"
                   },
                   "displayedFields":[
        
                   ],
                   "dataMappings":{
        
                   },
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":"auto",
                   "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
                      {
                         "valueAxis":"interval",
                         "rangeAxis":""
                      }
                   ],
                   "variant":"single",
                   "truncationMode":"middle",
                   "displayedFields":[
        
                   ]
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":false,
                "unitsOverrides":[
                   {
                      "identifier":"Completed",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":true,
                      "added":1736342780869
                   },
                   {
                      "identifier":"In Progress",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":true,
                      "added":1736801237253
                   },
                   {
                      "identifier":"Failed",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":true,
                      "added":1736801238775
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
        
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-2h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             }
          },
          "46":{
             "type":"markdown",
             "content":"# System Resources"
          },
          "59":{
             "title":"Task Backpressure",
             "description":"Indicates if there is a backpressure on a specific task. 1 indicates backpressure and 0 indicates no backpressure.",
             "type":"data",
             "query":"timeseries cycles = max(flink_taskmanager_job_task_isBackPressured, rate:1m), by: {k8s.namespace.name, task_name}\n| filter k8s.namespace.name == $Namespace\n| fields interval, timeframe, cycles, task_name",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "xAxisScaling":"analyzedTimeframe",
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"smooth",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxisLabel":"interval",
                      "valueAxisLabel":"interval",
                      "tooltipVariant":"single"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
        
                   ],
                   "leftYAxisSettings":{
                      "isLabelVisible":false,
                      "label":"Service request count"
                   },
                   "seriesOverrides":[
        
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "cycles"
                      ]
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
                   "prefixIcon":"",
                   "recordField":"flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
                   "autoscale":true,
                   "alignment":"center",
                   "trend":{
                      "trendType":"auto",
                      "isVisible":true
                   },
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
                      "[\"k8s.pod.name\"]":313.328125
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "cycles"
                         ],
                         "value":"sparkline",
                         "id":1737379738673
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto"
                   },
                   "displayedFields":[
                      "task_name"
                   ],
                   "dataMappings":{
                      "value":"task_name"
                   },
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":"auto",
                   "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
                      {
                         "valueAxis":"interval",
                         "rangeAxis":""
                      }
                   ],
                   "variant":"single",
                   "truncationMode":"middle",
                   "displayedFields":[
        
                   ]
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"rate",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_second",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":true,
                      "added":1736340564248
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
        
             }
          },
          "66":{
             "title":"Job Manager Memory",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries { max(dt.runtime.jvm.memory_pool.max), value.A = avg(dt.runtime.jvm.memory_pool.max, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries { max(dt.runtime.jvm.memory_pool.used), value.B = avg(dt.runtime.jvm.memory_pool.used, scalar: true) },\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)`, value.B }",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"start",
                   "bandChartSettings":{
                      "lower":"max(dt.runtime.jvm.memory_pool.max)",
                      "upper":"max(dt.runtime.jvm.memory_pool.used)"
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "max(dt.runtime.jvm.memory_pool.max)",
                         "max(dt.runtime.jvm.memory_pool.used)"
                      ]
                   },
                   "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"JVM heap memory max bytes"
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "max(dt.runtime.jvm.memory_pool.max)"
                         ],
                         "value":"sparkline",
                         "id":1746013715838
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.runtime.jvm.memory_pool.max",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace  dt.entity.host.name != *taskmanager* "
                   },
                   {
                      "id":"B",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.runtime.jvm.memory_pool.used",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace dt.entity.host.name != *taskmanager*"
                   }
                ]
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-30m",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             }
          },
          "67":{
             "title":"Task Manager Memory",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries { max(dt.runtime.jvm.memory_pool.max), value.A = avg(dt.runtime.jvm.memory_pool.max, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries { max(dt.runtime.jvm.memory_pool.used), value.B = avg(dt.runtime.jvm.memory_pool.used, scalar: true) },\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)`, value.B }",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"start",
                   "bandChartSettings":{
                      "lower":"max(dt.runtime.jvm.memory_pool.max)",
                      "upper":"max(dt.runtime.jvm.memory_pool.used)"
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "max(dt.runtime.jvm.memory_pool.max)",
                         "max(dt.runtime.jvm.memory_pool.used)"
                      ]
                   },
                   "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"JVM heap memory max bytes"
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "max(dt.runtime.jvm.memory_pool.used)"
                         ],
                         "value":"sparkline",
                         "id":1746122857447
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.runtime.jvm.memory_pool.max",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace dt.entity.host.name = *taskmanager* "
                   },
                   {
                      "id":"B",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.runtime.jvm.memory_pool.used",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace dt.entity.host.name = *taskmanager*"
                   }
                ]
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "76":{
             "title":"Task Manager CPU Usage",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries max(dt.kubernetes.container.requests_cpu), interval: 5m, filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.pod.name, \"*taskmanager*\") }\n| join [\n    timeseries max(dt.kubernetes.container.cpu_usage),\n    interval: 5m,\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.pod.name, \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.kubernetes.container.cpu_usage)` }\n| fieldsAdd C = (`max(dt.kubernetes.container.cpu_usage)`[]/`max(dt.kubernetes.container.requests_cpu)`[])*100\n| fieldsRemove `max(dt.kubernetes.container.requests_cpu)`\n| fieldsRemove `max(dt.kubernetes.container.cpu_usage)`",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "C"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"Max CPU Usage",
                      "max":100
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "C"
                         ],
                         "value":"sparkline",
                         "id":1746608379890
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":false,
                "unitsOverrides":[
                   {
                      "identifier":"C",
                      "unitCategory":"percentage",
                      "baseUnit":"percent",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746607779471
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":false,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.kubernetes.container.requests_cpu",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace k8s.pod.name = *taskmanager*"
                   },
                   {
                      "id":"B",
                      "isEnabled":false,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.kubernetes.container.cpu_usage",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace k8s.pod.name = *taskmanager*"
                   },
                   {
                      "id":"C",
                      "isEnabled":true,
                      "datatype":"expression",
                      "expression":"(B/A)*100"
                   }
                ],
                "globalCommands":{
                   "interval":"5m"
                }
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "77":{
             "title":"Jobs Running",
             "description":"",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries { min(flink_jobmanager_numRunningJobs), value.A = avg(flink_jobmanager_numRunningJobs, scalar: true) }, interval: 5m, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "min(flink_jobmanager_numRunningJobs)"
                      ]
                   },
                   "leftYAxisSettings":{
                      "isLabelVisible":false,
                      "label":""
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "min(flink_jobmanager_numRunningJobs)"
                         ],
                         "value":"sparkline",
                         "id":1746610876287
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"flink_jobmanager_numRunningJobs",
                         "aggregation":"min"
                      },
                      "filter":"k8s.namespace.name = $Namespace "
                   }
                ],
                "globalCommands":{
                   "interval":"5m"
                }
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-24h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             }
          },
          "78":{
             "title":"Restarts Over Time",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries { max(flink_jobmanager_job_fullRestarts), value.A = avg(flink_jobmanager_job_fullRestarts, scalar: true) }, interval: 5m, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "max(flink_jobmanager_job_fullRestarts)"
                      ]
                   },
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "max(flink_jobmanager_job_fullRestarts)"
                         ],
                         "value":"sparkline",
                         "id":1746057407897
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"flink_jobmanager_job_fullRestarts",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace "
                   }
                ],
                "globalCommands":{
                   "interval":"5m"
                }
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "79":{
             "title":"",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries { min(flink_jobmanager_job_fullRestarts), value.A = avg(flink_jobmanager_job_fullRestarts, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
             "visualization":"singleValue",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "min(flink_jobmanager_job_fullRestarts)"
                      ]
                   },
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"Restarts",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "recordField":"value.A",
                   "autoscale":true,
                   "sparklineSettings":{
                      "record":"avg(flink_jobmanager_job_fullRestarts)"
                   },
                   "alignment":"center",
                   "trend":{
                      "trendType":"auto",
                      "isVisible":true
                   },
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "min(flink_jobmanager_job_fullRestarts)"
                         ],
                         "value":"sparkline",
                         "id":1748224104876
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":false,
                "dataMapping":{
                   "value":"value.A"
                },
                "label":{
                   "showLabel":true,
                   "label":"value.A"
                },
                "unitsOverrides":[
                   {
                      "identifier":"value.A",
                      "unitCategory":"amount",
                      "baseUnit":"one",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748224112245
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"flink_jobmanager_job_fullRestarts",
                         "aggregation":"min"
                      },
                      "filter":"k8s.namespace.name = $Namespace "
                   }
                ]
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "80":{
             "title":"",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries { min(flink_jobmanager_numRunningJobs), value.A = avg(flink_jobmanager_numRunningJobs, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
             "visualization":"singleValue",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "min(flink_jobmanager_numRunningJobs)"
                      ]
                   },
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"Jobs Running",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "recordField":"value.A",
                   "autoscale":true,
                   "sparklineSettings":{
                      "record":"max(flink_jobmanager_numRunningJobs)"
                   },
                   "alignment":"center",
                   "trend":{
                      "trendType":"auto",
                      "isVisible":true
                   },
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "min(flink_jobmanager_numRunningJobs)"
                         ],
                         "value":"sparkline",
                         "id":1746615506931
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":false,
                "unitsOverrides":[
                   {
                      "identifier":"value.A",
                      "unitCategory":"amount",
                      "baseUnit":"one",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746058049868
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"flink_jobmanager_numRunningJobs",
                         "aggregation":"min"
                      },
                      "filter":"k8s.namespace.name = $Namespace "
                   }
                ]
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-24h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             }
          },
          "92":{
             "title":"",
             "type":"data",
             "query":"timeseries max(flink_jobmanager_job_uptime), filter: { matchesValue(k8s.namespace.name, $Namespace) }\n| fieldsAdd B = `max(flink_jobmanager_job_uptime)`[]/3600000\n| fieldsAdd value.B = arrayLast(`B`)\n| fieldsRemove `max(flink_jobmanager_job_uptime)`",
             "visualization":"singleValue",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "B"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "recordField":"value.B",
                   "autoscale":true,
                   "sparklineSettings":{
                      "record":"B"
                   },
                   "alignment":"center",
                   "trend":{
                      "trendType":"auto",
                      "isVisible":false,
                      "label":"",
                      "upward":{
                         "Default":"var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                      },
                      "downward":{
                         "Default":"var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                      },
                      "neutral":{
                         "Default":"var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                      },
                      "isRelative":true,
                      "isLabelVisible":false
                   },
                   "colorThresholdTarget":"value",
                   "label":"Uptime Since Last Deployment"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "B"
                         ],
                         "value":"sparkline",
                         "id":1746186241300
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":false,
                "dataMapping":{
                   "value":"value.B"
                },
                "label":{
                   "showLabel":true,
                   "label":"Uptime Since Last Deployment"
                },
                "unitsOverrides":[
                   {
                      "identifier":"value.B",
                      "unitCategory":"time",
                      "baseUnit":"hour",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746199873364
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "93":{
             "title":"Job Manager CPU Usage",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries max(dt.kubernetes.container.requests_cpu), interval: 5m, filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(k8s.pod.name, \"*taskmanager*\") }\n| join [\n    timeseries max(dt.kubernetes.container.cpu_usage),\n    interval: 5m,\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(k8s.pod.name, \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.kubernetes.container.cpu_usage)` }\n| fieldsAdd C = (`max(dt.kubernetes.container.cpu_usage)`[]/`max(dt.kubernetes.container.requests_cpu)`[])*100\n| fieldsRemove `max(dt.kubernetes.container.requests_cpu)`\n| fieldsRemove `max(dt.kubernetes.container.cpu_usage)`",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "C"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"Max CPU Usage",
                      "min":"auto",
                      "max":100
                   },
                   "seriesOverrides":[
        
                   ],
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "C"
                         ],
                         "value":"sparkline",
                         "id":1746608379842
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":false,
                "unitsOverrides":[
                   {
                      "identifier":"C",
                      "unitCategory":"percentage",
                      "baseUnit":"percent",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746607563134
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":false,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.kubernetes.container.requests_cpu",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace  k8s.pod.name != *taskmanager*"
                   },
                   {
                      "id":"B",
                      "isEnabled":false,
                      "datatype":"metrics",
                      "metric":{
                         "key":"dt.kubernetes.container.cpu_usage",
                         "aggregation":"max"
                      },
                      "filter":"k8s.namespace.name = $Namespace k8s.pod.name != *taskmanager*"
                   },
                   {
                      "id":"C",
                      "isEnabled":true,
                      "datatype":"expression",
                      "expression":"(B/A)*100"
                   }
                ],
                "globalCommands":{
                   "interval":"5m"
                }
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "102":{
             "title":"Checkpoint Size",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries { avg(flink_jobmanager_job_lastCheckpointSize), value.A = avg(flink_jobmanager_job_lastCheckpointSize, scalar: true) }, interval: 5m, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "avg(flink_jobmanager_job_lastCheckpointSize)"
                      ]
                   },
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "avg(flink_jobmanager_job_lastCheckpointSize)"
                         ],
                         "value":"sparkline",
                         "id":1746603800529
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"avg(flink_jobmanager_job_lastCheckpointSize)",
                      "unitCategory":"data",
                      "baseUnit":"byte",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746603933960
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                         "key":"flink_jobmanager_job_lastCheckpointSize",
                         "aggregation":"avg"
                      },
                      "filter":"k8s.namespace.name = $Namespace "
                   }
                ],
                "globalCommands":{
                   "interval":"5m"
                }
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "103":{
             "title":"Flink Heap Usage",
             "type":"data",
             "query":"timeseries `max(dt.runtime.jvm.memory_pool.max)·A` = max(dt.runtime.jvm.memory_pool.max), filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.used)·B` = max(dt.runtime.jvm.memory_pool.used),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)·B` }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.max)·D` = max(dt.runtime.jvm.memory_pool.max),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.max)·D` }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.used)·E` = max(dt.runtime.jvm.memory_pool.used),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)·E` }\n| fieldsAdd `Job Manager` = (`max(dt.runtime.jvm.memory_pool.used)·B`[]/`max(dt.runtime.jvm.memory_pool.max)·A`[])*100\n| fieldsAdd `Task Manager` = (`max(dt.runtime.jvm.memory_pool.used)·E`[]/`max(dt.runtime.jvm.memory_pool.max)·D`[])*100\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.max)·A`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.used)·B`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.max)·D`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.used)·E`",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "bandChartSettings":{
                      "lower":"Job Manager",
                      "upper":"Task Manager"
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "Job Manager",
                         "Task Manager"
                      ]
                   },
                   "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"Heap Usage",
                      "max":100
                   },
                   "rightYAxisSettings":{
                      "isLabelVisible":false
                   },
                   "tooltip":{
                      "seriesDisplayMode":"single-line"
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "Job Manager",
                            "Task Manager"
                         ],
                         "value":"sparkline",
                         "id":1746605767648
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"Job Manager",
                      "unitCategory":"percentage",
                      "baseUnit":"percent",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":true,
                      "added":1746015971389
                   },
                   {
                      "identifier":"Task Manager",
                      "unitCategory":"percentage",
                      "baseUnit":"percent",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746016013552
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-30m",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             }
          },
          "104":{
             "title":"Data Freshness",
             "description":"Data Freshness",
             "type":"data",
             "subType":"dql-builder-metrics",
             "query":"timeseries max(flink_taskmanager_job_task_operator_currentOutputWatermark), by: { operator_name }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(operator_name, \"*KAFKA_*\") AND NOT matchesValue(operator_name, \"Source*\") AND NOT matchesValue(operator_name, \"*_Committer*\") }\n| join [\n    timeseries min(flink_taskmanager_job_task_operator_currentOutputWatermark),\n    by: { operator_name },\n    filter: {\n        matchesValue(k8s.namespace.name, $Namespace) AND\n        matchesValue(operator_name, \"*KAFKA_*\") AND\n        NOT matchesValue(operator_name, \"Source*\") AND\n        NOT matchesValue(operator_name, \"*_Committer*\")\n    }\n  ], on: { operator_name }, fields: { `min(flink_taskmanager_job_task_operator_currentOutputWatermark)` }\n| fieldsAdd C = (`max(flink_taskmanager_job_task_operator_currentOutputWatermark)`[]-`min(flink_taskmanager_job_task_operator_currentOutputWatermark)`[]) / 1000\n| fieldsRemove `max(flink_taskmanager_job_task_operator_currentOutputWatermark)`\n| fieldsRemove `min(flink_taskmanager_job_task_operator_currentOutputWatermark)`",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"end",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "C"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "value.A"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "tooltip":{
                      "variant":"single",
                      "seriesDisplayMode":"single-line"
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "C"
                         ],
                         "value":"sparkline",
                         "id":1746711369296
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"C",
                      "unitCategory":"angle",
                      "baseUnit":"second",
                      "displayUnit":"minute",
                      "decimals":0,
                      "suffix":"Min",
                      "delimiter":false,
                      "added":1746711567880
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "queryConfig":{
                "version":"13.5.2",
                "subQueries":[
                   {
                      "id":"A",
                      "isEnabled":false,
                      "datatype":"metrics",
                      "metric":{
                         "key":"flink_taskmanager_job_task_operator_currentOutputWatermark",
                         "aggregation":"max"
                      },
                      "by":[
                         "operator_name"
                      ],
                      "filter":"k8s.namespace.name = $Namespace operator_name = *KAFKA_* operator_name  != \"Source*\" operator_name  != \"*_Committer*\""
                   },
                   {
                      "id":"B",
                      "isEnabled":false,
                      "datatype":"metrics",
                      "metric":{
                         "key":"flink_taskmanager_job_task_operator_currentOutputWatermark",
                         "aggregation":"min"
                      },
                      "by":[
                         "operator_name"
                      ],
                      "filter":"k8s.namespace.name = $Namespace operator_name = *KAFKA_*operator_name  != \"Source*\" operator_name  != \"*_Committer*\""
                   },
                   {
                      "id":"C",
                      "isEnabled":true,
                      "datatype":"expression",
                      "expression":"(A-B) / 1000"
                   }
                ],
                "globalCommands":{
        
                }
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "105":{
             "type":"markdown",
             "content":"# Mot DDR"
          },
          "109":{
             "title":"Zone radius",
             "type":"data",
             "query":"timeseries radius=avg(flink_taskmanager_job_task_operator_service_area_id_zone_id_mp_health_zone_radius)\n, filter: k8s.namespace.name == $Namespace\n, by:{service_area_id,zone_id}\n, interval:1m",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "radius"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "radius"
                      ]
                   },
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "radius"
                         ],
                         "value":"sparkline",
                         "id":1746791451942
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "110":{
             "title":"Supply Shortage Per Service Aread - food - assuming 1 order per 1 captain",
             "type":"data",
             "query":"timeseries demand=avg(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_demand_per_zone_gauge),\nsupply=avg(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_supply_per_zone_gauge)\n, filter: k8s.namespace.name == $Namespace\n, by:{serviceareaid}\n, interval:5m\n| fieldsAdd diff = demand[] - supply[]\n| fieldsAdd shortage = (diff[] / supply[]) * 100\n| fields timeframe, interval,shortage,\"serviceArea-\",serviceareaid",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
                   {
                      "id":1,
                      "field":"",
                      "title":"",
                      "isEnabled":true,
                      "rules":[
                         {
                            "id":0,
                            "color":{
                               "Default":"var(--dt-colors-charts-loglevel-emergency-default, #ae132d)"
                            },
                            "comparator":">",
                            "label":"",
                            "value":0
                         },
                         {
                            "id":1,
                            "color":{
                               "Default":"var(--dt-colors-charts-apdex-good-default, #1c520a)"
                            },
                            "comparator":"≤",
                            "label":"",
                            "value":0
                         }
                      ]
                   }
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"security-risk-level",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"auto",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "radius"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "shortage"
                      ]
                   },
                   "leftYAxisSettings":{
                      "scale":"linear",
                      "isLabelVisible":true,
                      "label":""
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "tooltip":{
                      "seriesDisplayMode":"single-line"
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "shortage"
                         ],
                         "value":"sparkline",
                         "id":1746796732298
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"shortage",
                      "unitCategory":"percentage",
                      "baseUnit":"percent",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746796773168
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "111":{
             "title":"Supply Shortage Per Zone - food - assuming 1 order per 1 captain",
             "type":"data",
             "query":"timeseries demand=avg(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_demand_per_zone_gauge),\nsupply=avg(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_supply_per_zone_gauge)\n, filter: k8s.namespace.name == $Namespace\n, by:{serviceareaid,zoneid}\n, interval:5m\n| fieldsAdd diff = demand[] - supply[]\n| fieldsAdd shortage = (diff[] / supply[]) * 100\n| fields timeframe, interval,shortage,serviceareaid,zoneid",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
                   {
                      "id":1,
                      "field":"",
                      "title":"",
                      "isEnabled":true,
                      "rules":[
                         {
                            "id":0,
                            "color":{
                               "Default":"var(--dt-colors-charts-loglevel-emergency-default, #ae132d)"
                            },
                            "comparator":">",
                            "label":"",
                            "value":0
                         },
                         {
                            "id":1,
                            "color":{
                               "Default":"var(--dt-colors-charts-apdex-good-default, #1c520a)"
                            },
                            "comparator":"≤",
                            "label":"",
                            "value":0
                         }
                      ]
                   }
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"security-risk-level",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"auto",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "radius"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "shortage"
                      ]
                   },
                   "leftYAxisSettings":{
                      "scale":"linear",
                      "isLabelVisible":true,
                      "label":""
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "tooltip":{
                      "seriesDisplayMode":"single-line"
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "shortage"
                         ],
                         "value":"sparkline",
                         "id":1746796732298
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"shortage",
                      "unitCategory":"percentage",
                      "baseUnit":"percent",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1746796773168
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "112":{
             "title":"Active Demand Per Service Area Per Zone",
             "type":"data",
             "query":"timeseries demand=avg(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_demand_per_zone_gauge),\n filter: k8s.namespace.name == $Namespace\n, by:{serviceareaid,zoneid}\n, interval:5m\n| fields timeframe, interval,demand, serviceareaid, zoneid",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"blue-steel-inverted",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"auto",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "radius"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "demand"
                      ]
                   },
                   "leftYAxisSettings":{
                      "scale":"linear",
                      "isLabelVisible":true,
                      "label":""
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "tooltip":{
                      "seriesDisplayMode":"single-line"
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "demand"
                         ],
                         "value":"sparkline",
                         "id":1748248821370
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "113":{
             "title":"Active Supply Per Service Area Per Zone",
             "type":"data",
             "query":"timeseries \nsupply=avg(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_supply_per_zone_gauge)\n, filter: k8s.namespace.name == $Namespace\n, by:{serviceareaid,zoneid}\n, interval:5m\n| fields timeframe, interval,supply,serviceareaid,zoneid",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical-inverted",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"auto",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "radius"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "supply"
                      ]
                   },
                   "leftYAxisSettings":{
                      "scale":"linear",
                      "isLabelVisible":true,
                      "label":""
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "tooltip":{
                      "seriesDisplayMode":"single-line"
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
        
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "114":{
             "title":"Active Supply Batch and Active Demand Batch Per Service Area Per Zone (Should not exceed 1)",
             "type":"data",
             "query":"timeseries \nsupply=avg(flink_taskmanager_job_task_operator_service_area_id_zone_id_batch_name_mp_health_mot_active_supply_and_demand_batch_count)\n, filter: k8s.namespace.name == $Namespace\n, by:{service_area_id,zone_id}\n, interval:5m\n| fields timeframe, interval,supply,service_area_id,zone_id",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical-inverted",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"auto",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "radius"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "supply"
                      ]
                   },
                   "leftYAxisSettings":{
                      "scale":"linear",
                      "isLabelVisible":true,
                      "label":""
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "tooltip":{
                      "seriesDisplayMode":"single-line"
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "supply"
                         ],
                         "value":"sparkline",
                         "id":1746797596121
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "115":{
             "title":"Records out from operators",
             "type":"data",
             "query":"timeseries ro = sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond), \nby: {operator_name}, filter: {k8s.namespace.name == $Namespace }\n| fieldsAdd ro = ro[]/60\n| fields timeframe,interval,ro,operator_name",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"always",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "ro"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "pl"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "ro"
                         ],
                         "value":"sparkline",
                         "id":1747064190954
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"ro",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_second",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"c/s",
                      "delimiter":false,
                      "added":1747064009350
                   }
                ],
                "legend":{
                   "showLegend":false,
                   "position":"auto",
                   "ratio":27
                }
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-2h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             },
             "segments":{
                "tileSegments":[
        
                ],
                "tileSegmentsEnabled":false
             }
          },
          "116":{
             "title":"Records In to operators",
             "type":"data",
             "query":"timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsInPerSecond,rate:60s), \nby: {operator_name}, filter: {k8s.namespace.name == $Namespace }\n| fieldsAdd ri = ri[]/60\n| fields timeframe,interval,ri,operator_name\n\n",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"always",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "ri"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "pl"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "ri"
                         ],
                         "value":"sparkline",
                         "id":1747141567312
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"ri",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_second",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"c/s",
                      "delimiter":false,
                      "added":1747207977127
                   }
                ],
                "legend":{
                   "showLegend":false,
                   "position":"auto",
                   "ratio":27
                }
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-2h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             },
             "segments":{
                "tileSegments":[
        
                ],
                "tileSegmentsEnabled":false
             }
          },
          "117":{
             "title":"Poison letter",
             "type":"data",
             "query":"timeseries value = max(flink_taskmanager_job_task_operator_stream_function_mp_health_poison_letter_counter),\nby: {stream,function},\nfilter:k8s.namespace.name==$Namespace\n| fieldsAdd increase = arrayDelta(value)\n| fields interval, timeframe, stream,function,increase",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"always",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "pl"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "seriesOverrides":[
        
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "increase"
                      ]
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "increase"
                         ],
                         "value":"sparkline",
                         "id":1747122059662
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ],
                "legend":{
                   "showLegend":false,
                   "position":"auto",
                   "ratio":27
                }
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-2h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             },
             "segments":{
                "tileSegments":[
        
                ],
                "tileSegmentsEnabled":false
             }
          },
          "118":{
             "title":"Number of Records Out From Sources",
             "type":"data",
             "query":"timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond), \nby: {operator_name}, \n filter: {k8s.namespace.name == $Namespace } \n| filter startsWith(operator_name,\"Source:_\")\n| fields timeframe,interval,ri,operator_name",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"always",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "ri"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "pl"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "ri"
                         ],
                         "value":"sparkline",
                         "id":1747125318044
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"ri",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_second",
                      "displayUnit":"count_per_second",
                      "decimals":0,
                      "suffix":"c/s",
                      "delimiter":true,
                      "added":1747057890500
                   }
                ],
                "legend":{
                   "showLegend":false,
                   "position":"auto",
                   "ratio":27
                }
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-2h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             },
             "segments":{
                "tileSegments":[
        
                ],
                "tileSegmentsEnabled":false
             }
          },
          "119":{
             "title":"Number of Records Out From Sinks",
             "type":"data",
             "query":"timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsInPerSecond\n,rate:60s), \nby: {operator_name}, \n filter: {k8s.namespace.name == $Namespace } \n| filter endsWith(operator_name,\"_Writer\")\n| filter startsWith(operator_name,\"KAFKA\")\n| fieldsAdd per_second = ri[]/60\n| fields timeframe,interval,per_second,operator_name",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"always",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "per_second"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "pl"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   },
                   "seriesOverrides":[
        
                   ]
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "per_second"
                         ],
                         "value":"sparkline",
                         "id":1747121227964
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"per_second",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_second",
                      "displayUnit":"count_per_second",
                      "decimals":0,
                      "suffix":"c/s",
                      "delimiter":false,
                      "added":1747057890500
                   }
                ],
                "legend":{
                   "showLegend":false,
                   "position":"auto",
                   "ratio":27
                }
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             },
             "timeframe":{
                "tileTimeframe":{
                   "from":"now()-2h",
                   "to":"now()"
                },
                "tileTimeframeEnabled":false
             },
             "segments":{
                "tileSegments":[
        
                ],
                "tileSegmentsEnabled":false
             }
          },
          "120":{
             "title":"DDR Model latency",
             "type":"data",
             "query":"timeseries latency=avg(flink_taskmanager_job_task_operator_mp_health_mot_model_execution_latency_histogram_latency),\nfilter: {k8s.namespace.name == $Namespace},\nby:{percentile}\n| fields timeframe,interval,\"p\",percentile,latency",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical-inverted",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "x"
                   ],
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "latency"
                      ]
                   },
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "latency"
                         ],
                         "value":"sparkline",
                         "id":1748249345709
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"latency",
                      "unitCategory":"electricity",
                      "baseUnit":"millisiemens",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747125717910
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "122":{
             "title":"Zones Missing Previous Radius",
             "type":"data",
             "query":"timeseries ri = max(flink_taskmanager_job_task_operator_mp_health_mot_previous_radius_for_zone_not_found_counter, rate:80s), \nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = arrayDelta(ri)\n| fields timeframe,interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1747914583313
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":1,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "123":{
             "title":"Merchant Radius Updates",
             "type":"data",
             "query":"timeseries ri = max(flink_taskmanager_job_task_operator_service_area_id_zone_id_mp_health_merchant_radius_update_counter, rate:60s), \nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = arrayDelta(ri)\n| fieldsAdd merchants = iCollectArray(count[]*10)\n| fields timeframe,interval, merchants",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "merchants"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
        
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"merchants",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "124":{
             "title":"Filtered Out Active Supply and Demand Service Area Batches",
             "type":"data",
             "query":"timeseries ri = sum(flink_taskmanager_job_task_operator_mp_health_mot_filtered_out_batch_with_disabled_service_area_count, rate:60s), \nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = arrayDelta(ri)\n| fieldsAdd disabled_service_area = iCollectArray(count[]*10)\n| fields timeframe,interval, disabled_service_area",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "disabled_service_area"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "disabled_service_area"
                         ],
                         "value":"sparkline",
                         "id":1747916030712
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"merchants",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":0,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "125":{
             "title":"Filtered Out Active Supply and Demand Batches",
             "type":"data",
             "query":"timeseries {\n  cycles = sum(flink_taskmanager_job_task_operator_mp_health_mot_filtered_out_batch_with_disabled_zone_count, rate:40s), \n  cycles1 = sum(flink_taskmanager_job_task_operator_mp_health_mot_filtered_out_batch_with_no_match_zone_count, rate:40s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd disabled_zone = arrayDelta(cycles)\n| fieldsAdd no_match_zone = arrayDelta(cycles1)\n| fields timeframe, interval, disabled_zone, no_match_zone",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "disabled_zone",
                         "no_match_zone"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "bandChartSettings":{
                      "lower":"disabled_zone",
                      "upper":"no_match_zone"
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "disabled_zone",
                            "no_match_zone"
                         ],
                         "value":"sparkline",
                         "id":1748249055703
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"no_match_zone",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   },
                   {
                      "identifier":"disabled_zone",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748211632143
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "126":{
             "title":"DDR Model executions",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_mot_ddr_model_execution_counter_success, rate:60s), \n  cycles1 = max(flink_taskmanager_job_task_operator_mp_health_mot_ddr_model_execution_counter_failure, rate:60s),\n  cycles2 = max(flink_taskmanager_job_task_operator_mp_health_mot_ddr_async_operator_timeout_count, rate:60s),\n  cycles3 = max(flink_taskmanager_job_task_operator_mp_health_mot_ddr_circuit_breaker_not_permitted_calls, rate:60s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd success = (arrayDelta(cycles))[]*10\n| fieldsAdd failed = (arrayDelta(cycles1))[]*10\n| fieldsAdd time_out = (arrayDelta(cycles2))[]*10\n| fieldsAdd open_circuit = (arrayDelta(cycles3))[]*10\n| fields timeframe, interval, success, failed, time_out, open_circuit",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "success",
                         "failed",
                         "time_out",
                         "open_circuit"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "bandChartSettings":{
                      "lower":"success",
                      "upper":"failed"
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "success",
                            "failed",
                            "time_out",
                            "open_circuit"
                         ],
                         "value":"sparkline",
                         "id":1748249055793
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "136":{
             "title":"DDR Model Name for Service Area Id",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_ddr_model_name_service_area_id_mp_health_mot_ddr_model_name_by_service_area_id, rate:70s)\n},\nby: {k8s.namespace.name, service_area_id, ddr_model_name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))\n| fields timeframe, interval, count, service_area_id, ddr_model_name",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748214557260
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748213920412
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "137":{
             "title":"Stream Failure to Process and Write data to Search Team's Kafka topic",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_order_count, rate:90s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748212044391
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748213920412
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "138":{
             "title":"Merchant Radius Updates Per Service Area Per Zone",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_service_area_id_zone_id_mp_health_merchant_radius_update_counter, rate:80s)\n},\nby: {k8s.namespace.name, service_area_id, zone_id}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))\n| fields timeframe, interval, count, service_area_id, zone_id",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748214557260
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748213920412
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "143":{
             "title":"Received Merchant Updates",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_merchant_update_stream_merchants_counter, rate:80s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748215140943
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748215553941
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "149":{
             "title":"Ping Processor Ping Collections Received",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_ping_processor_ping_collections_passed, rate:100s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles)[]*10)\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748215140943
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748215553941
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "150":{
             "title":"Ping Processor Pings",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_ping_processor_pings, rate:90s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles)[]*10)\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748215140943
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748215553941
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "151":{
             "title":"Received Order Updates",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_order_count, rate:100s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles)[]*10)\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748215140943
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748215553941
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "152":{
             "title":"Dropped Order Updates",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_order_count, rate:100s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| append [\n    timeseries { \n      cycles1 = max(flink_taskmanager_job_task_operator_reason_mp_health_order_update_event_missing_value, rate:100s)\n    },\n    by: {k8s.namespace.name, reason}\n    | fieldsAdd count = (arrayDelta(cycles1))[]*10\n]\n| fieldsAdd order_count_from_kafka = (arrayDelta(cycles))[]*10\n| fields interval, timeframe, order_count_from_kafka, count, reason\n\n",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "order_count_from_kafka",
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
        
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "bandChartSettings":{
                      "lower":"order_count_from_kafka",
                      "upper":"count"
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748217113848
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"order_count_from_kafka",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748217216015
                   },
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748217226437
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "153":{
             "title":"Self Delivery Merchants - order updates excluded",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_service_area_id_mp_health_filtered_self_delivery_merchant_order_update_events_counter, rate:30s)\n},\nby: {k8s.namespace.name, service_area_id}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, \"Service Area\",service_area_id, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748215140943
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748215553941
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "154":{
             "title":"Enriched pings (valid vs dropped)",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_ping_processor_pings, rate:80s),\n  cycles1 = max(flink_taskmanager_job_task_operator_mp_health_ping_processor_dropped_pings, rate:80s)\n\n},\nby: {k8s.namespace.name, driver_status}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd valid_enriched_pings = (arrayDelta(cycles))[]*10\n| fieldsAdd dropped_enriched_pings = (arrayDelta(cycles1))[]*10\n| fields timeframe, interval, dropped_enriched_pings, valid_enriched_pings",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "dropped_enriched_pings",
                         "valid_enriched_pings"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "bandChartSettings":{
                      "lower":"dropped_enriched_pings",
                      "upper":"valid_enriched_pings"
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "dropped_enriched_pings",
                            "valid_enriched_pings"
                         ],
                         "value":"sparkline",
                         "id":1748247633876
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "155":{
             "title":"Dropped Merchant Updates",
             "type":"data",
             "query":"timeseries {\n  cycles = avg(flink_taskmanager_job_task_operator_reason_mp_health_order_update_event_missing_value, rate:100s)\n},\nby: {k8s.namespace.name, reason}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields interval, timeframe, count, reason\n\n",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
        
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748217113848
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"order_count_from_kafka",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748217216015
                   },
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748217226437
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "156":{
             "title":"MoT pings by driver status breakdown",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_driver_status_mp_health_mot_captain_ping_stream_mot_pings_by_driver_status, rate:60s)\n},\nby: {k8s.namespace.name, driver_status}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, driver_status, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748215140943
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "157":{
             "title":"Dropped enriched pings breakdown (reason)",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_reason_mp_health_ping_processor_dropped_pings_by_reason, rate:60s)\n},\nby: {k8s.namespace.name, reason}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, reason, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748215140943
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "158":{
             "title":"Total Active Demand",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_demand_per_zone_gauge, rate:60s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles)[]*100)\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748212044391
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "159":{
             "title":"Total Active Supply",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_serviceAreaId_zoneId_mp_health_active_supply_per_zone_gauge, rate:60s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles)[]*100)\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748212044391
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "160":{
             "title":"Total Enriched pings vs MoT & Available pings",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_mot_captain_ping_stream_enriched_ping_counter, rate:80s),\n  cycles1 = max(flink_taskmanager_job_task_operator_mp_health_mot_captain_ping_stream_active_mot_enriched_ping_counter, rate:80s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd total_enriched_pings = (arrayDelta(cycles))[]*10\n| fieldsAdd mot_active_pings = (arrayDelta(cycles1))[]*10\n| fields timeframe, interval, total_enriched_pings, mot_active_pings",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "total_enriched_pings",
                         "mot_active_pings"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "bandChartSettings":{
                      "lower":"total_enriched_pings",
                      "upper":"mot_active_pings"
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "total_enriched_pings",
                            "mot_active_pings"
                         ],
                         "value":"sparkline",
                         "id":1748248691572
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "161":{
             "title":"Order Updates for Delivered/Cancelled Orders",
             "type":"data",
             "query":"timeseries {\n  cycles = avg(flink_taskmanager_job_task_operator_mp_health_mot_order_heartbeat_stream_order_update_for_delivered_or_cancelled_order_counter, rate:120s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748212044391
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "162":{
             "title":"Generated Active Supply and Demand Batch",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_generated_mot_active_supply_and_demand_batch_count, rate:60s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748248952623
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "163":{
             "title":"Order Updates for Non-existing Order",
             "type":"data",
             "query":"timeseries {\n  cycles = avg(flink_taskmanager_job_task_operator_mp_health_mot_order_heartbeat_stream_order_update_for_non_existing_order_counter, rate:80s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748212044391
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "164":{
             "title":"Active Orders Filter",
             "type":"data",
             "query":"timeseries {\n  cycles = sum(flink_taskmanager_job_task_operator_mp_health_mot_draft_order_update_for_closed_order_counter, rate:60s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = arrayDelta(cycles)\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748218524189
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1747914513300
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "165":{
             "title":"Zone Supply Demand Count To Analytika Event",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_mp_health_zone_supply_demand_count_to_analytika_event_counter, rate:80s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748212044391
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
        
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "166":{
             "title":"Mot DdrModel Input/Output ToAnalytika Event Count By status",
             "type":"data",
             "query":"timeseries {\n  cycles = avg(flink_taskmanager_job_task_operator_status_mot_ddr_model_input_to_analytika_event_status_monitor, rate:100s)\n},\nby: {k8s.namespace.name, status}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd ddr_model_input = (arrayDelta(cycles))[]*10\n| fields timeframe, interval, status, ddr_model_input",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "ddr_model_input"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "ddr_model_input"
                         ],
                         "value":"sparkline",
                         "id":1748249756026
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748215553941
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "167":{
             "title":"Dropping Stale Batches of Active Supply & Demand Batch",
             "type":"data",
             "query":"timeseries {\n  cycles = sum(flink_taskmanager_job_task_operator_mp_health_mot_filtered_out_batch_with_stale_data, rate:60s)\n},\nby: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))\n| fields timeframe, interval, count",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748212044391
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748213920412
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          },
          "168":{
             "title":"Generated Merchant Recommendations for Hive Table Per SA",
             "type":"data",
             "query":"timeseries {\n  cycles = max(flink_taskmanager_job_task_operator_service_area_id_mp_health_merchant_ddr_recommendation_count, rate:80s)\n},\nby: {k8s.namespace.name, service_area_id}\n| filter k8s.namespace.name == $Namespace \n| fieldsAdd count = (arrayDelta(cycles))\n| fields timeframe, interval, count, \"Service Area\", service_area_id",
             "visualization":"lineChart",
             "visualizationSettings":{
                "thresholds":[
        
                ],
                "chartSettings":{
                   "gapPolicy":"gap",
                   "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                   },
                   "categoryOverrides":{
        
                   },
                   "curve":"linear",
                   "pointsDisplay":"auto",
                   "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "valueAxisScale":"linear"
                   },
                   "colorPalette":"categorical",
                   "valueRepresentation":"absolute",
                   "truncationMode":"middle",
                   "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                         "count"
                      ]
                   },
                   "xAxisScaling":"analyzedTimeframe",
                   "xAxisLabel":"timeframe",
                   "xAxisIsLabelVisible":false,
                   "hiddenLegendFields":[
                      "interval",
                      "per_second"
                   ],
                   "leftYAxisSettings":{
        
                   },
                   "legend":{
                      "position":"bottom",
                      "hidden":false
                   }
                },
                "singleValue":{
                   "showLabel":true,
                   "label":"",
                   "prefixIcon":"AnalyticsIcon",
                   "isIconVisible":false,
                   "autoscale":true,
                   "alignment":"center",
                   "colorThresholdTarget":"value"
                },
                "table":{
                   "rowDensity":"condensed",
                   "enableSparklines":false,
                   "hiddenColumns":[
        
                   ],
                   "linewrapEnabled":false,
                   "lineWrapIds":[
        
                   ],
                   "monospacedFontEnabled":false,
                   "monospacedFontColumns":[
        
                   ],
                   "columnWidths":{
        
                   },
                   "columnTypeOverrides":[
                      {
                         "fields":[
                            "count"
                         ],
                         "value":"sparkline",
                         "id":1748214557260
                      }
                   ]
                },
                "honeycomb":{
                   "shape":"hexagon",
                   "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                   },
                   "dataMappings":{
        
                   },
                   "displayedFields":[
        
                   ],
                   "truncationMode":"middle",
                   "colorMode":"color-palette",
                   "colorPalette":"categorical"
                },
                "histogram":{
                   "legend":{
                      "position":"auto"
                   },
                   "yAxis":{
                      "label":"Frequency",
                      "isLabelVisible":true,
                      "scale":"linear"
                   },
                   "colorPalette":"categorical",
                   "dataMappings":[
        
                   ],
                   "variant":"single",
                   "truncationMode":"middle"
                },
                "valueBoundaries":{
                   "min":"auto",
                   "max":"auto"
                },
                "autoSelectVisualization":true,
                "unitsOverrides":[
                   {
                      "identifier":"count",
                      "unitCategory":"unspecified",
                      "baseUnit":"count_per_minute",
                      "displayUnit":null,
                      "decimals":2,
                      "suffix":"",
                      "delimiter":false,
                      "added":1748213920412
                   }
                ]
             },
             "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
             },
             "davis":{
                "enabled":false,
                "davisVisualization":{
                   "isAvailable":true
                }
             }
          }
        },
        "layouts":{
          "16":{
             "x":0,
             "y":47,
             "w":20,
             "h":1
          },
          "38":{
             "x":0,
             "y":26,
             "w":24,
             "h":1
          },
          "42":{
             "x":0,
             "y":33,
             "w":12,
             "h":6
          },
          "46":{
             "x":0,
             "y":0,
             "w":24,
             "h":1
          },
          "59":{
             "x":12,
             "y":39,
             "w":12,
             "h":8
          },
          "66":{
             "x":12,
             "y":9,
             "w":12,
             "h":9
          },
          "67":{
             "x":0,
             "y":18,
             "w":24,
             "h":8
          },
          "76":{
             "x":0,
             "y":9,
             "w":12,
             "h":9
          },
          "77":{
             "x":0,
             "y":27,
             "w":12,
             "h":6
          },
          "78":{
             "x":12,
             "y":33,
             "w":12,
             "h":6
          },
          "79":{
             "x":18,
             "y":30,
             "w":6,
             "h":3
          },
          "80":{
             "x":18,
             "y":27,
             "w":6,
             "h":3
          },
          "92":{
             "x":12,
             "y":27,
             "w":6,
             "h":6
          },
          "93":{
             "x":0,
             "y":1,
             "w":12,
             "h":8
          },
          "102":{
             "x":0,
             "y":39,
             "w":12,
             "h":8
          },
          "103":{
             "x":12,
             "y":1,
             "w":12,
             "h":8
          },
          "104":{
             "x":0,
             "y":48,
             "w":12,
             "h":5
          },
          "105":{
             "x":0,
             "y":63,
             "w":24,
             "h":1
          },
          "109":{
             "x":0,
             "y":209,
             "w":24,
             "h":8
          },
          "110":{
             "x":0,
             "y":109,
             "w":24,
             "h":8
          },
          "111":{
             "x":0,
             "y":117,
             "w":24,
             "h":8
          },
          "112":{
             "x":0,
             "y":143,
             "w":24,
             "h":8
          },
          "113":{
             "x":0,
             "y":151,
             "w":24,
             "h":8
          },
          "114":{
             "x":0,
             "y":168,
             "w":24,
             "h":8
          },
          "115":{
             "x":12,
             "y":53,
             "w":12,
             "h":5
          },
          "116":{
             "x":0,
             "y":53,
             "w":12,
             "h":5
          },
          "117":{
             "x":12,
             "y":48,
             "w":12,
             "h":5
          },
          "118":{
             "x":0,
             "y":58,
             "w":12,
             "h":5
          },
          "119":{
             "x":12,
             "y":58,
             "w":12,
             "h":5
          },
          "120":{
             "x":12,
             "y":201,
             "w":12,
             "h":8
          },
          "122":{
             "x":0,
             "y":201,
             "w":12,
             "h":8
          },
          "123":{
             "x":0,
             "y":192,
             "w":12,
             "h":9
          },
          "124":{
             "x":12,
             "y":192,
             "w":12,
             "h":9
          },
          "125":{
             "x":12,
             "y":183,
             "w":12,
             "h":9
          },
          "126":{
             "x":0,
             "y":183,
             "w":12,
             "h":9
          },
          "136":{
             "x":0,
             "y":235,
             "w":24,
             "h":9
          },
          "137":{
             "x":0,
             "y":244,
             "w":12,
             "h":9
          },
          "138":{
             "x":0,
             "y":217,
             "w":24,
             "h":9
          },
          "143":{
             "x":0,
             "y":82,
             "w":12,
             "h":9
          },
          "149":{
             "x":0,
             "y":64,
             "w":12,
             "h":9
          },
          "150":{
             "x":12,
             "y":64,
             "w":12,
             "h":9
          },
          "151":{
             "x":0,
             "y":73,
             "w":12,
             "h":9
          },
          "152":{
             "x":12,
             "y":73,
             "w":12,
             "h":9
          },
          "153":{
             "x":12,
             "y":82,
             "w":12,
             "h":9
          },
          "154":{
             "x":0,
             "y":91,
             "w":12,
             "h":9
          },
          "155":{
             "x":12,
             "y":91,
             "w":12,
             "h":9
          },
          "156":{
             "x":0,
             "y":100,
             "w":12,
             "h":9
          },
          "157":{
             "x":12,
             "y":100,
             "w":12,
             "h":9
          },
          "158":{
             "x":0,
             "y":125,
             "w":12,
             "h":9
          },
          "159":{
             "x":0,
             "y":134,
             "w":12,
             "h":9
          },
          "160":{
             "x":12,
             "y":125,
             "w":12,
             "h":9
          },
          "161":{
             "x":12,
             "y":134,
             "w":12,
             "h":9
          },
          "162":{
             "x":0,
             "y":159,
             "w":12,
             "h":9
          },
          "163":{
             "x":12,
             "y":159,
             "w":12,
             "h":9
          },
          "164":{
             "x":0,
             "y":176,
             "w":24,
             "h":7
          },
          "165":{
             "x":0,
             "y":260,
             "w":24,
             "h":8
          },
          "166":{
             "x":0,
             "y":253,
             "w":24,
             "h":7
          },
          "167":{
             "x":12,
             "y":244,
             "w":12,
             "h":9
          },
          "168":{
             "x":0,
             "y":226,
             "w":24,
             "h":9
          }
        },
        "importedWithCode":false,
        "settings":{
          "gridLayout":{
             "mode":"responsive"
          }
        }
    }
