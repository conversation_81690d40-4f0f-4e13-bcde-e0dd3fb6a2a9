---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: golden-dashboard
  labels:
    scope: global
spec:
  name: Golden Dashboard
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.kubernetes_cluster\n| fields entity.name\n| sort entity.name",
                "multiple": true
            },
            {
                "key": "Namespace",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.kubernetes_cluster\n| filter in(entity.name, $Cluster)\n| join [fetch dt.entity.cloud_application_namespace\n| fieldsAdd clusterId=clustered_by[dt.entity.kubernetes_cluster]], on:{left[id]==right[clusterId]}, fields:{nsName=entity.name}\n| fields nsName\n| sort nsName",
                "multiple": true
            },
            {
                "key": "LogStatus",
                "type": "csv",
                "visible": true,
                "input": "NONE,INFO,WARN,ERROR",
                "multiple": true,
                "defaultValue": [
                    "ERROR",
                    "WARN"
                ],
                "version": 1
            },
            {
                "key": "LogSearch",
                "type": "text",
                "visible": true,
                "version": 1
            }
        ],
        "tiles": {
            "0": {
                "title": "Workload Health",
                "type": "data",
                "query": "fetch dt.entity.cloud_application\n| filter clustered_by[dt.entity.kubernetes_cluster] in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields id\n]\n| filter in(namespaceName, $Namespace)\n| lookup [\n  fetch dt.davis.problems\n  | filter event.status==\"ACTIVE\" and dt.davis.is_duplicate==false\n  | expand affected_entity_ids\n], sourceField:id, lookupField:affected_entity_ids, fields:{event.status, display_id, event.start, event.id}\n| fieldsAdd status=if(event.status==\"ACTIVE\", \"Problem\", else: \"OK\")\n| sort event.status asc, entity.name",
                "visualization": "honeycomb",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "entity.name,id,status",
                            "valueAxisLabel": "",
                            "categoryAxis": [
                                "entity.name",
                                "id",
                                "status"
                            ],
                            "valueAxis": [],
                            "tooltipVariant": "single"
                        },
                        "hiddenLegendFields": [
                            "entity"
                        ],
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "0": "h",
                            "1": "i",
                            "2": "d",
                            "3": "d",
                            "4": "e",
                            "5": "n",
                            "hidden": true
                        },
                        "dataMappings": {
                            "value": "status"
                        },
                        "displayedFields": [
                            "entity.name"
                        ],
                        "colorMode": "custom-colors",
                        "colorPalette": "categorical",
                        "customColors": [
                            {
                                "id": 0,
                                "value": "Problem",
                                "comparator": "=",
                                "color": "#CD3741"
                            },
                            {
                                "id": 147809.09999999963,
                                "value": "OK",
                                "comparator": "=",
                                "color": "#0D9C29"
                            }
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": [
                            "entity.name",
                            "id",
                            "status"
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "1": {
                "title": "Service Health",
                "type": "data",
                "query": "fetch dt.entity.service\n| filter belongs_to[dt.entity.cloud_application][0] in [\n  fetch dt.entity.cloud_application\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| lookup [\n  fetch dt.davis.problems\n  | filter event.status==\"ACTIVE\" and dt.davis.is_duplicate==false\n  | expand affected_entity_ids\n], sourceField:id, lookupField:affected_entity_ids, fields:{event.status, display_id, event.start, event.id}\n| fieldsAdd status=if(event.status==\"ACTIVE\", \"Problem\", else: \"OK\"), timestamp=$dt_timeframe_to\n| sort event.status asc, entity.name",
                "visualization": "honeycomb",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "entity.name",
                            "valueAxisLabel": "",
                            "categoryAxis": "entity.name",
                            "valueAxis": [],
                            "tooltipVariant": "single"
                        },
                        "hiddenLegendFields": [
                            "entity"
                        ],
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "0": "h",
                            "1": "i",
                            "2": "d",
                            "3": "d",
                            "4": "e",
                            "5": "n",
                            "hidden": true
                        },
                        "dataMappings": {
                            "value": "status"
                        },
                        "displayedFields": [
                            "entity.name"
                        ],
                        "colorMode": "custom-colors",
                        "colorPalette": "categorical",
                        "customColors": [
                            {
                                "id": 0,
                                "value": "Problem",
                                "comparator": "=",
                                "color": "#CD3741"
                            },
                            {
                                "id": 147809.09999999963,
                                "value": "OK",
                                "comparator": "=",
                                "color": "#0D9C29"
                            }
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "title": "🚩 Failure Rate",
                "type": "data",
                "query": "timeseries {failures=sum(dt.service.request.failure_count, default:0), requests=sum(dt.service.request.count, default:1)}, by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application][0] in [\n    fetch dt.entity.cloud_application\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n      fetch dt.entity.kubernetes_cluster\n      | filter in(entity.name, $Cluster)\n      | fields id\n    ]\n    | filter in(namespaceName, $Namespace)\n    | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service),failureRate = failures[]/requests[]*100\n| filterOut contains(dt.entity.service.name, \"Requests executed in background threads of \")\n| fieldsRemove failures, requests\n| sort arrayLast(failureRate) desc\n| limit 20",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.service.name",
                            "categoryAxisLabel": "dt.entity.service.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.service"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "failureRate"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.service",
                                "dt.entity.service.name"
                            ]
                        },
                        "legend": {
                            "hidden": true
                        },
                        "colorPalette": "fireplace",
                        "leftYAxisSettings": {
                            "max": "data-max",
                            "min": 0
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "dt.entity.service",
                        "prefixIcon": "",
                        "recordField": "dt.entity.service",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            "dt.entity.service"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "failureRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721893726115
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {}
            },
            "11": {
                "title": "📈 Throughput/m",
                "type": "data",
                "query": "timeseries requests=sum(dt.service.request.count, rate:1m), default:0, by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application][0] in [\n    fetch dt.entity.cloud_application\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n      fetch dt.entity.kubernetes_cluster\n      | filter in(entity.name, $Cluster)\n      | fields id\n    ]\n    | filter in(namespaceName, $Namespace)\n    | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| filterOut contains(dt.entity.service.name, \"Requests executed in background threads of \")\n| sort arrayAvg(requests) desc\n| limit 20",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.service.name",
                            "categoryAxisLabel": "dt.entity.service.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.service"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.service",
                                "dt.entity.service.name"
                            ]
                        },
                        "legend": {
                            "hidden": true
                        },
                        "colorPalette": "categorical"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "dt.entity.service",
                        "prefixIcon": "",
                        "recordField": "dt.entity.service",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            "dt.entity.service"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "failureRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721893726115
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {}
            },
            "12": {
                "title": "⏱️ Response Time",
                "type": "data",
                "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application][0] in [\n    fetch dt.entity.cloud_application\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n      fetch dt.entity.kubernetes_cluster\n      | filter in(entity.name, $Cluster)\n      | fields id\n    ]\n    | filter in(namespaceName, $Namespace)\n    | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| filterOut contains(dt.entity.service.name, \"Requests executed in background threads of \")\n| sort arrayAvg(requests) desc\n| limit 20",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.service.name",
                            "categoryAxisLabel": "dt.entity.service.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.service"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.service",
                                "dt.entity.service.name"
                            ]
                        },
                        "legend": {
                            "hidden": true
                        },
                        "colorPalette": "purple-rain"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "dt.entity.service",
                        "prefixIcon": "",
                        "recordField": "dt.entity.service",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            "dt.entity.service"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "failureRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721893726115
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "componentState": {
                        "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                        "inputData": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "generalParameters": {
                                    "timeframe": {
                                        "startTime": "now-24h",
                                        "endTime": "now"
                                    },
                                    "resolveDimensionalQueryData": true,
                                    "logVerbosity": "INFO"
                                },
                                "numberOfSignalFluctuations": 1,
                                "alertCondition": "ABOVE",
                                "alertOnMissingData": false,
                                "violatingSamples": 3,
                                "slidingWindow": 5,
                                "dealertingSamples": 5,
                                "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                            }
                        },
                        "analyzerHints": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "unit": {
                                    "unitCategory": "time",
                                    "baseUnit": "microsecond"
                                }
                            }
                        }
                    }
                }
            },
            "13": {
                "title": "📔 Log Patterns",
                "type": "data",
                "query": "fetch logs\n| filter in(k8s.namespace.name, array($Namespace)) and in(status, $LogStatus) and contains(content, $LogSearch)\n| parse content, \"DATA* JSON:json\"\n| fieldsAdd pattern=coalesce(message,msg,json[message],json[msg],json[rest],rest,content)\n| fieldsAdd pattern=replacePattern(replacePattern(replacePattern(replacePattern(replacePattern(replacePattern(pattern, \"UUIDSTRING\", \"UUID\"), \"(ISO8601|HTTPDATE|JSONTIMESTAMP|TIMESTAMP|TIME)\", \"TIME\"),\"IPADDR\", \"IP\"), \"'-' ALNUM{10} '-' ALNUM{5}\", \"-PODID\"), \"XDIGIT{5,}\", \"*\"), \"DIGIT\", \"*\")\n| maketimeseries {count=count(default:0)}, by: {status,pattern, dt.entity.process_group, dt.entity.cloud_application}\n| sort arraySum(count) desc\n| fieldsAdd entityName(dt.entity.process_group)\n| fieldsAdd severity = if(status==\"ERROR\", 0, else: if(status==\"WARN\", 1, else:if(status==\"NONE\", 2, else: 3)))\n| sort severity, arraySum(count) desc\n| fieldsRename Status=status, Pattern=pattern, Process=dt.entity.process_group.name, Count=count\n",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "Status",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "INFO"
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "WARN"
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "ERROR"
                                },
                                {
                                    "id": 3,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-04-default, #d85a9f)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "NONE"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "Status",
                            "categoryAxisLabel": "Status",
                            "valueAxis": "severity",
                            "valueAxisLabel": "severity"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "Count"
                            ],
                            "leftAxisDimensions": [
                                "Status",
                                "Pattern",
                                "severity"
                            ]
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "hiddenLegendFields": [
                            "dt.entity.cloud_application",
                            "dt.entity.process_group"
                        ]
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "pattern",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": true,
                        "hiddenColumns": [
                            [
                                "timeframe"
                            ],
                            [
                                "interval"
                            ],
                            [
                                "dt.entity.cloud_application"
                            ],
                            [
                                "dt.entity.process_group"
                            ],
                            [
                                "Process"
                            ],
                            [
                                "severity"
                            ]
                        ],
                        "lineWrapIds": [
                            [
                                "Pattern"
                            ]
                        ],
                        "columnWidths": {
                            "[\"dt.entity.process_group.name\"]": 218.99999999999994,
                            "[\"pattern\"]": 855,
                            "[\"count\"]": 148.94033813476562,
                            "[\"Pattern\"]": 1152
                        },
                        "colorThresholdTarget": "background"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "Status"
                        },
                        "displayedFields": [
                            "Status"
                        ]
                    },
                    "histogram": {
                        "dataMappings": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 50,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "14": {
                "title": "🖹 Logs",
                "type": "data",
                "query": "fetch logs\n| filter in(k8s.namespace.name, array($Namespace)) and in(status,$LogStatus) and contains(content, $LogSearch)\n| parse content, \"DATA* JSON:json\"\n| parse content, \"DATA* '\\\"rest\\\":' DQS:rest\"\n| fieldsAdd Status=status, Timestamp=timestamp, Message=coalesce(message,msg,json[message],json[msg],json[rest],rest,content)\n| sort timestamp desc\n| limit 100",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "Status",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "INFO"
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "WARN"
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "ERROR"
                                },
                                {
                                    "id": 3,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-04-default, #d85a9f)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "NONE"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "pattern",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": []
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "pattern",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "dt.entity.cloud_application"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ],
                            [
                                "dt.entity.cloud_application_namespace"
                            ],
                            [
                                "dt.entity.host"
                            ],
                            [
                                "dt.entity.kubernetes_cluster"
                            ],
                            [
                                "dt.entity.process_group"
                            ],
                            [
                                "dt.entity.process_group_instance"
                            ],
                            [
                                "dt.kubernetes.cluster.id"
                            ],
                            [
                                "dt.kubernetes.workload.kind"
                            ],
                            [
                                "dt.kubernetes.workload.name"
                            ],
                            [
                                "dt.source_entity"
                            ],
                            [
                                "event.type"
                            ],
                            [
                                "k8s.namespace.name"
                            ],
                            [
                                "k8s.pod.name"
                            ],
                            [
                                "k8s.pod.uid"
                            ],
                            [
                                "loglevel"
                            ],
                            [
                                "@timestamp"
                            ],
                            [
                                "bookingid"
                            ],
                            [
                                "caller"
                            ],
                            [
                                "captainid"
                            ],
                            [
                                "cluster_id"
                            ],
                            [
                                "dt.auth.origin"
                            ],
                            [
                                "dt.ingest.warnings"
                            ],
                            [
                                "environmentname"
                            ],
                            [
                                "error"
                            ],
                            [
                                "googletripid"
                            ],
                            [
                                "instanceid"
                            ],
                            [
                                "kubernetes.container_hash"
                            ],
                            [
                                "kubernetes.container_image"
                            ],
                            [
                                "kubernetes.container_name"
                            ],
                            [
                                "kubernetes.docker_id"
                            ],
                            [
                                "kubernetes.host"
                            ],
                            [
                                "kubernetes.labels.app.kubernetes.io/name"
                            ],
                            [
                                "kubernetes.labels.linkerd.io/control-plane-ns"
                            ],
                            [
                                "kubernetes.labels.linkerd.io/proxy-deployment"
                            ],
                            [
                                "kubernetes.labels.linkerd.io/workload-ns"
                            ],
                            [
                                "kubernetes.labels.pod-template-hash"
                            ],
                            [
                                "kubernetes.namespace_name"
                            ],
                            [
                                "kubernetes.pod_id"
                            ],
                            [
                                "kubernetes.pod_name"
                            ],
                            [
                                "log"
                            ],
                            [
                                "logtag"
                            ],
                            [
                                "servermethod"
                            ],
                            [
                                "service"
                            ],
                            [
                                "span_id"
                            ],
                            [
                                "stacktrace"
                            ],
                            [
                                "stream"
                            ],
                            [
                                "trace_id"
                            ],
                            [
                                "trace_sampled"
                            ],
                            [
                                "userid"
                            ],
                            [
                                "version"
                            ],
                            [
                                "x-calling-api"
                            ],
                            [
                                "x-client-name"
                            ],
                            [
                                "x-client-version"
                            ],
                            [
                                "x-originated-from"
                            ],
                            [
                                "x-request-id"
                            ],
                            [
                                "dt.entity.kubernetes_node"
                            ],
                            [
                                "k8s.container.name"
                            ],
                            [
                                "class"
                            ],
                            [
                                "hostname"
                            ],
                            [
                                "k8s.node.name"
                            ],
                            [
                                "logtype"
                            ],
                            [
                                "rest"
                            ],
                            [
                                "span"
                            ],
                            [
                                "thread"
                            ],
                            [
                                "trace"
                            ],
                            [
                                "ipaddress"
                            ],
                            [
                                "meta.dt.entity.host"
                            ],
                            [
                                "meta.dt.entity.process_group"
                            ],
                            [
                                "meta.dt.entity.process_group_instance"
                            ],
                            [
                                "meta.dt.kubernetes.cluster.id"
                            ],
                            [
                                "meta.dt.kubernetes.workload.kind"
                            ],
                            [
                                "meta.dt.kubernetes.workload.name"
                            ],
                            [
                                "meta.dt.span_id"
                            ],
                            [
                                "meta.dt.trace_id"
                            ],
                            [
                                "meta.dt.trace_sampled"
                            ],
                            [
                                "meta.environment"
                            ],
                            [
                                "meta.errors"
                            ],
                            [
                                "meta.hostname"
                            ],
                            [
                                "meta.k8s.namespace.name"
                            ],
                            [
                                "meta.k8s.pod.name"
                            ],
                            [
                                "meta.k8s.pod.uid"
                            ],
                            [
                                "meta.level"
                            ],
                            [
                                "meta.message"
                            ],
                            [
                                "meta.msg"
                            ],
                            [
                                "meta.name"
                            ],
                            [
                                "meta.namespace"
                            ],
                            [
                                "meta.pid"
                            ],
                            [
                                "meta.status"
                            ],
                            [
                                "meta.time"
                            ],
                            [
                                "meta.v"
                            ],
                            [
                                "meta.version"
                            ],
                            [
                                "arn"
                            ],
                            [
                                "err"
                            ],
                            [
                                "job_type"
                            ],
                            [
                                "kubernetes.labels.app.kubernetes.io/instance"
                            ],
                            [
                                "msg"
                            ],
                            [
                                "region"
                            ],
                            [
                                "ts"
                            ],
                            [
                                "exportable"
                            ],
                            [
                                "pid"
                            ],
                            [
                                "domain"
                            ],
                            [
                                "time"
                            ],
                            [
                                "activity_type"
                            ],
                            [
                                "level"
                            ],
                            [
                                "provider_id"
                            ],
                            [
                                "reference_id"
                            ],
                            [
                                "canary"
                            ],
                            [
                                "parent"
                            ],
                            [
                                "alert.name"
                            ],
                            [
                                "alert.namespace"
                            ],
                            [
                                "controller"
                            ],
                            [
                                "controllergroup"
                            ],
                            [
                                "controllerkind"
                            ],
                            [
                                "name"
                            ],
                            [
                                "namespace"
                            ],
                            [
                                "reconcileid"
                            ],
                            [
                                "errorverbose"
                            ],
                            [
                                "signal"
                            ],
                            [
                                "category"
                            ],
                            [
                                "isreport"
                            ],
                            [
                                "k8sresourceapiversion"
                            ],
                            [
                                "k8sresourcekind"
                            ],
                            [
                                "k8sresourcename"
                            ],
                            [
                                "k8sresourcenamespace"
                            ],
                            [
                                "kubernetes.labels.app.kubernetes.io/component"
                            ],
                            [
                                "kubernetes.labels.app.kubernetes.io/managed-by"
                            ],
                            [
                                "kubernetes.labels.app.kubernetes.io/part-of"
                            ],
                            [
                                "kubernetes.labels.helm.sh/chart"
                            ],
                            [
                                "policy"
                            ],
                            [
                                "priority"
                            ],
                            [
                                "result"
                            ],
                            [
                                "resultcreationtimestamp"
                            ],
                            [
                                "resultseverity"
                            ],
                            [
                                "rule"
                            ],
                            [
                                "scored"
                            ],
                            [
                                "unparsed_timestamp"
                            ],
                            [
                                "logistics.batchuid"
                            ],
                            [
                                "logistics.bookingtype"
                            ],
                            [
                                "logistics.cct"
                            ],
                            [
                                "logistics.domain"
                            ],
                            [
                                "logistics.matchingid"
                            ],
                            [
                                "logistics.servicearea"
                            ],
                            [
                                "kubernetes.labels.linkerd.io/proxy-replicaset"
                            ],
                            [
                                "kubernetes.labels.rollouts-pod-template-hash"
                            ],
                            [
                                "kubernetes.labels.role"
                            ],
                            [
                                "spanid"
                            ],
                            [
                                "traceid"
                            ],
                            [
                                "json"
                            ],
                            [
                                "content"
                            ],
                            [
                                "logger"
                            ],
                            [
                                "timestamp"
                            ],
                            [
                                "status"
                            ]
                        ],
                        "lineWrapIds": [
                            [
                                "content"
                            ],
                            [
                                "message"
                            ],
                            [
                                "Message"
                            ]
                        ],
                        "columnWidths": {},
                        "colorThresholdTarget": "background"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "timestamp"
                        },
                        "displayedFields": [
                            "content"
                        ]
                    },
                    "histogram": {
                        "dataMappings": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "⏳ Slow Traces",
                "type": "data",
                "query": "fetch spans\n| filter k8s.cluster.uid in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields kubernetesClusterId\n]\nand in(k8s.namespace.name, array($Namespace)) and isFalseOrNull(request.is_failed) and request.is_root_span\n| fields Timestamp=start_time, Namespace=k8s.namespace.name, Workload=dt.kubernetes.workload.name, Service=entityName(dt.entity.service), Endpoint=endpoint.name, Duration=duration, start_time, end_time, trace.id, span.id\n| filterOut contains(Service, \"Requests executed in background threads of \")\n| sort Duration desc\n| limit 20",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "Namespace",
                            "valueAxisLabel": "Duration",
                            "categoryAxis": "Namespace",
                            "valueAxis": "Duration"
                        },
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "Timestamp",
                            "leftAxisValues": [
                                "Duration"
                            ],
                            "leftAxisDimensions": [
                                "Namespace"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "pattern",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "trace.id"
                            ],
                            [
                                "span.id"
                            ],
                            [
                                "span.parent_id"
                            ],
                            [
                                "dt.entity.process_group"
                            ],
                            [
                                "k8s.container.name"
                            ],
                            [
                                "dt.entity.process_group_instance"
                            ],
                            [
                                "dt.kubernetes.workload.kind"
                            ],
                            [
                                "k8s.pod.uid"
                            ],
                            [
                                "dt.kubernetes.cluster.id"
                            ],
                            [
                                "dt.entity.host"
                            ],
                            [
                                "java.jar.file"
                            ],
                            [
                                "k8s.cluster.uid"
                            ],
                            [
                                "spring.profile.name"
                            ],
                            [
                                "k8s.pod.name"
                            ],
                            [
                                "process.executable.path"
                            ],
                            [
                                "dt.entity.container_group"
                            ],
                            [
                                "spring.startup.class"
                            ],
                            [
                                "dt.entity.container_group_instance"
                            ],
                            [
                                "java.jar.path"
                            ],
                            [
                                "dt.agent.module.type"
                            ],
                            [
                                "dt.agent.module.version"
                            ],
                            [
                                "dt.agent.module.version_short"
                            ],
                            [
                                "dt.agent.module.id"
                            ],
                            [
                                "process.pid"
                            ],
                            [
                                "span.is_subroutine"
                            ],
                            [
                                "span.kind"
                            ],
                            [
                                "network.protocol.name"
                            ],
                            [
                                "http.response.status_code"
                            ],
                            [
                                "server.port"
                            ],
                            [
                                "server.resolved_ips"
                            ],
                            [
                                "server.address"
                            ],
                            [
                                "code.function"
                            ],
                            [
                                "code.namespace"
                            ],
                            [
                                "span.timing.cpu"
                            ],
                            [
                                "span.timing.cpu_self"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ],
                            [
                                "dt.entity.cloud_application_namespace"
                            ],
                            [
                                "dt.entity.kubernetes_cluster"
                            ],
                            [
                                "dt.entity.cloud_application"
                            ],
                            [
                                "dt.entity.service"
                            ],
                            [
                                "span.links"
                            ],
                            [
                                "dt.retain.size"
                            ],
                            [
                                "thread.id"
                            ],
                            [
                                "request.is_root_span"
                            ],
                            [
                                "db.system"
                            ],
                            [
                                "db.operation.name"
                            ],
                            [
                                "network.transport"
                            ],
                            [
                                "db.namespace"
                            ],
                            [
                                "db.query.text"
                            ],
                            [
                                "db.affected_item_count"
                            ],
                            [
                                "db.result.duration_sum"
                            ],
                            [
                                "db.result.exception_count"
                            ],
                            [
                                "db.result.execution_count"
                            ],
                            [
                                "http.route"
                            ],
                            [
                                "http.request.header.user-agent"
                            ],
                            [
                                "http.request.header.host"
                            ],
                            [
                                "http.response.header.content-type"
                            ],
                            [
                                "servlet.context.name"
                            ],
                            [
                                "servlet.context.path"
                            ],
                            [
                                "http.server_name"
                            ],
                            [
                                "http.response.header.server"
                            ],
                            [
                                "aggregation.count"
                            ],
                            [
                                "aggregation.exception_count"
                            ],
                            [
                                "aggregation.duration_sum"
                            ],
                            [
                                "aggregation.duration_min"
                            ],
                            [
                                "aggregation.duration_max"
                            ],
                            [
                                "aggregation.parallel_execution"
                            ],
                            [
                                "aggregation.duration_samples"
                            ],
                            [
                                "supportability.atm_sampling_ratio"
                            ],
                            [
                                "sampling.threshold"
                            ],
                            [
                                "http.request.header.x-forwarded-host"
                            ],
                            [
                                "dt.pg_detection.workload.result"
                            ],
                            [
                                "process.executable.name"
                            ],
                            [
                                "http.request.method"
                            ],
                            [
                                "request.id"
                            ],
                            [
                                "host.name"
                            ],
                            [
                                "thread.name"
                            ],
                            [
                                "url.path"
                            ],
                            [
                                "url.scheme"
                            ],
                            [
                                "dt.kubernetes.workload.name"
                            ],
                            [
                                "end_time"
                            ],
                            [
                                "start_time"
                            ]
                        ],
                        "lineWrapIds": [
                            [
                                "content"
                            ],
                            [
                                "Service"
                            ]
                        ],
                        "columnWidths": {
                            "[\"endpoint.name\"]": 1161.40625,
                            "[\"Service\"]": 413,
                            "[\"Failed\"]": 733.546875,
                            "[\"Endpoint\"]": 497.50567626953125
                        }
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "Duration"
                        },
                        "displayedFields": [
                            "Namespace"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "Duration",
                                "rangeAxis": ""
                            }
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 50,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "17": {
                "title": "Process Health",
                "type": "data",
                "query": "fetch dt.entity.process_group_instance\n| filter belongs_to[dt.entity.container_group_instance] in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| lookup [\n  fetch dt.davis.problems\n  | filter event.status==\"ACTIVE\" and dt.davis.is_duplicate==false\n  | expand affected_entity_ids\n], sourceField:id, lookupField:affected_entity_ids, fields:{event.status, display_id, event.start, event.id}\n| fieldsAdd status=if(event.status==\"ACTIVE\", \"Problem\", else: \"OK\")\n| sort event.status asc, entity.name",
                "visualization": "honeycomb",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "entity.name",
                            "valueAxisLabel": "",
                            "categoryAxis": "entity.name",
                            "valueAxis": [],
                            "tooltipVariant": "single"
                        },
                        "hiddenLegendFields": [
                            "entity"
                        ],
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "entity.name",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "0": "h",
                            "1": "i",
                            "2": "d",
                            "3": "d",
                            "4": "e",
                            "5": "n",
                            "hidden": true
                        },
                        "dataMappings": {
                            "value": "status"
                        },
                        "displayedFields": [
                            "entity.name"
                        ],
                        "colorMode": "custom-colors",
                        "colorPalette": "categorical",
                        "customColors": [
                            {
                                "id": 0,
                                "value": "Problem",
                                "comparator": "=",
                                "color": "#CD3741"
                            },
                            {
                                "id": 147809.09999999963,
                                "value": "OK",
                                "comparator": "=",
                                "color": "#0D9C29"
                            }
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "18": {
                "title": "",
                "type": "data",
                "query": "timeseries requests=percentile(dt.service.request.response_time, 50), filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd `P50`=arrayAvg(requests)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "P50",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 100000
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 200000
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "dt.entity.service",
                            "valueAxisLabel": "P50"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.service"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests"
                            ],
                            "leftAxisDimensions": [
                                "P50"
                            ]
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "colorPalette": "purple-rain"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "P50",
                        "prefixIcon": "",
                        "recordField": "P50",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "background",
                        "trend": {
                            "isVisible": false,
                            "isRelative": true,
                            "trendType": "auto"
                        },
                        "sparklineSettings": {
                            "isVisible": false
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            null
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "P50",
                            "unitCategory": "time",
                            "baseUnit": "microsecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721893726115
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "componentState": {
                        "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                        "inputData": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "generalParameters": {
                                    "timeframe": {
                                        "startTime": "now-24h",
                                        "endTime": "now"
                                    },
                                    "resolveDimensionalQueryData": true,
                                    "logVerbosity": "INFO"
                                },
                                "numberOfSignalFluctuations": 1,
                                "alertCondition": "ABOVE",
                                "alertOnMissingData": false,
                                "violatingSamples": 3,
                                "slidingWindow": 5,
                                "dealertingSamples": 5,
                                "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                            }
                        },
                        "analyzerHints": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "unit": {
                                    "unitCategory": "time",
                                    "baseUnit": "microsecond"
                                }
                            }
                        }
                    }
                }
            },
            "19": {
                "title": "",
                "type": "data",
                "query": "timeseries {failures=sum(dt.service.request.failure_count, default:0), requests=sum(dt.service.request.count, default:1)}, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd failureRate = failures[]/requests[]*100\n| fieldsAdd `Failures`=arrayAvg(failureRate)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "Failures",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 1
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 2
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "dt.entity.service.name",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.service"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "failures",
                                "requests",
                                "failureRate"
                            ],
                            "leftAxisDimensions": [
                                "Failures"
                            ]
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "colorPalette": "purple-rain"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Failures",
                        "prefixIcon": "",
                        "recordField": "Failures",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "background",
                        "trend": {
                            "isVisible": false,
                            "isRelative": true,
                            "trendType": "auto"
                        },
                        "sparklineSettings": {
                            "isVisible": false
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            null
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "Failures",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721893726115
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "componentState": {
                        "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                        "inputData": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "generalParameters": {
                                    "timeframe": {
                                        "startTime": "now-24h",
                                        "endTime": "now"
                                    },
                                    "resolveDimensionalQueryData": true,
                                    "logVerbosity": "INFO"
                                },
                                "numberOfSignalFluctuations": 1,
                                "alertCondition": "ABOVE",
                                "alertOnMissingData": false,
                                "violatingSamples": 3,
                                "slidingWindow": 5,
                                "dealertingSamples": 5,
                                "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                            }
                        },
                        "analyzerHints": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "unit": {
                                    "unitCategory": "time",
                                    "baseUnit": "microsecond"
                                }
                            }
                        }
                    }
                }
            },
            "20": {
                "title": "",
                "type": "data",
                "query": "timeseries requests=sum(dt.service.request.count, rate:1m), filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd `Requests`=arrayAvg(requests)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "Requests",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "≥",
                                    "label": ""
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "dt.entity.service.name",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.service"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests"
                            ],
                            "leftAxisDimensions": [
                                "Requests"
                            ]
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "colorPalette": "categorical"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Requests",
                        "prefixIcon": "",
                        "recordField": "Requests",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "background",
                        "trend": {
                            "isVisible": false,
                            "trendType": "auto"
                        },
                        "sparklineSettings": {
                            "showTicks": false,
                            "isVisible": false
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            null
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "Requests",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "/m",
                            "delimiter": false,
                            "added": 1721893726115
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {}
            },
            "22": {
                "title": "",
                "type": "data",
                "query": "fetch dt.davis.problems\n| filter event.status==\"ACTIVE\" and dt.davis.is_duplicate==false\n| fieldsAdd severity=if(\n  event.category==\"AVAILABILITY\", 1, else: if(\n  event.category==\"ERROR\", 2, else: if(\n  event.category==\"SLOWDOWN\", 3, else: if(\n  event.category==\"RESOURCE_CONTENTION\", 4, else: if(\n  event.category==\"CUSTOM_ALERT\", 2)))))\n| sort severity, display_id\n| expand affected_entity_ids\n| filter (in(k8s.cluster.name, $Cluster) and in(k8s.namespace.name, $Namespace)) or affected_entity_ids in [\nfetch dt.entity.process_group_instance\n| filter belongs_to[dt.entity.container_group_instance] in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fields id\n| append [\nfetch dt.entity.service\n| filter belongs_to[dt.entity.cloud_application][0] in [\n  fetch dt.entity.cloud_application\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fields id\n]\n]\n| dedup display_id\n| fieldsRename Start=event.start, ID=display_id, Severity=event.category, Name = event.name, Cluster=k8s.cluster.name, Namespace=k8s.namespace.name, Workload=k8s.workload.name\n| summarize count(), min(severity)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "count()",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 1
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "ID",
                            "valueAxisLabel": "severity"
                        },
                        "hiddenLegendFields": []
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Problems",
                        "recordField": "count()",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "background",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "severity"
                            ],
                            [
                                "affected_entity_ids"
                            ],
                            [
                                "affected_entity_types"
                            ],
                            [
                                "dt.davis.event_ids"
                            ],
                            [
                                "dt.davis.is_duplicate"
                            ],
                            [
                                "dt.davis.is_frequent_event"
                            ],
                            [
                                "dt.davis.mute.status"
                            ],
                            [
                                "event.id"
                            ],
                            [
                                "event.kind"
                            ],
                            [
                                "event.status"
                            ],
                            [
                                "event.status_transition"
                            ],
                            [
                                "labels.alerting_profile"
                            ],
                            [
                                "maintenance.is_under_maintenance"
                            ],
                            [
                                "event.start"
                            ],
                            [
                                "timestamp"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {
                            "[\"Severity\"]": 350.21875
                        },
                        "colorThresholdTarget": "background"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "count()"
                        },
                        "displayedFields": [
                            null
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "count()",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "timeframe": {
                    "tileTimeframe": {
                        "from": "now()-2h",
                        "to": "now()"
                    },
                    "tileTimeframeEnabled": false
                }
            },
            "25": {
                "title": "🔥 CPU Usage",
                "type": "data",
                "query": "timeseries cpu=sum(dt.kubernetes.container.cpu_usage, default:0, rate:1m), by: { k8s.namespace.name, dt.entity.kubernetes_cluster, dt.entity.cloud_application_namespace, dt.entity.cloud_application, k8s.pod.name}, filter:{in(k8s.namespace.name, $Namespace) and in(k8s.cluster.name, $Cluster)}\n| sort k8s.pod.name",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.namespace.name",
                            "categoryAxisLabel": "k8s.namespace.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "cpu"
                            ],
                            "leftAxisDimensions": [
                                "k8s.namespace.name",
                                "dt.entity.kubernetes_cluster",
                                "dt.entity.cloud_application_namespace",
                                "dt.entity.cloud_application",
                                "k8s.pod.name"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8sspace.name",
                            "k8s.pod",
                            "dt.entity.kubernetes_cluster",
                            "dt.entity.cloud_application_namespace",
                            "dt.entity.cloud_application",
                            "dt.entity.cloud_application_instance"
                        ],
                        "legend": {
                            "hidden": true
                        },
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "valueRepresentation": "absolute",
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Kubernetes: Container - CPU usage"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "k8s.namespace.name",
                        "prefixIcon": "",
                        "recordField": "k8s.namespace.name",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            "k8s.namespace.name"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "displayedFields": [
                            "k8s.namespace.name",
                            "dt.entity.kubernetes_cluster",
                            "dt.entity.cloud_application_namespace",
                            "dt.entity.cloud_application",
                            "k8s.pod.name"
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {}
            },
            "26": {
                "title": "📃 Memory Usage",
                "type": "data",
                "query": "timeseries memory=sum(dt.kubernetes.container.memory_working_set, default:0, rate:1m), by: { k8s.namespace.name, dt.entity.kubernetes_cluster, dt.entity.cloud_application_namespace, dt.entity.cloud_application, k8s.pod.name }, filter:{in(k8s.namespace.name, $Namespace) and in(k8s.cluster.name, $Cluster)}\n| sort k8s.pod.name",
                "visualization": "areaChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.namespace.name",
                            "categoryAxisLabel": "k8s.namespace.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "memory",
                                "limits"
                            ],
                            "leftAxisDimensions": [
                                "k8s.namespace.name",
                                "k8s.pod.name",
                                "dt.entity.kubernetes_cluster",
                                "dt.entity.cloud_application_namespace",
                                "dt.entity.cloud_application",
                                "dt.entity.cloud_application_instance"
                            ]
                        },
                        "legend": {
                            "hidden": true
                        },
                        "hiddenLegendFields": [
                            "k8sspace.name",
                            "k8s.pod",
                            "dt.entity.kubernetes_cluster",
                            "dt.entity.cloud_application_namespace",
                            "dt.entity.cloud_application",
                            "dt.entity.cloud_application_instance"
                        ]
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "k8s.namespace.name",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "k8s.namespace.name"
                        },
                        "displayedFields": [
                            "k8s.namespace.name"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "componentState": {
                        "selectedAnalyzerName": "dt.statistics.ui.ForecastAnalyzer",
                        "inputData": {
                            "dt.statistics.ui.ForecastAnalyzer": {
                                "generalParameters": {
                                    "timeframe": {
                                        "startTime": "now-2h",
                                        "endTime": "now"
                                    },
                                    "resolveDimensionalQueryData": true,
                                    "logVerbosity": "INFO"
                                },
                                "forecastHorizon": 100,
                                "forecastOffset": 1,
                                "query": "timeseries memory=sum(dt.kubernetes.container.memory_working_set, default:0, rate:1m), by: { k8s.namespace.name, dt.entity.kubernetes_cluster, dt.entity.cloud_application_namespace, dt.entity.cloud_application, k8s.pod.name }, filter:{in(k8s.namespace.name, \"admin-order-experience-service\") and in(k8s.cluster.name, \"prod-mot-eks\")}\n| sort k8s.pod.name"
                            }
                        }
                    }
                }
            },
            "27": {
                "title": "⚓ K8s events",
                "type": "data",
                "query": "fetch events\n  | filter event.provider == \"KUBERNETES_EVENT\" and in(dt.entity.kubernetes_cluster.name, $Cluster) and in(dt.entity.cloud_application_namespace.name, $Namespace)\n  | dedup event.id, sort:{timestamp desc}\n  | fieldsAdd\n    event.category = if(isNull(status), event.category, else: status),\n    event.description = if(isNull(event.description), dt.kubernetes.event.message, else: event.description),\n    event.reason = dt.kubernetes.event.reason\n  | fieldsAdd  event.description = if(isNull(event.reason), event.description, else:concat(event.reason,\": \", event.description))\n  | sort timestamp desc\n  | fieldsRename Timestamp=timestamp, Status=event.category, Description=event.description",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "Status",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "INFO"
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "WARN"
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "ERROR"
                                },
                                {
                                    "id": 3,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-04-default, #d85a9f)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "NONE"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.cloud_application.name",
                            "categoryAxisLabel": "dt.entity.cloud_application.name",
                            "valueAxis": "dt.davis.timeout",
                            "valueAxisLabel": "dt.davis.timeout"
                        },
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "Timestamp",
                            "leftAxisValues": [
                                "dt.davis.timeout"
                            ],
                            "leftAxisDimensions": [
                                "Status"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "timestamp",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "event.reason"
                            ],
                            [
                                "affected_entity_ids"
                            ],
                            [
                                "affected_entity_types"
                            ],
                            [
                                "dt.davis.disable_merging_reason"
                            ],
                            [
                                "dt.davis.impact_level"
                            ],
                            [
                                "dt.davis.is_frequent_event"
                            ],
                            [
                                "dt.davis.is_merging_allowed"
                            ],
                            [
                                "dt.davis.is_rootcause_relevant"
                            ],
                            [
                                "dt.davis.mute.status"
                            ],
                            [
                                "dt.davis.timeout"
                            ],
                            [
                                "dt.entity.cloud_application"
                            ],
                            [
                                "dt.entity.cloud_application.name"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ],
                            [
                                "dt.entity.cloud_application_instance.name"
                            ],
                            [
                                "dt.entity.cloud_application_namespace"
                            ],
                            [
                                "dt.entity.cloud_application_namespace.name"
                            ],
                            [
                                "dt.entity.kubernetes_cluster"
                            ],
                            [
                                "dt.entity.kubernetes_cluster.name"
                            ],
                            [
                                "dt.entity.kubernetes_node"
                            ],
                            [
                                "dt.entity.kubernetes_node.name"
                            ],
                            [
                                "dt.kubernetes.cluster.app_enabled"
                            ],
                            [
                                "dt.kubernetes.cluster.name"
                            ],
                            [
                                "dt.kubernetes.event.count"
                            ],
                            [
                                "dt.kubernetes.event.first_seen"
                            ],
                            [
                                "dt.kubernetes.event.important"
                            ],
                            [
                                "dt.kubernetes.event.involved_object.kind"
                            ],
                            [
                                "dt.kubernetes.event.involved_object.name"
                            ],
                            [
                                "dt.kubernetes.event.last_seen"
                            ],
                            [
                                "dt.kubernetes.event.message"
                            ],
                            [
                                "dt.kubernetes.event.reason"
                            ],
                            [
                                "dt.kubernetes.event.uid"
                            ],
                            [
                                "dt.kubernetes.node.name"
                            ],
                            [
                                "dt.kubernetes.workload.name"
                            ],
                            [
                                "dt.source_entity"
                            ],
                            [
                                "dt.source_entity.type"
                            ],
                            [
                                "event.end"
                            ],
                            [
                                "event.group_label"
                            ],
                            [
                                "event.id"
                            ],
                            [
                                "event.kind"
                            ],
                            [
                                "event.name"
                            ],
                            [
                                "event.provider"
                            ],
                            [
                                "event.start"
                            ],
                            [
                                "event.status"
                            ],
                            [
                                "event.status_transition"
                            ],
                            [
                                "event.type"
                            ],
                            [
                                "k8s.namespace.name"
                            ],
                            [
                                "k8s.pod.name"
                            ],
                            [
                                "maintenance.is_under_maintenance"
                            ],
                            [
                                "related_entity_ids"
                            ],
                            [
                                "status"
                            ]
                        ],
                        "lineWrapIds": [
                            [
                                "Description"
                            ]
                        ],
                        "columnWidths": {},
                        "colorThresholdTarget": "background"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "Timestamp"
                        },
                        "displayedFields": [
                            "Status"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "dt.davis.timeout",
                                "rangeAxis": ""
                            }
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "28": {
                "title": "⛔ Failed Traces",
                "type": "data",
                "query": "fetch spans\n| filter k8s.cluster.uid in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields kubernetesClusterId\n]\nand in(k8s.namespace.name, array($Namespace)) and request.is_failed and request.is_root_span\n| fields Timestamp=start_time, Namespace=k8s.namespace.name, Workload=dt.kubernetes.workload.name, Service=entityName(dt.entity.service), Endpoint=endpoint.name, Duration=duration, start_time, end_time, trace.id, span.id\n| filterOut contains(Service, \"Requests executed in background threads of \")\n| sort end_time desc\n| limit 20",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "Namespace",
                            "valueAxisLabel": "Duration",
                            "categoryAxis": "Namespace",
                            "valueAxis": "Duration"
                        },
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "Timestamp",
                            "leftAxisValues": [
                                "Duration"
                            ],
                            "leftAxisDimensions": [
                                "Namespace"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "pattern",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "trace.id"
                            ],
                            [
                                "span.id"
                            ],
                            [
                                "span.parent_id"
                            ],
                            [
                                "dt.entity.process_group"
                            ],
                            [
                                "k8s.container.name"
                            ],
                            [
                                "dt.entity.process_group_instance"
                            ],
                            [
                                "dt.kubernetes.workload.kind"
                            ],
                            [
                                "k8s.pod.uid"
                            ],
                            [
                                "dt.kubernetes.cluster.id"
                            ],
                            [
                                "dt.entity.host"
                            ],
                            [
                                "java.jar.file"
                            ],
                            [
                                "k8s.cluster.uid"
                            ],
                            [
                                "spring.profile.name"
                            ],
                            [
                                "k8s.pod.name"
                            ],
                            [
                                "process.executable.path"
                            ],
                            [
                                "dt.entity.container_group"
                            ],
                            [
                                "spring.startup.class"
                            ],
                            [
                                "dt.entity.container_group_instance"
                            ],
                            [
                                "java.jar.path"
                            ],
                            [
                                "dt.agent.module.type"
                            ],
                            [
                                "dt.agent.module.version"
                            ],
                            [
                                "dt.agent.module.version_short"
                            ],
                            [
                                "dt.agent.module.id"
                            ],
                            [
                                "process.pid"
                            ],
                            [
                                "span.is_subroutine"
                            ],
                            [
                                "span.kind"
                            ],
                            [
                                "network.protocol.name"
                            ],
                            [
                                "http.response.status_code"
                            ],
                            [
                                "server.port"
                            ],
                            [
                                "server.resolved_ips"
                            ],
                            [
                                "server.address"
                            ],
                            [
                                "code.function"
                            ],
                            [
                                "code.namespace"
                            ],
                            [
                                "span.timing.cpu"
                            ],
                            [
                                "span.timing.cpu_self"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ],
                            [
                                "dt.entity.cloud_application_namespace"
                            ],
                            [
                                "dt.entity.kubernetes_cluster"
                            ],
                            [
                                "dt.entity.cloud_application"
                            ],
                            [
                                "dt.entity.service"
                            ],
                            [
                                "span.links"
                            ],
                            [
                                "dt.retain.size"
                            ],
                            [
                                "thread.id"
                            ],
                            [
                                "request.is_root_span"
                            ],
                            [
                                "db.system"
                            ],
                            [
                                "db.operation.name"
                            ],
                            [
                                "network.transport"
                            ],
                            [
                                "db.namespace"
                            ],
                            [
                                "db.query.text"
                            ],
                            [
                                "db.affected_item_count"
                            ],
                            [
                                "db.result.duration_sum"
                            ],
                            [
                                "db.result.exception_count"
                            ],
                            [
                                "db.result.execution_count"
                            ],
                            [
                                "http.route"
                            ],
                            [
                                "http.request.header.user-agent"
                            ],
                            [
                                "http.request.header.host"
                            ],
                            [
                                "http.response.header.content-type"
                            ],
                            [
                                "servlet.context.name"
                            ],
                            [
                                "servlet.context.path"
                            ],
                            [
                                "http.server_name"
                            ],
                            [
                                "http.response.header.server"
                            ],
                            [
                                "aggregation.count"
                            ],
                            [
                                "aggregation.exception_count"
                            ],
                            [
                                "aggregation.duration_sum"
                            ],
                            [
                                "aggregation.duration_min"
                            ],
                            [
                                "aggregation.duration_max"
                            ],
                            [
                                "aggregation.parallel_execution"
                            ],
                            [
                                "aggregation.duration_samples"
                            ],
                            [
                                "supportability.atm_sampling_ratio"
                            ],
                            [
                                "sampling.threshold"
                            ],
                            [
                                "http.request.header.x-forwarded-host"
                            ],
                            [
                                "dt.pg_detection.workload.result"
                            ],
                            [
                                "process.executable.name"
                            ],
                            [
                                "http.request.method"
                            ],
                            [
                                "request.id"
                            ],
                            [
                                "host.name"
                            ],
                            [
                                "thread.name"
                            ],
                            [
                                "url.path"
                            ],
                            [
                                "url.scheme"
                            ],
                            [
                                "dt.kubernetes.workload.name"
                            ],
                            [
                                "end_time"
                            ],
                            [
                                "start_time"
                            ]
                        ],
                        "lineWrapIds": [
                            [
                                "content"
                            ]
                        ],
                        "columnWidths": {
                            "[\"endpoint.name\"]": 1161.40625,
                            "[\"Service\"]": 413,
                            "[\"Failed\"]": 733.546875,
                            "[\"Endpoint\"]": 826.859375
                        }
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "Duration"
                        },
                        "displayedFields": [
                            "Namespace"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "Duration",
                                "rangeAxis": ""
                            }
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 50,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "29": {
                "title": "🧑‍🔧 In case of Problems, please reach out to",
                "type": "data",
                "query": "fetch dt.entity.cloud_application_namespace\n| filter in(entity.name, $Namespace)\n| fields Team=kubernetesAnnotations[`careem.com/team`], `Slack Channel`=concat(\"#\", kubernetesAnnotations[`careem.com/slack-channel`]), `VictorOps`=kubernetesAnnotations[`careem.com/victor-ops`]\n| dedup Team, `Slack Channel`\n| sort Team",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "count()",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 1
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "Team",
                            "valueAxisLabel": "",
                            "categoryAxis": "Team",
                            "valueAxis": [],
                            "tooltipVariant": "single"
                        },
                        "hiddenLegendFields": [],
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "Problems",
                        "prefixIcon": "",
                        "recordField": "slack",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "background"
                    },
                    "table": {
                        "rowDensity": "default",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "severity"
                            ],
                            [
                                "affected_entity_ids"
                            ],
                            [
                                "affected_entity_types"
                            ],
                            [
                                "dt.davis.event_ids"
                            ],
                            [
                                "dt.davis.is_duplicate"
                            ],
                            [
                                "dt.davis.is_frequent_event"
                            ],
                            [
                                "dt.davis.mute.status"
                            ],
                            [
                                "event.id"
                            ],
                            [
                                "event.kind"
                            ],
                            [
                                "event.status"
                            ],
                            [
                                "event.status_transition"
                            ],
                            [
                                "labels.alerting_profile"
                            ],
                            [
                                "maintenance.is_under_maintenance"
                            ],
                            [
                                "event.start"
                            ],
                            [
                                "timestamp"
                            ],
                            [
                                "VictorOps"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {
                            "[\"Severity\"]": 350.21875
                        },
                        "colorThresholdTarget": "value"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "Team"
                        },
                        "displayedFields": [
                            "Team"
                        ]
                    },
                    "histogram": {
                        "dataMappings": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "timeframe": {
                    "tileTimeframeEnabled": true,
                    "tileTimeframe": {
                        "from": "now()-2h",
                        "to": "now()"
                    }
                }
            },
            "30": {
                "type": "markdown",
                "content": "![Careem](https://dt-cdn.net/images/careem-wordmark-forest-green-rgb-8afa98b9ab.svg)"
            },
            "31": {
                "title": "",
                "type": "data",
                "query": "timeseries requests=percentile(dt.service.request.response_time, 90), filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd `P90`=arrayAvg(requests)",
                "visualization": "singleValue",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "P90",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 1000000
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 2000000
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "dt.entity.service.name",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.service"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "requests"
                            ],
                            "leftAxisDimensions": [
                                "P90"
                            ]
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "colorPalette": "purple-rain"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "P90",
                        "prefixIcon": "",
                        "recordField": "P90",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "background",
                        "trend": {
                            "isVisible": false,
                            "isRelative": true,
                            "trendType": "auto"
                        },
                        "sparklineSettings": {
                            "isVisible": false
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "interval"
                        },
                        "displayedFields": [
                            null
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "P90",
                            "unitCategory": "time",
                            "baseUnit": "microsecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721893726115
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "componentState": {
                        "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                        "inputData": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "generalParameters": {
                                    "timeframe": {
                                        "startTime": "now-24h",
                                        "endTime": "now"
                                    },
                                    "resolveDimensionalQueryData": true,
                                    "logVerbosity": "INFO"
                                },
                                "numberOfSignalFluctuations": 1,
                                "alertCondition": "ABOVE",
                                "alertOnMissingData": false,
                                "violatingSamples": 3,
                                "slidingWindow": 5,
                                "dealertingSamples": 5,
                                "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                            }
                        },
                        "analyzerHints": {
                            "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                                "unit": {
                                    "unitCategory": "time",
                                    "baseUnit": "microsecond"
                                }
                            }
                        }
                    }
                }
            },
            "32": {
                "title": "🛑 Problems",
                "type": "data",
                "query": "fetch dt.davis.problems\n| filter dt.davis.is_duplicate==false\n| fieldsAdd dt.entity.cloud_application = if(startsWith(affected_entity_ids[], \"CLOUD_APPLICATION-\"), affected_entity_ids[]), dt.entity.cloud_application_instance = if(startsWith(affected_entity_ids[], \"CLOUD_APPLICATION_INSTANCE-\"), affected_entity_ids[]), dt.entity.kubernetes_cluster = if(startsWith(affected_entity_ids[], \"KUBERNETES_CLUSTER-\"), affected_entity_ids[]),dt.entity.kubernetes_node = if(startsWith(affected_entity_ids[], \"KUBERNETES_NODE-\"), affected_entity_ids[]),dt.entity.service = if(startsWith(affected_entity_ids[], \"SERVICE-\"), affected_entity_ids[]),dt.entity.process_group = if(startsWith(affected_entity_ids[], \"PROCESS_GROUP-\"), affected_entity_ids[]),dt.entity.process_group_instance = if(startsWith(affected_entity_ids[], \"PROCESS_GROUP_INSTANCE-\"), affected_entity_ids[]),dt.entity.host = if(startsWith(affected_entity_ids[], \"HOST-\"), affected_entity_ids[])\n| fieldsAdd dt.entity.cloud_application = dt.entity.cloud_application[0], dt.entity.cloud_application_instance = dt.entity.cloud_application_instance[0], dt.entity.kubernetes_cluster=dt.entity.kubernetes_cluster[0], dt.entity.kubernetes_node=dt.entity.kubernetes_node[0],dt.entity.service=dt.entity.service[0],dt.entity.process_group=dt.entity.process_group[0],dt.entity.process_group_instance=dt.entity.process_group_instance[0],dt.entity.host=dt.entity.host[0]\n| fieldsAdd severity=if(\n  event.category==\"AVAILABILITY\", 1, else: if(\n  event.category==\"ERROR\", 2, else: if(\n  event.category==\"SLOWDOWN\", 3, else: if(\n  event.category==\"RESOURCE_CONTENTION\", 4, else: if(\n  event.category==\"CUSTOM_ALERT\", 2)))))\n| sort severity, display_id\n| expand affected_entity_ids\n| filter (in(k8s.cluster.name, $Cluster) and in(k8s.namespace.name, $Namespace)) or affected_entity_ids in [\nfetch dt.entity.process_group_instance\n| filter belongs_to[dt.entity.container_group_instance] in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| sort entity.name\n| fields id\n| append [\nfetch dt.entity.service\n| filter belongs_to[dt.entity.cloud_application][0] in [\n  fetch dt.entity.cloud_application\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| sort entity.name\n| fields id\n]\n]\n| dedup display_id\n| fieldsRename Status=event.status, Start=event.start, ID=display_id, Severity=event.category, Name = event.name, Cluster=k8s.cluster.name, Namespace=k8s.namespace.name, Workload=k8s.workload.name, `Affected Users`=dt.davis.affected_users_count\n| fieldsAdd `Root Cause`=coalesce(root_cause_entity_name, \"Unknown\")\n| sort Status, Severity, Start desc",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "Severity",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "AVAILABILITY"
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "SLOWDOWN"
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-01-default, #134fc9)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "RESOURCE_CONTENTION"
                                },
                                {
                                    "id": 3,
                                    "color": "#CD3741",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "ERROR"
                                },
                                {
                                    "id": 4,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-color-04-default, #d85a9f)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "CUSTOM_ALERT"
                                }
                            ]
                        },
                        {
                            "id": 2,
                            "field": "Status",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "CLOSED"
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "ACTIVE"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxisLabel": "dt.entity.service",
                            "valueAxisLabel": "severity",
                            "categoryAxis": "dt.entity.service",
                            "valueAxis": "severity"
                        },
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "timestamp",
                            "leftAxisValues": [
                                "severity"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.service"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "recordField": "timestamp"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "timestamp"
                            ],
                            [
                                "severity"
                            ],
                            [
                                "affected_entity_types"
                            ],
                            [
                                "dt.davis.event_ids"
                            ],
                            [
                                "dt.davis.is_duplicate"
                            ],
                            [
                                "dt.davis.is_frequent_event"
                            ],
                            [
                                "dt.davis.mute.status"
                            ],
                            [
                                "event.id"
                            ],
                            [
                                "event.kind"
                            ],
                            [
                                "event.status"
                            ],
                            [
                                "event.status_transition"
                            ],
                            [
                                "labels.alerting_profile"
                            ],
                            [
                                "maintenance.is_under_maintenance"
                            ],
                            [
                                "entity_tags"
                            ],
                            [
                                "event.end"
                            ],
                            [
                                "resolved_problem_duration"
                            ],
                            [
                                "Affected Users"
                            ],
                            [
                                "root_cause_entity_id"
                            ],
                            [
                                "root_cause_entity_name"
                            ],
                            [
                                "dt.entity.cloud_application"
                            ],
                            [
                                "dt.entity.cloud_application_instance"
                            ],
                            [
                                "dt.entity.kubernetes_cluster"
                            ],
                            [
                                "dt.entity.kubernetes_node"
                            ],
                            [
                                "dt.entity.service"
                            ],
                            [
                                "dt.entity.process_group"
                            ],
                            [
                                "dt.entity.process_group_instance"
                            ],
                            [
                                "affected_entity_ids"
                            ],
                            [
                                "dt.entity.host"
                            ],
                            [
                                "Cluster"
                            ],
                            [
                                "Namespace"
                            ],
                            [
                                "Workload"
                            ],
                            [
                                "dt.davis.last_reopen_timestamp"
                            ],
                            [
                                "related_entity_ids"
                            ],
                            [
                                "event.description"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {},
                        "colorThresholdTarget": "background"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "blue",
                        "dataMappings": {
                            "value": "severity"
                        },
                        "displayedFields": [
                            "dt.entity.service"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "severity",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "resolved_problem_duration",
                                "rangeAxis": ""
                            }
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "33": {
                "title": "💾 PVC Volume Usage",
                "type": "data",
                "query": "timeseries pvc_used = sum(dt.kubernetes.persistentvolumeclaim.used, rollup:avg), by:{k8s.persistent_volume_claim.name}, filter: in(k8s.cluster.name, $Cluster) and in(k8s.namespace.name, $Namespace), from: -1h | lookup [timeseries capacity = sum(dt.kubernetes.persistentvolumeclaim.capacity, rollup:avg), by:{k8s.persistent_volume_claim.name}, filter: in(k8s.cluster.name, $Cluster) and in(k8s.namespace.name, $Namespace) ], sourceField:k8s.persistent_volume_claim.name, lookupField:k8s.persistent_volume_claim.name, fields:{capacity}\n| fieldsAdd result = (pvc_used[] / capacity[]) * 100\n| fieldsRemove pvc_used, capacity \n| sort result desc \n| limit 10",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "k8s.persistent_volume_claim.name",
                            "valueAxis": "interval",
                            "categoryAxisLabel": "k8s.persistent_volume_claim.name",
                            "valueAxisLabel": "interval"
                        },
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "result"
                            ],
                            "leftAxisDimensions": [
                                "k8s.persistent_volume_claim.name"
                            ]
                        },
                        "hiddenLegendFields": [
                            "k8s.persistent_volume_claim"
                        ],
                        "leftYAxisSettings": {
                            "max": 100,
                            "scale": "linear"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "error",
                        "prefixIcon": "",
                        "recordField": "error",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        }
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "k8s.persistent_volume_claim.name"
                        },
                        "displayedFields": [
                            "k8s.persistent_volume_claim.name"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [],
                        "variant": "single",
                        "displayedFields": []
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "result",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721973606503
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "componentState": {
                        "selectedAnalyzerName": "dt.statistics.ui.ForecastAnalyzer",
                        "inputData": {
                            "dt.statistics.ui.ForecastAnalyzer": {
                                "generalParameters": {
                                    "timeframe": {
                                        "startTime": "now-2h",
                                        "endTime": "now"
                                    },
                                    "resolveDimensionalQueryData": true,
                                    "logVerbosity": "INFO"
                                },
                                "forecastHorizon": 100,
                                "forecastOffset": 1
                            }
                        },
                        "analyzerHints": {
                            "dt.statistics.ui.ForecastAnalyzer": {
                                "unit": {
                                    "unitCategory": "data",
                                    "baseUnit": "byte"
                                }
                            }
                        }
                    },
                    "davisVisualization": {
                        "isAvailable": true,
                        "settings": {
                            "visibleSections": "VISUALIZATION"
                        }
                    }
                }
            },
            "34": {
                "title": "🧩 Components",
                "type": "data",
                "query": "fetch dt.entity.service\n| filter belongs_to[dt.entity.cloud_application][0] in [\n  fetch dt.entity.cloud_application\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| sort entity.name\n| fields Type=\"Service\", Name=entity.name, id\n| append [\nfetch dt.entity.process_group_instance\n| filter belongs_to[dt.entity.container_group_instance] in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| sort entity.name\n| fields Type=\"Process\", Name=entity.name, id\n]\n| append [\nfetch dt.entity.cloud_application\n| filter clustered_by[dt.entity.kubernetes_cluster] in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields id\n]\n| filter in(namespaceName, $Namespace)\n| sort entity.name\n| fields Type=\"Workload\", Name=entity.name, id\n]\n| append [\nfetch dt.entity.kubernetes_service\n| filter clustered_by[dt.entity.kubernetes_cluster] in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields id\n]\n| filter in(namespaceName, $Namespace)\n| sort entity.name\n| fields Type=\"K8s Service\", Name=entity.name, id\n]\n| append [\n  fetch dt.entity.cloud_application_instance\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields Type=\"Pod\", Name=entity.name, id\n]",
                "visualization": "honeycomb",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "Type",
                            "categoryAxisLabel": "Type",
                            "valueAxis": [],
                            "valueAxisLabel": "",
                            "tooltipVariant": "single"
                        },
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "id"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {
                            "[\"Type\"]": 112.94033813476562
                        }
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "purple-rain",
                        "dataMappings": {
                            "value": "Type"
                        },
                        "displayedFields": [
                            "Name"
                        ]
                    },
                    "histogram": {
                        "dataMappings": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "35": {
                "title": "",
                "type": "data",
                "query": "fetch dt.entity.service\n| filter belongs_to[dt.entity.cloud_application][0] in [\n  fetch dt.entity.cloud_application\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| sort entity.name\n| fields Type=\"Service\", Name=entity.name, id\n| append [\nfetch dt.entity.process_group_instance\n| filter belongs_to[dt.entity.container_group_instance] in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| sort entity.name\n| fields Type=\"Process\", Name=entity.name, id\n]\n| append [\nfetch dt.entity.cloud_application\n| filter clustered_by[dt.entity.kubernetes_cluster] in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields id\n]\n| filter in(namespaceName, $Namespace)\n| sort entity.name\n| fields Type=\"Workload\", Name=entity.name, id\n]\n| append [\nfetch dt.entity.kubernetes_service\n| filter clustered_by[dt.entity.kubernetes_cluster] in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields id\n]\n| filter in(namespaceName, $Namespace)\n| sort entity.name\n| fields Type=\"K8s Service\", Name=entity.name, id\n]\n| append [\n  fetch dt.entity.cloud_application_instance\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields Type=\"Pod\", Name=entity.name, id\n]",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "xAxisScaling": "analyzedTimeframe",
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "Type",
                            "categoryAxisLabel": "Type",
                            "valueAxis": [],
                            "valueAxisLabel": "",
                            "tooltipVariant": "single"
                        },
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "entity.name",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [
                            [
                                "id"
                            ]
                        ],
                        "lineWrapIds": [],
                        "columnWidths": {
                            "[\"Type\"]": 112.94033813476562
                        }
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "Type"
                        },
                        "displayedFields": [
                            "Type"
                        ]
                    },
                    "histogram": {
                        "dataMappings": []
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "39": {
                "title": "JVM Memory",
                "type": "data",
                "query": "timeseries free=avg(dt.runtime.jvm.memory.free),total=avg(dt.runtime.jvm.memory.total), by: { dt.entity.process_group_instance}, filter: dt.entity.container_group_instance in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fieldsAdd dt.entity.process_group_instance.name = entityName(dt.entity.process_group_instance)\n| fieldsAdd `Memory used %`=100-free[]/total[]*100\n| fieldsRemove free, total\n| sort arrayAvg(`Memory used %`) desc\n| limit 20",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "Memory used %"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.process_group_instance",
                                "dt.entity.process_group_instance.name"
                            ]
                        },
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.process_group_instance.name",
                            "valueAxis": "interval",
                            "categoryAxisLabel": "dt.entity.process_group_instance.name",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.process_group_instance"
                        ],
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "dt.entity.process_group_instance",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "dt.entity.process_group_instance"
                        },
                        "displayedFields": [
                            "dt.entity.process_group_instance"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "Memory used %",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1721990934091
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {}
            },
            "40": {
                "title": "Garbage Collection Time",
                "type": "data",
                "query": "timeseries `Garbage Collection Time`=avg(dt.runtime.jvm.gc.collection_time), by: { dt.entity.process_group_instance }, filter: dt.entity.container_group_instance in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fieldsAdd dt.entity.process_group_instance.name = entityName(dt.entity.process_group_instance)\n| sort arrayAvg(`Garbage Collection Time`) desc\n| limit 20",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "Garbage Collection Time"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.process_group_instance",
                                "dt.entity.process_group_instance.name"
                            ]
                        },
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.process_group_instance.name",
                            "valueAxis": "interval",
                            "categoryAxisLabel": "dt.entity.process_group_instance.name",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.process_group_instance"
                        ],
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "dt.entity.process_group_instance",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "dt.entity.process_group_instance"
                        },
                        "displayedFields": [
                            "dt.entity.process_group_instance"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {}
            },
            "41": {
                "title": "Go Routines",
                "type": "data",
                "query": "timeseries Goroutines=avg(dt.runtime.go.scheduler.goroutine_count), by: { dt.entity.process_group }, filter: dt.entity.container_group_instance in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fieldsAdd dt.entity.process_group.name = entityName(dt.entity.process_group)\n| sort arrayAvg(Goroutines)\n| limit 20",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "dt.entity.process_group.name",
                            "categoryAxisLabel": "dt.entity.process_group.name",
                            "valueAxis": "interval",
                            "valueAxisLabel": "interval"
                        },
                        "hiddenLegendFields": [
                            "dt.entity.process_group"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "Goroutines"
                            ],
                            "leftAxisDimensions": [
                                "dt.entity.process_group",
                                "dt.entity.process_group.name"
                            ]
                        },
                        "legend": {
                            "hidden": true
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "dt.entity.process_group",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "lineWrapIds": [],
                        "columnWidths": {}
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "dt.entity.process_group"
                        },
                        "displayedFields": [
                            "dt.entity.process_group"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ]
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {}
            },
            "42": {
                "title": "💀 Exceptions",
                "type": "data",
                "query": "fetch spans\n| filter k8s.cluster.uid in [\n  fetch dt.entity.kubernetes_cluster\n  | filter in(entity.name, $Cluster)\n  | fields kubernetesClusterId\n]\nand in(k8s.namespace.name, array($Namespace))\n// only spans which contain a span event of type \"exception\"\n| filter iAny(span.events[][span_event.name] == \"exception\")\n\n// make exception type top level attribute\n| expand span.events | fieldsFlatten span.events, fields: { exception.type, exception.message }\n\n| summarize Count=count(),trace=takeAny(record(start_time, trace.id)), by: { request.is_failed, `Exception Type`=exception.type, `Message`=exception.message, dt.entity.service }\n| fields `Request Status`=if(request.is_failed, \"FAILED\", else: if(isNull(request.is_failed), \"N/A\", else: \"OK\")), Count, `Exception Type`, Message, dt.entity.service, trace.id=trace[trace.id], start_time=trace[start_time]\n| sort `Request Status`, Count desc",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "Request Status",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": "#0D9C29",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "OK"
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "=",
                                    "label": "",
                                    "value": "N/A"
                                },
                                {
                                    "id": 2,
                                    "color": "#CD3741",
                                    "comparator": "=",
                                    "label": "",
                                    "value": "FAILED"
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "connect",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "categoricalBarChartSettings": {
                            "categoryAxis": "Request Status",
                            "categoryAxisLabel": "Request Status",
                            "valueAxis": "Count",
                            "valueAxisLabel": "Count"
                        },
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "start_time",
                            "leftAxisValues": [
                                "Count"
                            ],
                            "leftAxisDimensions": [
                                "Request Status"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "",
                        "recordField": "exception.type",
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": true,
                        "hiddenColumns": [
                            [
                                "timeframe"
                            ],
                            [
                                "interval"
                            ],
                            [
                                "dt.entity.service"
                            ],
                            [
                                "trace.id"
                            ],
                            [
                                "start_time"
                            ]
                        ],
                        "lineWrapIds": [
                            [
                                "Message"
                            ]
                        ],
                        "columnWidths": {
                            "[\"Exceptions\"]": 1269.296875
                        },
                        "colorThresholdTarget": "background"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto"
                        },
                        "colorMode": "color-palette",
                        "colorPalette": "categorical",
                        "dataMappings": {
                            "value": "Request Status"
                        },
                        "displayedFields": [
                            "Request Status"
                        ]
                    },
                    "histogram": {
                        "dataMappings": [
                            {
                                "valueAxis": "Count",
                                "rangeAxis": ""
                            }
                        ]
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "Count",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1722452533169
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "43": {
                "type": "data",
                "title": "Kafka Consumer Throughput",
                "query": "timeseries value = sum(kafka_consumer_message_seconds, rollup:total, rate: 1m), \n  interval:1m, by:{c_service, status, name},\n  filter:{in(c_service, $Namespace)}\n| append [\n  timeseries value = sum(kafka_message_consumed_total, rate: 1m), \n    interval:1m, by:{c_service, topic},\n    filter:{in(c_service, $Namespace)}\n  | fieldsRename name = topic\n]\n| fields timeframe, interval, value, name, status",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "value"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "value"
                                ],
                                "value": "sparkline",
                                "id": 1745342358096
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 20
                    }
                },
                "visualization": "lineChart",
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "44": {
                "type": "data",
                "title": "Kafka Consumer Message Age",
                "query": " timeseries value = avg(kafka_consumer_message_age, rate: 1m), \n   interval:1m, by:{c_service, name},\n   filter:{in(c_service, $Namespace)}\n| fields timeframe, interval, value, name",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "value"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kafka_consumer_message_age"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "value"
                                ],
                                "value": "sparkline",
                                "id": 1745342379185
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "value",
                            "unitCategory": "time",
                            "baseUnit": "second",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1745342785309
                        }
                    ]
                },
                "visualization": "lineChart",
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "45": {
                "type": "data",
                "title": "Kafka Consumer Latency",
                "query": "timeseries value = avg(kafka_consumer_message_seconds, rate: 1m), \n  interval:1m, by:{c_service, status, name},\n  filter:{in(c_service, $Namespace)}\n| append [\n  timeseries value = sum(kafka_lag, rate: 1m), \n    interval:1m, by:{c_service, topic},\n    filter:{in(c_service, $Namespace)}\n  | fieldsRename name = topic\n]\n| fields timeframe, interval, value, name",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "value"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kafka_consumer_message_seconds"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "value"
                                ],
                                "value": "sparkline",
                                "id": 1745342389251
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "value",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1745342693519
                        }
                    ]
                },
                "visualization": "lineChart",
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "46": {
                "type": "data",
                "title": "Kafka Consumer Message Dropped",
                "query": "timeseries value = sum(kafka_consumer_message_dropped, rate: 1m), \n   interval:1m, by:{c_service, name},\n   filter:{in(c_service, $Namespace)}\n| append [\n   timeseries value = sum(kafka_consumer_message_dropped_total, rate: 1m), \n   interval:1m, by:{c_service, name},\n   filter:{in(c_service, $Namespace)}\n]\n| fields timeframe, interval, value, name",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "value"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "kafka_consumer_message_age"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "visualization": "recordView",
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "47": {
                "type": "data",
                "title": "Kafka Producer Throughput",
                "query": "timeseries value = sum(kafka_producer_message, rate: 1m), \n  interval:1m, by:{c_service, status, name},\n  filter:{in(c_service, $Namespace)}\n| append [\n  timeseries value = sum(kafka_message_produced_total, rate: 1m), \n    interval:1m, by:{c_service, topic},\n    filter:{in(c_service, $Namespace)}\n  | fieldsRename name = topic\n]\n| fields timeframe, interval, value, name, status",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "value"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "value"
                                ],
                                "value": "sparkline",
                                "id": 1745342404390
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 20
                    }
                },
                "visualization": "lineChart",
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "48": {
                "type": "data",
                "title": "Kafka Producer Errors or Dropped",
                "query": "timeseries value = sum(kafka_producer_message_dropped_total, rollup:total, rate: 1m), \n  interval:1m, by:{c_service, name},\n  filter:{in(c_service, $Namespace)}\n| append [\n  timeseries value = sum(kafka_message_error, rate: 1m), \n    interval:1m, by:{c_service, topic},\n    filter:{in(c_service, $Namespace)}\n  | fieldsRename name = topic\n]\n| fields timeframe, interval, value, name",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "value"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "value"
                                ],
                                "value": "sparkline",
                                "id": 1745342404390
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 20
                    }
                },
                "visualization": "recordView",
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "49": {
                "type": "data",
                "title": "Kafka Producer Latency",
                "query": "timeseries value = avg(kafka_producer_broker_int_latency, rate: 1m), \n  interval:1m, by:{c_service, name},\n  filter:{in(c_service, $Namespace) and field == \"avg\"}\n| fields timeframe, interval, value, name",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "value"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "value"
                                ],
                                "value": "sparkline",
                                "id": 1745342404390
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 20
                    },
                    "unitsOverrides": [
                        {
                            "identifier": "value",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1745342757017
                        }
                    ]
                },
                "visualization": "lineChart",
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            }
        },
        "layouts": {
            "0": {
                "x": 8,
                "y": 4,
                "w": 8,
                "h": 3
            },
            "1": {
                "x": 0,
                "y": 4,
                "w": 8,
                "h": 3
            },
            "10": {
                "x": 8,
                "y": 7,
                "w": 8,
                "h": 5
            },
            "11": {
                "x": 16,
                "y": 7,
                "w": 8,
                "h": 5
            },
            "12": {
                "x": 0,
                "y": 7,
                "w": 8,
                "h": 5
            },
            "13": {
                "x": 0,
                "y": 81,
                "w": 24,
                "h": 8
            },
            "14": {
                "x": 0,
                "y": 74,
                "w": 24,
                "h": 7
            },
            "15": {
                "x": 0,
                "y": 61,
                "w": 24,
                "h": 7
            },
            "17": {
                "x": 16,
                "y": 4,
                "w": 8,
                "h": 3
            },
            "18": {
                "x": 4,
                "y": 0,
                "w": 4,
                "h": 4
            },
            "19": {
                "x": 12,
                "y": 0,
                "w": 4,
                "h": 4
            },
            "20": {
                "x": 16,
                "y": 0,
                "w": 4,
                "h": 4
            },
            "22": {
                "x": 0,
                "y": 0,
                "w": 4,
                "h": 4
            },
            "25": {
                "x": 0,
                "y": 12,
                "w": 8,
                "h": 5
            },
            "26": {
                "x": 8,
                "y": 12,
                "w": 8,
                "h": 5
            },
            "27": {
                "x": 0,
                "y": 49,
                "w": 24,
                "h": 6
            },
            "28": {
                "x": 0,
                "y": 55,
                "w": 24,
                "h": 6
            },
            "29": {
                "x": 16,
                "y": 17,
                "w": 8,
                "h": 5
            },
            "30": {
                "x": 20,
                "y": 0,
                "w": 4,
                "h": 4
            },
            "31": {
                "x": 8,
                "y": 0,
                "w": 4,
                "h": 4
            },
            "32": {
                "x": 0,
                "y": 17,
                "w": 16,
                "h": 5
            },
            "33": {
                "x": 16,
                "y": 12,
                "w": 8,
                "h": 5
            },
            "34": {
                "x": 0,
                "y": 89,
                "w": 24,
                "h": 6
            },
            "35": {
                "x": 0,
                "y": 95,
                "w": 24,
                "h": 6
            },
            "39": {
                "x": 16,
                "y": 22,
                "w": 8,
                "h": 6
            },
            "40": {
                "x": 8,
                "y": 22,
                "w": 8,
                "h": 6
            },
            "41": {
                "x": 0,
                "y": 22,
                "w": 8,
                "h": 6
            },
            "42": {
                "x": 0,
                "y": 68,
                "w": 24,
                "h": 6
            },
            "43": {
                "x": 0,
                "y": 28,
                "w": 24,
                "h": 5
            },
            "44": {
                "x": 8,
                "y": 33,
                "w": 8,
                "h": 6
            },
            "45": {
                "x": 16,
                "y": 33,
                "w": 8,
                "h": 6
            },
            "46": {
                "x": 0,
                "y": 33,
                "w": 8,
                "h": 6
            },
            "47": {
                "x": 0,
                "y": 39,
                "w": 24,
                "h": 5
            },
            "48": {
                "x": 0,
                "y": 44,
                "w": 12,
                "h": 5
            },
            "49": {
                "x": 12,
                "y": 44,
                "w": 12,
                "h": 5
            }
        },
        "importedWithCode": false,
        "settings": {}
    }