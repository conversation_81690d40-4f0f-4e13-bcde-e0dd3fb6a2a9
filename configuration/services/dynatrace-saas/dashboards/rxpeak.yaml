---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: plt-marketplace-health-and-peak-rxpeak-dashboard
  labels:
    scope: production
spec:
  name: "[plt-marketplace-health-and-peak] RxPeak Dashboard"
  json: |
    {
      "version": 18,
      "variables": [
        {
          "key": "Namespace",
          "visible": true,
          "type": "query",
          "version": 1,
          "editable": true,
          "input": "fetch dt.entity.kubernetes_cluster\n| filter in(entity.name, \"data-prod-rh-eks\")\n| join [fetch dt.entity.cloud_application_namespace\n| fieldsAdd clusterId=clustered_by[dt.entity.kubernetes_cluster]\n| filter contains(entity.name, \"mp-health-mop-rxpeak\")], on:{left[id]==right[clusterId]}, fields:{nsName=entity.name}\n| fields nsName\n| sort nsName",
          "multiple": false
        }
      ],
      "tiles": {
        "16": {
          "type": "markdown",
          "content": "# MHI Common"
        },
        "38": {
          "type": "markdown",
          "content": "# Job Metrics"
        },
        "42": {
          "title": "Checkpoints Completed",
          "description": "",
          "type": "data",
          "query": "timeseries {\n    completed = avg(flink_jobmanager_job_numberOfCompletedCheckpoints, rate:1m), interval:5m,\n    inProgress = avg(flink_jobmanager_job_numberOfInProgressCheckpoints, rate:1m),\n    failed = avg(flink_jobmanager_job_numberOfFailedCheckpoints, rate:1m)\n}, by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd Completed = (arrayDelta(completed)[])\n| fieldsAdd `In Progress` = (arrayDelta(inProgress)[])\n| fieldsAdd Failed = (arrayDelta(failed)[])\n| fields interval, timeframe, Completed, `In Progress`, Failed\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "auto",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "Completed",
                  "In Progress",
                  "Failed"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              },
              "bandChartSettings": {
                "lower": "Completed",
                "upper": "In Progress"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "In Progress",
                    "Failed"
                  ],
                  "value": "sparkline",
                  "id": 1746123209992
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [],
              "dataMappings": {},
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": []
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "Completed",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736342780869
              },
              {
                "identifier": "In Progress",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736801237253
              },
              {
                "identifier": "Failed",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736801238775
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "46": {
          "type": "markdown",
          "content": "# System Resources"
        },
        "59": {
          "title": "Task Backpressure",
          "description": "Indicates if there is a backpressure on a specific task. 1 indicates backpressure and 0 indicates no backpressure.",
          "type": "data",
          "query": "timeseries cycles = max(flink_taskmanager_job_task_isBackPressured, rate:1m), by: {k8s.namespace.name, task_name}\n| filter k8s.namespace.name == $Namespace\n| fields interval, timeframe, cycles, task_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "cycles"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "cycles"
                  ],
                  "value": "sparkline",
                  "id": 1737379738673
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "task_name"
              ],
              "dataMappings": {
                "value": "task_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": []
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {}
        },
        "65": {
          "title": "Dropped late events",
          "description": "",
          "type": "data",
          "query": "timeseries cycles = sum(flink_taskmanager_job_task_operator_numLateRecordsDropped, rate:1m), by: {k8s.namespace.name, operator_name}, interval:5m\n| filter k8s.namespace.name == $Namespace\n| fields interval, timeframe, cycles, operator_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "cycles"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "cycles"
                  ],
                  "value": "sparkline",
                  "id": 1746379490771
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "66": {
          "title": "Job Manager Memory",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { max(dt.runtime.jvm.memory_pool.max), value.A = avg(dt.runtime.jvm.memory_pool.max, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries { max(dt.runtime.jvm.memory_pool.used), value.B = avg(dt.runtime.jvm.memory_pool.used, scalar: true) },\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)`, value.B }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "bandChartSettings": {
                "lower": "max(dt.runtime.jvm.memory_pool.max)",
                "upper": "max(dt.runtime.jvm.memory_pool.used)"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "max(dt.runtime.jvm.memory_pool.max)",
                  "max(dt.runtime.jvm.memory_pool.used)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "JVM heap memory max bytes"
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "max(dt.runtime.jvm.memory_pool.max)"
                  ],
                  "value": "sparkline",
                  "id": 1746013715838
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.max",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace  dt.entity.host.name != *taskmanager* "
              },
              {
                "id": "B",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.used",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace dt.entity.host.name != *taskmanager*"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-30m",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "67": {
          "title": "Task Manager Memory",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { max(dt.runtime.jvm.memory_pool.max), value.A = avg(dt.runtime.jvm.memory_pool.max, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries { max(dt.runtime.jvm.memory_pool.used), value.B = avg(dt.runtime.jvm.memory_pool.used, scalar: true) },\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)`, value.B }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "bandChartSettings": {
                "lower": "max(dt.runtime.jvm.memory_pool.max)",
                "upper": "max(dt.runtime.jvm.memory_pool.used)"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "max(dt.runtime.jvm.memory_pool.max)",
                  "max(dt.runtime.jvm.memory_pool.used)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "JVM heap memory max bytes"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "max(dt.runtime.jvm.memory_pool.used)"
                  ],
                  "value": "sparkline",
                  "id": 1746122857447
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": []
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.max",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace dt.entity.host.name = *taskmanager* "
              },
              {
                "id": "B",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.used",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace dt.entity.host.name = *taskmanager*"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "76": {
          "title": "Task Manager CPU Usage",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries max(dt.kubernetes.container.requests_cpu), filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.pod.name, \"*taskmanager*\") }\n| join [\n    timeseries max(dt.kubernetes.container.cpu_usage),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.pod.name, \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.kubernetes.container.cpu_usage)` }\n| fieldsAdd C = (`max(dt.kubernetes.container.cpu_usage)`[]/`max(dt.kubernetes.container.requests_cpu)`[])*100\n| fieldsRemove `max(dt.kubernetes.container.requests_cpu)`\n| fieldsRemove `max(dt.kubernetes.container.cpu_usage)`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "C"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Max CPU Usage",
                "max": 100
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "C"
                  ],
                  "value": "sparkline",
                  "id": 1746608379890
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "C",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746607779471
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.requests_cpu",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace k8s.pod.name = *taskmanager*"
              },
              {
                "id": "B",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.cpu_usage",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace k8s.pod.name = *taskmanager*"
              },
              {
                "id": "C",
                "isEnabled": true,
                "datatype": "expression",
                "expression": "(B/A)*100"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "77": {
          "title": "Jobs Running",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { min(flink_jobmanager_numRunningJobs), value.A = avg(flink_jobmanager_numRunningJobs, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "min(flink_jobmanager_numRunningJobs)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": ""
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "min(flink_jobmanager_numRunningJobs)"
                  ],
                  "value": "sparkline",
                  "id": 1746610876287
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_numRunningJobs",
                  "aggregation": "min"
                },
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "78": {
          "title": "Restarts Over Time",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { max(flink_jobmanager_job_fullRestarts), value.A = avg(flink_jobmanager_job_fullRestarts, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "max(flink_jobmanager_job_fullRestarts)"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "max(flink_jobmanager_job_fullRestarts)"
                  ],
                  "value": "sparkline",
                  "id": 1746057407897
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_job_fullRestarts",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "79": {
          "title": "",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries min(flink_jobmanager_job_fullRestarts), filter: { matchesValue(k8s.namespace.name, $Namespace) }\n| fieldsAdd value.A = arrayLast(`min(flink_jobmanager_job_fullRestarts)`)",
          "visualization": "singleValue",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "min(flink_jobmanager_job_fullRestarts)"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "Restarts",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "value.A",
              "autoscale": true,
              "sparklineSettings": {
                "record": "avg(flink_jobmanager_job_fullRestarts)"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "value.A"
            },
            "label": {
              "showLabel": true,
              "label": "value.A"
            },
            "unitsOverrides": [
              {
                "identifier": "value.A",
                "unitCategory": "amount",
                "baseUnit": "one",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746057715675
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_job_fullRestarts",
                  "aggregation": "min"
                },
                "convertToValue": "Last",
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "80": {
          "title": "",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { min(flink_jobmanager_numRunningJobs), value.A = avg(flink_jobmanager_numRunningJobs, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
          "visualization": "singleValue",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "min(flink_jobmanager_numRunningJobs)"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "Jobs Running",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "value.A",
              "autoscale": true,
              "sparklineSettings": {
                "record": "max(flink_jobmanager_numRunningJobs)"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "min(flink_jobmanager_numRunningJobs)"
                  ],
                  "value": "sparkline",
                  "id": 1746615506931
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "value.A",
                "unitCategory": "amount",
                "baseUnit": "one",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746058049868
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_numRunningJobs",
                  "aggregation": "min"
                },
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "92": {
          "title": "",
          "type": "data",
          "query": "timeseries max(flink_jobmanager_job_uptime), filter: { matchesValue(k8s.namespace.name, $Namespace) }\n| fieldsAdd B = `max(flink_jobmanager_job_uptime)`[]/3600000\n| fieldsAdd value.B = arrayLast(`B`)\n| fieldsRemove `max(flink_jobmanager_job_uptime)`",
          "visualization": "singleValue",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "B"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "value.B",
              "autoscale": true,
              "sparklineSettings": {
                "record": "B"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": false,
                "label": "",
                "upward": {
                  "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                },
                "downward": {
                  "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                },
                "neutral": {
                  "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                },
                "isRelative": true,
                "isLabelVisible": false
              },
              "colorThresholdTarget": "value",
              "label": "Uptime Since Last Deployment"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "B"
                  ],
                  "value": "sparkline",
                  "id": 1746186241300
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "value.B"
            },
            "label": {
              "showLabel": true,
              "label": "Uptime Since Last Deployment"
            },
            "unitsOverrides": [
              {
                "identifier": "value.B",
                "unitCategory": "time",
                "baseUnit": "hour",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1746199873364
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "93": {
          "title": "Job Manager CPU Usage",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries max(dt.kubernetes.container.requests_cpu), filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(k8s.pod.name, \"*taskmanager*\") }\n| join [\n    timeseries max(dt.kubernetes.container.cpu_usage),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(k8s.pod.name, \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.kubernetes.container.cpu_usage)` }\n| fieldsAdd C = (`max(dt.kubernetes.container.cpu_usage)`[]/`max(dt.kubernetes.container.requests_cpu)`[])*100\n| fieldsRemove `max(dt.kubernetes.container.requests_cpu)`\n| fieldsRemove `max(dt.kubernetes.container.cpu_usage)`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "C"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Max CPU Usage",
                "min": "auto",
                "max": 100
              },
              "seriesOverrides": []
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "C"
                  ],
                  "value": "sparkline",
                  "id": 1746608379842
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "C",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746607563134
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.requests_cpu",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace  k8s.pod.name != *taskmanager*"
              },
              {
                "id": "B",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.cpu_usage",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace k8s.pod.name != *taskmanager*"
              },
              {
                "id": "C",
                "isEnabled": true,
                "datatype": "expression",
                "expression": "(B/A)*100"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "102": {
          "title": "Checkpoint Size",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { avg(flink_jobmanager_job_lastCheckpointSize), value.A = avg(flink_jobmanager_job_lastCheckpointSize, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "avg(flink_jobmanager_job_lastCheckpointSize)"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "avg(flink_jobmanager_job_lastCheckpointSize)"
                  ],
                  "value": "sparkline",
                  "id": 1746603800529
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "avg(flink_jobmanager_job_lastCheckpointSize)",
                "unitCategory": "data",
                "baseUnit": "byte",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746603933960
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_job_lastCheckpointSize",
                  "aggregation": "avg"
                },
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "103": {
          "title": "Flink Heap Usage",
          "type": "data",
          "query": "timeseries `max(dt.runtime.jvm.memory_pool.max)·A` = max(dt.runtime.jvm.memory_pool.max), filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.used)·B` = max(dt.runtime.jvm.memory_pool.used),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)·B` }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.max)·D` = max(dt.runtime.jvm.memory_pool.max),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.max)·D` }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.used)·E` = max(dt.runtime.jvm.memory_pool.used),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)·E` }\n| fieldsAdd `Job Manager` = (`max(dt.runtime.jvm.memory_pool.used)·B`[]/`max(dt.runtime.jvm.memory_pool.max)·A`[])*100\n| fieldsAdd `Task Manager` = (`max(dt.runtime.jvm.memory_pool.used)·E`[]/`max(dt.runtime.jvm.memory_pool.max)·D`[])*100\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.max)·A`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.used)·B`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.max)·D`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.used)·E`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "bandChartSettings": {
                "lower": "Job Manager",
                "upper": "Task Manager"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "Job Manager",
                  "Task Manager"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Heap Usage",
                "max": 100
              },
              "rightYAxisSettings": {
                "isLabelVisible": false
              },
              "tooltip": {
                "seriesDisplayMode": "single-line"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "Job Manager",
                    "Task Manager"
                  ],
                  "value": "sparkline",
                  "id": 1746605767648
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "Job Manager",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1746015971389
              },
              {
                "identifier": "Task Manager",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746016013552
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-30m",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "104": {
          "type": "markdown",
          "content": "# Rxpeak"
        },
        "105": {
          "title": "PeakV2 - Supply-Demand list events per cycle",
          "type": "data",
          "query": "timeseries cycles = sum(flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle), by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase"
                  ],
                  "value": "sparkline",
                  "id": 1747293317468
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "106": {
          "title": "PeakV2 - Supply-Demand list events per cycle (cct)",
          "type": "data",
          "query": "timeseries cycles = sum (flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle), by: {k8s.namespace.name, cct}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fieldsAdd non_null = arrayRemoveNulls(increase)\n| fields interval, timeframe, cct, non_null",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "non_null"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "non_null"
                  ],
                  "value": "sparkline",
                  "id": 1747296492430
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "107": {
          "title": "PeakV2 - Supply-Demand list events per cycle (service_area_id)",
          "type": "data",
          "query": "timeseries cycles = sum (flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle), by: {k8s.namespace.name, service_area_id}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fieldsAdd non_null = arrayRemoveNulls(increase)\n| fields interval, timeframe, service_area_id, non_null",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "non_null"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "non_null"
                  ],
                  "value": "sparkline",
                  "id": 1747296492430
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "108": {
          "title": "Total Supply-Demand list events per cycle (Enriched w/Cell ID)",
          "type": "data",
          "query": "timeseries cycles = sum (flink_taskmanager_job_task_operator_mp_health_peak_v2_captain_customer_enrich_success), by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "109": {
          "title": "PeakV2 DS Model Stream Metrics (Failures, Successes, Cell Peak Factors Count)",
          "type": "data",
          "query": "timeseries {\n  cycles = sum (flink_taskmanager_job_task_operator_mp_health_peak_v2_captain_customer_enrich_success),\n  cycles1 = sum (flink_taskmanager_job_task_operator_mp_health_peak_v2_cell_peak_factor_items_per_cycle_counter)\n}, by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd success = arrayDelta(cycles)\n| fieldsAdd generated_cell_peak_factor_per_cycle = arrayDelta(cycles1)\n| fields interval, timeframe, success, generated_cell_peak_factor_per_cycle",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "success",
                  "generated_cell_peak_factor_per_cycle"
                ]
              },
              "leftYAxisSettings": {},
              "bandChartSettings": {
                "lower": "success",
                "upper": "generated_cell_peak_factor_per_cycle"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "success",
                    "generated_cell_peak_factor_per_cycle"
                  ],
                  "value": "sparkline",
                  "id": 1747297873172
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "110": {
          "title": "PeakV2 DS Model Stream Latency",
          "type": "data",
          "query": "timeseries latency=avg(flink_taskmanager_job_task_operator_mp_health_peak_v2_ds_model_stream_latency_histogram_latency),\nfilter: {k8s.namespace.name == $Namespace},\nby: {percentile}\n| fields timeframe,interval,\"p\",percentile,latency[]/1000",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "latency[] / 1000"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "latency[] / 1000"
                  ],
                  "value": "sparkline",
                  "id": *********2523
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "latency[] / 1000",
                "unitCategory": "time",
                "baseUnit": "second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1747298100398
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "111": {
          "title": "PeakV2 DS Model Stream - Average Demand And Supply Items Count Per Container",
          "type": "data",
          "query": "timeseries latency=max(flink_taskmanager_job_task_operator_mp_health_peak_v2_ds_model_stream_demand_and_supply_items_histogram_latency),\nfilter: {k8s.namespace.name == $Namespace},\nby: {tm_id}\n| fields timeframe,interval,\"p\",tm_id,latency",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "latency"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "latency"
                  ],
                  "value": "sparkline",
                  "id": 1747298764893
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "latency[] / 1000",
                "unitCategory": "time",
                "baseUnit": "second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1747298100398
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "112": {
          "title": "Dropped Late events",
          "type": "data",
          "query": "timeseries {\n  cycles = sum (flink_taskmanager_job_task_operator_mp_health_dropped_captain_late_events)\n}, by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "113": {
          "title": "Captain And Customer Latest Event Time Difference",
          "type": "data",
          "query": "timeseries {\n  cycles = sum (flink_taskmanager_job_task_operator_mp_health_dpv2_captain_and_customer_latest_event_time_difference)\n}, by: {k8s.namespace.name, k8s.pod.name}\n| filter k8s.namespace.name == $Namespace\n| fields interval, timeframe, cycles, k8s.pod.name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "cycles"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "cycles"
                  ],
                  "value": "sparkline",
                  "id": 1747299196676
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "114": {
          "title": "Captain and Customer Per Cycle Data Freshness Distribution",
          "type": "data",
          "query": "timeseries latency=min(flink_taskmanager_job_task_operator_mp_health_dpv2_captain_and_customer_per_cycle_stream_freshness_histogram_latency),\nfilter: {k8s.namespace.name == $Namespace},\nby: {percentile}\n| fields timeframe,interval,\"p\",percentile,latency[]/1000",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "latency[] / 1000"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "latency[] / 1000"
                  ],
                  "value": "sparkline",
                  "id": *********2523
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "latency[] / 1000",
                "unitCategory": "time",
                "baseUnit": "second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1747298100398
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "116": {
          "title": "Data Freshness Distribution Count in Seconds",
          "type": "data",
          "query": "timeseries {\n  cycles = max (flink_taskmanager_job_task_operator_mp_health_dpv2_captain_and_customer_per_cycle_stream_freshness_histogram_latency)\n}, by: {k8s.namespace.name, k8s.pod.name}\n| filter k8s.namespace.name == $Namespace\n| fields interval, timeframe, cycles[]/1000, k8s.pod.name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "cycles[] / 1000"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "117": {
          "title": "PeakV2 - Dropped invalid events during warm-up (after savepoint)",
          "type": "data",
          "query": "timeseries {\n  cycles = sum (flink_taskmanager_job_task_operator_mp_health_peak_v2_warmup_filtered_events_before_ds_model_runner)\n}, by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase"
                  ],
                  "value": "sparkline",
                  "id": 1747300885681
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "118": {
          "title": "Dropped captain pings",
          "type": "data",
          "query": "timeseries \n  cycles = sum (flink_taskmanager_job_task_operator_reason_filtered_service_area_dropped_captain_ping_counter),\n  by: {k8s.namespace.name, reason}\n| filter k8s.namespace.name == $Namespace\n| append [ \n  timeseries \n    cycles1 = sum (flink_taskmanager_job_task_operator_reason_filtered_service_area_dropped_captain_ping_counter),\n    by: {k8s.namespace.name}\n    | filter k8s.namespace.name == $Namespace\n]\n| fieldsAdd increase = arrayDelta(cycles)\n| fieldsAdd increase1 = arrayDelta(cycles1)\n| fields interval, timeframe, increase, increase1, reason",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase",
                  "increase1"
                ]
              },
              "leftYAxisSettings": {},
              "bandChartSettings": {
                "lower": "increase",
                "upper": "increase1"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase1"
                  ],
                  "value": "sparkline",
                  "id": 1747302374299
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "increase",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1747376580077
              },
              {
                "identifier": "increase1",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1747376596310
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "119": {
          "title": "Dropped app events",
          "type": "data",
          "query": "timeseries \n  cycles = sum (flink_taskmanager_job_task_operator_reason_filtered_service_area_dropped_app_events_counter),\n  by: {k8s.namespace.name, reason}\n| filter k8s.namespace.name == $Namespace\n| append [ \n  timeseries \n    cycles1 = sum (flink_taskmanager_job_task_operator_reason_filtered_service_area_dropped_app_events_counter),\n    by: {k8s.namespace.name}\n    | filter k8s.namespace.name == $Namespace\n]\n| fieldsAdd increase = arrayDelta(cycles)\n| fieldsAdd increase1 = arrayDelta(cycles1)\n| fields interval, timeframe, increase, increase1, reason",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase",
                  "increase1"
                ]
              },
              "leftYAxisSettings": {},
              "bandChartSettings": {
                "lower": "increase",
                "upper": "increase1"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase1"
                  ],
                  "value": "sparkline",
                  "id": 1747302780789
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "increase",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1747376523586
              },
              {
                "identifier": "increase1",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1747376545686
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "120": {
          "title": "Dropped booking events",
          "type": "data",
          "query": "timeseries \n  cycles = sum (flink_taskmanager_job_task_operator_reason_service_area_id_filtered_service_area_dropped_bookings_counter),\n  by: {k8s.namespace.name, reason, service_area_id}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase, reason",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase"
                  ],
                  "value": "sparkline",
                  "id": 1747303114062
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "increase",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1747376471247
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "121": {
          "title": "PeakV2 - Unique events per cycle",
          "type": "data",
          "query": "timeseries \n  cycles = sum (flink_taskmanager_job_task_operator_cct_service_area_id_user_type_mp_health_dpv2_unique_captain_customer_events_per_cycle),\n  by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase"
                  ],
                  "value": "sparkline",
                  "id": 1747647580927
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "122": {
          "title": "Supply over time",
          "type": "data",
          "query": "timeseries \n  cycles = sum (flink_taskmanager_job_task_operator_cct_service_area_id_user_type_mp_health_dpv2_unique_captain_customer_events_per_cycle), \n  interval:5m,\n  by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fieldsAdd rate_per_hour = iCollectArray(increase[]/(60*60))\n| fields interval, timeframe, rate_per_hour",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "cycles"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "rate_per_hour"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "rate_per_hour"
                  ],
                  "value": "sparkline",
                  "id": 1747378355595
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "interval"
            },
            "label": {
              "showLabel": true,
              "label": "interval"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "123": {
          "title": "Data Freshness",
          "description": "",
          "type": "data",
          "query": "timeseries max(flink_taskmanager_job_task_operator_currentOutputWatermark), by: { operator_name }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(operator_name, \"*KAFKA_*\") OR matchesValue(operator_name, \"Sink*\") AND NOT matchesValue(operator_name, \"Source_*\") AND NOT matchesValue(operator_name, \"*_Committer*\") }\n| join [\n    timeseries min(flink_taskmanager_job_task_operator_currentOutputWatermark),\n    by: { operator_name },\n    filter: {\n        matchesValue(k8s.namespace.name, $Namespace) AND\n        (matchesValue(operator_name, \"*KAFKA_*\") OR matchesValue(operator_name, \"Sink*\")) AND\n        NOT matchesValue(operator_name, \"Source*\") AND\n        NOT matchesValue(operator_name, \"*_Committer*\")\n    }\n  ], on: { operator_name }, fields: { `min(flink_taskmanager_job_task_operator_currentOutputWatermark)` }\n| fieldsAdd C = (`max(flink_taskmanager_job_task_operator_currentOutputWatermark)`[]-`min(flink_taskmanager_job_task_operator_currentOutputWatermark)`[]) / 1000\n| fieldsRemove `max(flink_taskmanager_job_task_operator_currentOutputWatermark)`\n| fieldsRemove `min(flink_taskmanager_job_task_operator_currentOutputWatermark)`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "C"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "C"
                  ],
                  "value": "sparkline",
                  "id": 1747986470392
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "C",
                "unitCategory": "time",
                "baseUnit": "second",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "124": {
          "title": "Poison Letter",
          "description": "",
          "type": "data",
          "query": "timeseries value = sum(flink_taskmanager_job_task_operator_stream_function_mp_health_poison_letter_counter),\nby: {stream,function},\nfilter:k8s.namespace.name==$Namespace\n| fieldsAdd increase = arrayDelta(value)\n| fields interval, timeframe, stream,function,increase",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "cycles"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "cycles"
                  ],
                  "value": "sparkline",
                  "id": 1746379490771
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "125": {
          "title": "Number of Records Out From Sinks",
          "description": "",
          "type": "data",
          "query": "timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsInPerSecond\n,rate:60s), \nby: {operator_name}, \n filter: {k8s.namespace.name == $Namespace } \n| filter endsWith(operator_name,\"_Writer\")\n| filter startsWith(operator_name,\"KAFKA\")\n| fieldsAdd per_second = ri[]/60\n| fields timeframe,interval,per_second,operator_name\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "per_second"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "per_second"
                  ],
                  "value": "sparkline",
                  "id": 1748203262280
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "per_second",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "126": {
          "title": "Records In to operators",
          "description": "",
          "type": "data",
          "query": "timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsInPerSecond,rate:60s), \nby: {operator_name}, filter: {k8s.namespace.name == $Namespace }\n| fieldsAdd ri = ri[]/60\n| fields timeframe,interval,ri,operator_name\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "ri"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "ri",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "127": {
          "title": "Records out from operators",
          "description": "",
          "type": "data",
          "query": "timeseries ro = sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond), \nby: {operator_name}, filter: {k8s.namespace.name == $Namespace }\n| fieldsAdd ro = ro[]/60\n| fields timeframe,interval,ro,operator_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "ro"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "ro",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "129": {
          "title": "Number of Records Out From Sources",
          "description": "",
          "type": "data",
          "query": "timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond), \nby: {operator_name}, \n filter: {k8s.namespace.name == $Namespace } \n| filter startsWith(operator_name,\"Source:_\")\n| fields timeframe,interval,ri,operator_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "ri"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "ri",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        }
      },
      "layouts": {
        "16": {
          "x": 0,
          "y": 47,
          "w": 20,
          "h": 1
        },
        "38": {
          "x": 0,
          "y": 26,
          "w": 24,
          "h": 1
        },
        "42": {
          "x": 0,
          "y": 33,
          "w": 12,
          "h": 7
        },
        "46": {
          "x": 0,
          "y": 0,
          "w": 24,
          "h": 1
        },
        "59": {
          "x": 0,
          "y": 40,
          "w": 12,
          "h": 7
        },
        "65": {
          "x": 0,
          "y": 71,
          "w": 12,
          "h": 8
        },
        "66": {
          "x": 0,
          "y": 19,
          "w": 24,
          "h": 7
        },
        "67": {
          "x": 12,
          "y": 10,
          "w": 12,
          "h": 9
        },
        "76": {
          "x": 0,
          "y": 10,
          "w": 12,
          "h": 9
        },
        "77": {
          "x": 0,
          "y": 27,
          "w": 12,
          "h": 6
        },
        "78": {
          "x": 12,
          "y": 33,
          "w": 12,
          "h": 7
        },
        "79": {
          "x": 18,
          "y": 30,
          "w": 6,
          "h": 3
        },
        "80": {
          "x": 18,
          "y": 27,
          "w": 6,
          "h": 3
        },
        "92": {
          "x": 12,
          "y": 27,
          "w": 6,
          "h": 6
        },
        "93": {
          "x": 0,
          "y": 1,
          "w": 12,
          "h": 9
        },
        "102": {
          "x": 12,
          "y": 40,
          "w": 12,
          "h": 7
        },
        "103": {
          "x": 12,
          "y": 1,
          "w": 12,
          "h": 9
        },
        "104": {
          "x": 0,
          "y": 79,
          "w": 24,
          "h": 1
        },
        "105": {
          "x": 12,
          "y": 80,
          "w": 12,
          "h": 10
        },
        "106": {
          "x": 0,
          "y": 99,
          "w": 24,
          "h": 10
        },
        "107": {
          "x": 0,
          "y": 109,
          "w": 24,
          "h": 8
        },
        "108": {
          "x": 0,
          "y": 117,
          "w": 12,
          "h": 9
        },
        "109": {
          "x": 12,
          "y": 117,
          "w": 12,
          "h": 9
        },
        "110": {
          "x": 0,
          "y": 126,
          "w": 12,
          "h": 10
        },
        "111": {
          "x": 12,
          "y": 126,
          "w": 12,
          "h": 10
        },
        "112": {
          "x": 0,
          "y": 136,
          "w": 12,
          "h": 9
        },
        "113": {
          "x": 12,
          "y": 136,
          "w": 12,
          "h": 9
        },
        "114": {
          "x": 0,
          "y": 145,
          "w": 12,
          "h": 9
        },
        "116": {
          "x": 12,
          "y": 145,
          "w": 12,
          "h": 9
        },
        "117": {
          "x": 0,
          "y": 154,
          "w": 12,
          "h": 9
        },
        "118": {
          "x": 12,
          "y": 154,
          "w": 12,
          "h": 9
        },
        "119": {
          "x": 0,
          "y": 163,
          "w": 12,
          "h": 9
        },
        "120": {
          "x": 12,
          "y": 163,
          "w": 12,
          "h": 9
        },
        "121": {
          "x": 0,
          "y": 80,
          "w": 12,
          "h": 10
        },
        "122": {
          "x": 0,
          "y": 90,
          "w": 24,
          "h": 9
        },
        "123": {
          "x": 0,
          "y": 48,
          "w": 12,
          "h": 8
        },
        "124": {
          "x": 12,
          "y": 48,
          "w": 12,
          "h": 8
        },
        "125": {
          "x": 12,
          "y": 56,
          "w": 12,
          "h": 8
        },
        "126": {
          "x": 0,
          "y": 64,
          "w": 12,
          "h": 7
        },
        "127": {
          "x": 12,
          "y": 64,
          "w": 12,
          "h": 7
        },
        "129": {
          "x": 0,
          "y": 56,
          "w": 12,
          "h": 8
        }
      },
      "importedWithCode": false,
      "settings": {
        "gridLayout": {
          "mode": "responsive"
        }
      }
    }