---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: plt-marketplace-health-and-peak-shift-adh-dashboard
  labels:
    scope: production
spec:
  name: "[plt-marketplace-health-and-peak] Shift ADH Dashboard"
  json: |
    {
      "version": 18,
      "variables": [
        {
          "key": "Namespace",
          "visible": true,
          "type": "query",
          "version": 1,
          "editable": true,
          "input": "fetch dt.entity.kubernetes_cluster\n| filter in(entity.name, \"data-prod-rh-eks\")\n| join [fetch dt.entity.cloud_application_namespace\n| fieldsAdd clusterId=clustered_by[dt.entity.kubernetes_cluster]\n| filter contains(entity.name, \"mp-health-shift-adh\")], on:{left[id]==right[clusterId]}, fields:{nsName=entity.name}\n| fields nsName\n| sort nsName",
          "multiple": false
        }
      ],
      "tiles": {
        "16": {
          "type": "markdown",
          "content": "# MHI Common"
        },
        "38": {
          "type": "markdown",
          "content": "# Job Metrics"
        },
        "42": {
          "title": "Checkpoints Completed",
          "description": "",
          "type": "data",
          "query": "timeseries {\n    completed = avg(flink_jobmanager_job_numberOfCompletedCheckpoints, rate:1m), interval:5m,\n    inProgress = avg(flink_jobmanager_job_numberOfInProgressCheckpoints, rate:1m),\n    failed = avg(flink_jobmanager_job_numberOfFailedCheckpoints, rate:1m)\n}, by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd Completed = (arrayDelta(completed)[])\n| fieldsAdd `In Progress` = (arrayDelta(inProgress)[])\n| fieldsAdd Failed = (arrayDelta(failed)[])\n| fields interval, timeframe, Completed, `In Progress`, Failed\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "auto",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "Completed",
                  "In Progress",
                  "Failed"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              },
              "bandChartSettings": {
                "lower": "Completed",
                "upper": "In Progress"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "In Progress",
                    "Failed"
                  ],
                  "value": "sparkline",
                  "id": 1746123209992
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [],
              "dataMappings": {},
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": []
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "Completed",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736342780869
              },
              {
                "identifier": "In Progress",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736801237253
              },
              {
                "identifier": "Failed",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736801238775
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "46": {
          "type": "markdown",
          "content": "# System Resources"
        },
        "59": {
          "title": "Task Backpressure",
          "description": "Indicates if there is a backpressure on a specific task. 1 indicates backpressure and 0 indicates no backpressure.",
          "type": "data",
          "query": "timeseries cycles = max(flink_taskmanager_job_task_isBackPressured, rate:1m), by: {k8s.namespace.name, task_name}\n| filter k8s.namespace.name == $Namespace\n| fields interval, timeframe, cycles, task_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "cycles"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "cycles"
                  ],
                  "value": "sparkline",
                  "id": 1737379738673
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "task_name"
              ],
              "dataMappings": {
                "value": "task_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": []
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {}
        },
        "65": {
          "title": "Dropped late events",
          "description": "",
          "type": "data",
          "query": "timeseries cycles = sum(flink_taskmanager_job_task_operator_numLateRecordsDropped, rate:1m), by: {k8s.namespace.name, operator_name}, interval:5m\n| filter k8s.namespace.name == $Namespace\n| fields interval, timeframe, cycles, operator_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "cycles"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "cycles"
                  ],
                  "value": "sparkline",
                  "id": 1746379490771
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "66": {
          "title": "Job Manager Memory",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { max(dt.runtime.jvm.memory_pool.max), value.A = avg(dt.runtime.jvm.memory_pool.max, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries { max(dt.runtime.jvm.memory_pool.used), value.B = avg(dt.runtime.jvm.memory_pool.used, scalar: true) },\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)`, value.B }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "bandChartSettings": {
                "lower": "max(dt.runtime.jvm.memory_pool.max)",
                "upper": "max(dt.runtime.jvm.memory_pool.used)"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "max(dt.runtime.jvm.memory_pool.max)",
                  "max(dt.runtime.jvm.memory_pool.used)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "JVM heap memory max bytes"
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "max(dt.runtime.jvm.memory_pool.max)"
                  ],
                  "value": "sparkline",
                  "id": 1746013715838
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.max",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace  dt.entity.host.name != *taskmanager* "
              },
              {
                "id": "B",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.used",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace dt.entity.host.name != *taskmanager*"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-30m",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "67": {
          "title": "Task Manager Memory",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { max(dt.runtime.jvm.memory_pool.max), value.A = avg(dt.runtime.jvm.memory_pool.max, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries { max(dt.runtime.jvm.memory_pool.used), value.B = avg(dt.runtime.jvm.memory_pool.used, scalar: true) },\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)`, value.B }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "bandChartSettings": {
                "lower": "max(dt.runtime.jvm.memory_pool.max)",
                "upper": "max(dt.runtime.jvm.memory_pool.used)"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "max(dt.runtime.jvm.memory_pool.max)",
                  "max(dt.runtime.jvm.memory_pool.used)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "JVM heap memory max bytes"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "max(dt.runtime.jvm.memory_pool.used)"
                  ],
                  "value": "sparkline",
                  "id": 1746122857447
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": []
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.max",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace dt.entity.host.name = *taskmanager* "
              },
              {
                "id": "B",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.runtime.jvm.memory_pool.used",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace dt.entity.host.name = *taskmanager*"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "76": {
          "title": "Task Manager CPU Usage",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries max(dt.kubernetes.container.requests_cpu), filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.pod.name, \"*taskmanager*\") }\n| join [\n    timeseries max(dt.kubernetes.container.cpu_usage),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.pod.name, \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.kubernetes.container.cpu_usage)` }\n| fieldsAdd C = (`max(dt.kubernetes.container.cpu_usage)`[]/`max(dt.kubernetes.container.requests_cpu)`[])*100\n| fieldsRemove `max(dt.kubernetes.container.requests_cpu)`\n| fieldsRemove `max(dt.kubernetes.container.cpu_usage)`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "C"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Max CPU Usage",
                "max": 100
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "C"
                  ],
                  "value": "sparkline",
                  "id": 1746608379890
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "C",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746607779471
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.requests_cpu",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace k8s.pod.name = *taskmanager*"
              },
              {
                "id": "B",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.cpu_usage",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace k8s.pod.name = *taskmanager*"
              },
              {
                "id": "C",
                "isEnabled": true,
                "datatype": "expression",
                "expression": "(B/A)*100"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "77": {
          "title": "Jobs Running",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { min(flink_jobmanager_numRunningJobs), value.A = avg(flink_jobmanager_numRunningJobs, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "min(flink_jobmanager_numRunningJobs)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": ""
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "min(flink_jobmanager_numRunningJobs)"
                  ],
                  "value": "sparkline",
                  "id": 1746610876287
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_numRunningJobs",
                  "aggregation": "min"
                },
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "78": {
          "title": "Restarts Over Time",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { max(flink_jobmanager_job_fullRestarts), value.A = avg(flink_jobmanager_job_fullRestarts, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "max(flink_jobmanager_job_fullRestarts)"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "max(flink_jobmanager_job_fullRestarts)"
                  ],
                  "value": "sparkline",
                  "id": 1747120179685
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_job_fullRestarts",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "79": {
          "title": "",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries min(flink_jobmanager_job_fullRestarts), filter: { matchesValue(k8s.namespace.name, $Namespace) }\n| fieldsAdd value.A = arrayLast(`min(flink_jobmanager_job_fullRestarts)`)",
          "visualization": "singleValue",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "min(flink_jobmanager_job_fullRestarts)"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "Restarts",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "value.A",
              "autoscale": true,
              "sparklineSettings": {
                "record": "avg(flink_jobmanager_job_fullRestarts)"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "value.A"
            },
            "label": {
              "showLabel": true,
              "label": "value.A"
            },
            "unitsOverrides": [
              {
                "identifier": "value.A",
                "unitCategory": "amount",
                "baseUnit": "one",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746057715675
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_job_fullRestarts",
                  "aggregation": "min"
                },
                "convertToValue": "Last",
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-30m",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "80": {
          "title": "",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries min(flink_jobmanager_numRunningJobs), filter: { matchesValue(k8s.namespace.name, $Namespace) }\n| fieldsAdd value.A = arrayLast(`min(flink_jobmanager_numRunningJobs)`)",
          "visualization": "singleValue",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "min(flink_jobmanager_numRunningJobs)"
                ]
              },
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "Jobs Running",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "value.A",
              "autoscale": true,
              "sparklineSettings": {
                "record": "max(flink_jobmanager_numRunningJobs)"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "min(flink_jobmanager_numRunningJobs)"
                  ],
                  "value": "sparkline",
                  "id": 1746615506931
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "value.A",
                "unitCategory": "amount",
                "baseUnit": "one",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746058049868
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_numRunningJobs",
                  "aggregation": "min"
                },
                "convertToValue": "Last",
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "92": {
          "title": "",
          "type": "data",
          "query": "timeseries max(flink_jobmanager_job_uptime), filter: { matchesValue(k8s.namespace.name, $Namespace) }\n| fieldsAdd B = `max(flink_jobmanager_job_uptime)`[]/3600000\n| fieldsAdd value.B = arrayLast(`B`)\n| fieldsRemove `max(flink_jobmanager_job_uptime)`",
          "visualization": "singleValue",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "B"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {}
            },
            "singleValue": {
              "showLabel": true,
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "value.B",
              "autoscale": true,
              "sparklineSettings": {
                "record": "B"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": false,
                "label": "",
                "upward": {
                  "Default": "var(--dt-colors-charts-diverging-red-green-color-10-default, #2a7453)"
                },
                "downward": {
                  "Default": "var(--dt-colors-charts-diverging-red-blue-color-02-default, #ae132d)"
                },
                "neutral": {
                  "Default": "var(--dt-colors-charts-diverging-red-blue-color-10-default, #134fc9)"
                },
                "isRelative": true,
                "isLabelVisible": false
              },
              "colorThresholdTarget": "value",
              "label": "Uptime Since Last Deployment"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "B"
                  ],
                  "value": "sparkline",
                  "id": 1746186241300
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "value.B"
            },
            "label": {
              "showLabel": true,
              "label": "Uptime Since Last Deployment"
            },
            "unitsOverrides": [
              {
                "identifier": "value.B",
                "unitCategory": "time",
                "baseUnit": "hour",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1746199873364
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "93": {
          "title": "Job Manager CPU Usage",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries max(dt.kubernetes.container.requests_cpu), filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(k8s.pod.name, \"*taskmanager*\") }\n| join [\n    timeseries max(dt.kubernetes.container.cpu_usage),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(k8s.pod.name, \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.kubernetes.container.cpu_usage)` }\n| fieldsAdd C = (`max(dt.kubernetes.container.cpu_usage)`[]/`max(dt.kubernetes.container.requests_cpu)`[])*100\n| fieldsRemove `max(dt.kubernetes.container.requests_cpu)`\n| fieldsRemove `max(dt.kubernetes.container.cpu_usage)`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "C"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Max CPU Usage",
                "min": "auto",
                "max": 100
              },
              "seriesOverrides": []
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "C"
                  ],
                  "value": "sparkline",
                  "id": 1746608379842
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "C",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746607563134
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.requests_cpu",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace  k8s.pod.name != *taskmanager*"
              },
              {
                "id": "B",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "dt.kubernetes.container.cpu_usage",
                  "aggregation": "max"
                },
                "filter": "k8s.namespace.name = $Namespace k8s.pod.name != *taskmanager*"
              },
              {
                "id": "C",
                "isEnabled": true,
                "datatype": "expression",
                "expression": "(B/A)*100"
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "102": {
          "title": "Checkpoint Size",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { avg(flink_jobmanager_job_lastCheckpointSize), value.A = avg(flink_jobmanager_job_lastCheckpointSize, scalar: true) }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "avg(flink_jobmanager_job_lastCheckpointSize)"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "avg(flink_jobmanager_job_lastCheckpointSize)"
                  ],
                  "value": "sparkline",
                  "id": 1746603800529
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "avg(flink_jobmanager_job_lastCheckpointSize)",
                "unitCategory": "data",
                "baseUnit": "byte",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746603933960
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_jobmanager_job_lastCheckpointSize",
                  "aggregation": "avg"
                },
                "filter": "k8s.namespace.name = $Namespace "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "103": {
          "title": "Flink Heap Usage",
          "type": "data",
          "query": "timeseries `max(dt.runtime.jvm.memory_pool.max)·A` = max(dt.runtime.jvm.memory_pool.max), filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.used)·B` = max(dt.runtime.jvm.memory_pool.used),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND NOT matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)·B` }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.max)·D` = max(dt.runtime.jvm.memory_pool.max),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.max)·D` }\n| join [\n    timeseries `max(dt.runtime.jvm.memory_pool.used)·E` = max(dt.runtime.jvm.memory_pool.used),\n    filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(entityAttr(dt.entity.host, \"entity.name\"), \"*taskmanager*\") }\n  ], on: { interval }, fields: { `max(dt.runtime.jvm.memory_pool.used)·E` }\n| fieldsAdd `Job Manager` = (`max(dt.runtime.jvm.memory_pool.used)·B`[]/`max(dt.runtime.jvm.memory_pool.max)·A`[])*100\n| fieldsAdd `Task Manager` = (`max(dt.runtime.jvm.memory_pool.used)·E`[]/`max(dt.runtime.jvm.memory_pool.max)·D`[])*100\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.max)·A`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.used)·B`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.max)·D`\n| fieldsRemove `max(dt.runtime.jvm.memory_pool.used)·E`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "bandChartSettings": {
                "lower": "Job Manager",
                "upper": "Task Manager"
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "Job Manager",
                  "Task Manager"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "Heap Usage",
                "max": 100
              },
              "rightYAxisSettings": {
                "isLabelVisible": false
              },
              "tooltip": {
                "seriesDisplayMode": "single-line"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "Job Manager",
                    "Task Manager"
                  ],
                  "value": "sparkline",
                  "id": 1746605767648
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "Job Manager",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1746015971389
              },
              {
                "identifier": "Task Manager",
                "unitCategory": "percentage",
                "baseUnit": "percent",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1746016013552
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          },
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-30m",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "104": {
          "title": "Kinesis Captain Pings Dropped",
          "description": "",
          "type": "data",
          "query": "timeseries \n    cycles = sum(flink_taskmanager_job_task_operator_reason_mp_health_kinesis_captain_ping_dropped_ping_counter_by_reason, rate:1m), by: {k8s.namespace.name, reason}, interval:5m\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase, reason\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase"
                  ],
                  "value": "sparkline",
                  "id": 1747130377510
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "105": {
          "type": "markdown",
          "content": "# **Captain Pings**"
        },
        "106": {
          "title": "Pings on Inactive Captains",
          "description": "",
          "type": "data",
          "query": "timeseries \n    cycles = sum(flink_taskmanager_job_task_operator_mp_health_shift_adherence_non_adherent_events_counter), by: {k8s.namespace.name}, interval:5m\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd non_adherent_events_count = arrayDelta(cycles)\n| fields interval, timeframe, non_adherent_events_count\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "non_adherent_events_count"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "non_adherent_events_count"
                  ],
                  "value": "sparkline",
                  "id": 1747132533020
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "107": {
          "type": "markdown",
          "content": "# **Zones**"
        },
        "108": {
          "title": "Location Resolution Service API",
          "description": "",
          "type": "data",
          "query": "timeseries {\n    cycles = sum(flink_taskmanager_job_task_operator_mp_health_zone_retrieval_successful, rate:80s),\n    cycles1 = sum(flink_taskmanager_job_task_operator_mp_health_zero_zones_collected_from_api, rate:80s)\n  },\n  by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd  zone_retrieval_successful = arrayDelta(cycles)\n| fieldsAdd  no_zones_collected_from_api = arrayDelta(cycles1)\n| fields interval, timeframe, zone_retrieval_successful, no_zones_collected_from_api\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "zone_retrieval_successful",
                  "no_zones_collected_from_api"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              },
              "bandChartSettings": {
                "lower": "zone_retrieval_successful",
                "upper": "no_zones_collected_from_api"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "zone_retrieval_successful"
                  ],
                  "value": "sparkline",
                  "id": 1748208067213
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "zone_retrieval_successful",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1736340564248
              },
              {
                "identifier": "no_zones_collected_from_api",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1748208168700
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "109": {
          "title": "Latency",
          "description": "",
          "type": "data",
          "query": "timeseries \n    cycles = max(flink_taskmanager_job_task_operator_mp_health_zone_retrieval_latency_histogram_latency, rate:1m), by: {k8s.namespace.name}, interval:5m\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase"
                  ],
                  "value": "sparkline",
                  "id": 1746786583104
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "111": {
          "title": "Zone Errors",
          "description": "",
          "type": "data",
          "query": "timeseries {\n    cycles = sum(flink_taskmanager_job_task_operator_mp_health_zone_retrieval_operator_timeout, rate:1m), \n    cycles1 = sum(flink_taskmanager_job_task_operator_mp_health_zones_retrieved_less_than_total_records, rate:1m), \n    cycles2 = sum(flink_taskmanager_job_task_operator_mp_health_zone_retrieval_failed, rate:1m)\n  },\n  by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd zone_retrieval_timeout = arrayDelta(cycles)\n| fieldsAdd zones_less_than_total_records = arrayDelta(cycles1)\n| fieldsAdd zone_retrieval_failed = arrayDelta(cycles2)\n| fields interval, timeframe, zone_retrieval_failed, zones_less_than_total_records, zone_retrieval_timeout\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "zone_retrieval_failed",
                  "zones_less_than_total_records",
                  "zone_retrieval_timeout"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              },
              "bandChartSettings": {
                "lower": "zone_retrieval_failed",
                "upper": "zones_less_than_total_records"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": []
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "112": {
          "type": "markdown",
          "content": "# **Captain Adherence**"
        },
        "113": {
          "title": "Captain Shifts",
          "description": "",
          "type": "data",
          "query": "timeseries \n    cycles = sum(flink_taskmanager_job_task_operator_reason_mp_health_dropped_shift_enriched_event, rate:1m), \n    by: {k8s.namespace.name, reason}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd increase = arrayDelta(cycles)\n| fields interval, timeframe, increase, reason\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "increase"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "increase"
                  ],
                  "value": "sparkline",
                  "id": 1746792329265
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "increase",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_hour",
                "displayUnit": "count_per_minute",
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "114": {
          "title": "One minute window input/output",
          "description": "",
          "type": "data",
          "query": "timeseries \n    A = sum(flink_taskmanager_job_task_operator_numRecordsInPerSecond, rate:300s), \n    by: {k8s.namespace.name, operator_name},\n    filter: {matchesValue(operator_name, \"ShiftAdherenceStream_ShiftAdherenceReducer\")}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd recordsIn = arrayDelta(A)\n| append [ timeseries \n    B = sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond, rate:300s), \n    by: {k8s.namespace.name, operator_name},\n    filter: {matchesValue(operator_name, \"ShiftAdherenceStream_ShiftAdherenceReducer\")}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd recordsOut = arrayDelta(B)\n] \n| fields A, B, operator_name, interval, timeframe\n          ",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "A",
                  "B"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              },
              "bandChartSettings": {
                "lower": "A",
                "upper": "B"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "B"
                  ],
                  "value": "sparkline",
                  "id": 1747131527091
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-30m",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "115": {
          "title": "Errors",
          "description": "",
          "type": "data",
          "query": "timeseries \n      A = sum(flink_taskmanager_job_task_operator_mp_health_shift_adherence_more_than_one_event_out_of_window_error_counter), \n      by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd event_out_of_window = arrayDelta(A)\n| append [ timeseries\n      B = sum(flink_taskmanager_job_task_operator_mp_health_detected_issue_with_processing_time_timer), \n      by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd issue_with_processing_time = arrayDelta(B)\n] | append [timeseries \n      C = sum(flink_taskmanager_job_task_operator_reason_mp_health_dropped_shift_enriched_event), \n      by: {k8s.namespace.name, reason}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd dropped_shift_enriched_event = arrayDelta(C)\n]\n| fields event_out_of_window, issue_with_processing_time, dropped_shift_enriched_event, reason, interval, timeframe\n          ",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "event_out_of_window",
                  "issue_with_processing_time",
                  "dropped_shift_enriched_event"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              },
              "bandChartSettings": {
                "lower": "event_out_of_window",
                "upper": "issue_with_processing_time"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "dropped_shift_enriched_event"
                  ],
                  "value": "sparkline",
                  "id": 1746791685647
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "event_out_of_window",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_hour",
                "displayUnit": "count_per_minute",
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1748207890400
              },
              {
                "identifier": "issue_with_processing_time",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_hour",
                "displayUnit": "count_per_minute",
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1748207902025
              },
              {
                "identifier": "dropped_shift_enriched_event",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_hour",
                "displayUnit": "count_per_minute",
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1748207914330
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-30m",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "116": {
          "title": "Available captains out of zone",
          "description": "",
          "type": "data",
          "query": "timeseries \n    cycles = sum(flink_taskmanager_job_task_operator_mp_health_shift_adherence_non_adherent_events_counter), by: {k8s.namespace.name}\n| filter k8s.namespace.name == $Namespace\n| fieldsAdd pings = arrayDelta(cycles)\n| fields interval, timeframe, pings\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "pings"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "pings"
                  ],
                  "value": "sparkline",
                  "id": 1746790053536
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "pings",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_minute",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-24h",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "117": {
          "title": "Number of records out from sources",
          "description": "",
          "type": "data",
          "query": "timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond), \nby: {operator_name}, \n filter: {k8s.namespace.name == $Namespace } \n| filter startsWith(operator_name,\"Source:_\")\n| fields timeframe,interval,ri,operator_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "ri"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "ri",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2d",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "118": {
          "title": "Poison Letter",
          "description": "",
          "type": "data",
          "query": "timeseries value = max(flink_taskmanager_job_task_operator_stream_function_mp_health_poison_letter_counter),\nby: {stream,function},\nfilter:k8s.namespace.name==$Namespace\n| fieldsAdd increase = arrayDelta(value)\n| fields interval, timeframe, stream,function,increase",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "rate"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "rate"
                  ],
                  "value": "sparkline",
                  "id": 1747120681717
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "rate",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2d",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "119": {
          "title": "Record In to Operators",
          "description": "",
          "type": "data",
          "query": "timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsInPerSecond,rate:60s), \nby: {operator_name}, filter: {k8s.namespace.name == $Namespace }\n| fieldsAdd ri = ri[]/60\n| fields timeframe,interval,ri,operator_name\n\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "ri"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "ri",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2d",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "120": {
          "title": "Records out of operators",
          "description": "",
          "type": "data",
          "query": "timeseries ro = sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond), \nby: {operator_name}, filter: {k8s.namespace.name == $Namespace }\n| fieldsAdd ro = ro[]/60\n| fields timeframe,interval,ro,operator_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "ro"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "ro",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2d",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        },
        "121": {
          "title": "Data Freshness",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries max(flink_taskmanager_job_task_operator_currentOutputWatermark), interval: 1m, by: { operator_name }, filter: { matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(operator_name, \"*KAFKA*\") AND matchesValue(operator_name, \"*Writer*\") }\n| join [\n    timeseries min(flink_taskmanager_job_task_operator_currentOutputWatermark),\n    interval: 1m,\n    by: { operator_name },\n    filter: {\n        matchesValue(k8s.namespace.name, $Namespace) AND\n        matchesValue(operator_name, \"*KAFKA*\") AND\n        matchesValue(operator_name, \"*Writer*\")\n    }\n  ], on: { operator_name }, fields: { `min(flink_taskmanager_job_task_operator_currentOutputWatermark)` }\n| fieldsAdd C = ((`max(flink_taskmanager_job_task_operator_currentOutputWatermark)`[]-`min(flink_taskmanager_job_task_operator_currentOutputWatermark)`[])/1000)/6\n| fieldsRemove `max(flink_taskmanager_job_task_operator_currentOutputWatermark)`\n| fieldsRemove `min(flink_taskmanager_job_task_operator_currentOutputWatermark)`",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "C"
                ]
              },
              "leftYAxisSettings": {},
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": [
                {
                  "fields": [
                    "C"
                  ],
                  "value": "sparkline",
                  "id": 1747137238311
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "unitsOverrides": [
              {
                "identifier": "C",
                "unitCategory": "time",
                "baseUnit": "second",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": false,
                "added": 1747137517494
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_taskmanager_job_task_operator_currentOutputWatermark",
                  "aggregation": "max"
                },
                "by": [
                  "operator_name"
                ],
                "filter": "k8s.namespace.name = $Namespace AND operator_name = *KAFKA* AND operator_name = *Writer*"
              },
              {
                "id": "B",
                "isEnabled": false,
                "datatype": "metrics",
                "metric": {
                  "key": "flink_taskmanager_job_task_operator_currentOutputWatermark",
                  "aggregation": "min"
                },
                "by": [
                  "operator_name"
                ],
                "filter": "k8s.namespace.name = $Namespace AND operator_name = *KAFKA* AND operator_name = *Writer*"
              },
              {
                "id": "C",
                "isEnabled": true,
                "datatype": "expression",
                "expression": "((A-B)/1000)/6"
              }
            ],
            "globalCommands": {
              "interval": "1m"
            }
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "122": {
          "title": "Number of records out from sinks",
          "description": "",
          "type": "data",
          "query": "timeseries ri = sum(flink_taskmanager_job_task_operator_numRecordsInPerSecond\n,rate:60s), \nby: {operator_name}, \n filter: {k8s.namespace.name == $Namespace } \n| filter endsWith(operator_name,\"_Writer\")\n| filter startsWith(operator_name,\"KAFKA\")\n| fieldsAdd per_second = ri[]/60\n| fields timeframe,interval,per_second,operator_name",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "xAxisScaling": "analyzedTimeframe",
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "smooth",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "categoryAxisLabel": "operator_name",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [],
              "leftYAxisSettings": {
                "isLabelVisible": false,
                "label": "Service request count"
              },
              "seriesOverrides": [],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "per_second"
                ]
              },
              "legend": {
                "position": "bottom",
                "hidden": false
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "prefixIcon": "",
              "recordField": "flink_taskmanager_job_task_operator_cct_service_area_id_mp_health_dpv2_supply_demand_events_per_cycle",
              "autoscale": true,
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"k8s.pod.name\"]": 313.328125
              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto"
              },
              "displayedFields": [
                "operator_name"
              ],
              "dataMappings": {
                "value": "operator_name"
              },
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": "auto",
              "yAxis": {
                "label": "Frequency",
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "interval",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "operator_name"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "unitsOverrides": [
              {
                "identifier": "per_second",
                "unitCategory": "unspecified",
                "baseUnit": "count_per_second",
                "displayUnit": null,
                "decimals": 2,
                "suffix": "",
                "delimiter": true,
                "added": 1736340564248
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {},
          "timeframe": {
            "tileTimeframe": {
              "from": "now()-2d",
              "to": "now()"
            },
            "tileTimeframeEnabled": false
          }
        }
      },
      "layouts": {
        "16": {
          "x": 0,
          "y": 46,
          "w": 20,
          "h": 1
        },
        "38": {
          "x": 0,
          "y": 26,
          "w": 24,
          "h": 1
        },
        "42": {
          "x": 0,
          "y": 33,
          "w": 12,
          "h": 6
        },
        "46": {
          "x": 0,
          "y": 0,
          "w": 24,
          "h": 1
        },
        "59": {
          "x": 0,
          "y": 39,
          "w": 12,
          "h": 7
        },
        "65": {
          "x": 0,
          "y": 71,
          "w": 12,
          "h": 8
        },
        "66": {
          "x": 0,
          "y": 19,
          "w": 24,
          "h": 7
        },
        "67": {
          "x": 12,
          "y": 10,
          "w": 12,
          "h": 9
        },
        "76": {
          "x": 0,
          "y": 10,
          "w": 12,
          "h": 9
        },
        "77": {
          "x": 0,
          "y": 27,
          "w": 12,
          "h": 6
        },
        "78": {
          "x": 12,
          "y": 33,
          "w": 12,
          "h": 6
        },
        "79": {
          "x": 18,
          "y": 30,
          "w": 6,
          "h": 3
        },
        "80": {
          "x": 18,
          "y": 27,
          "w": 6,
          "h": 3
        },
        "92": {
          "x": 12,
          "y": 27,
          "w": 6,
          "h": 6
        },
        "93": {
          "x": 0,
          "y": 1,
          "w": 12,
          "h": 9
        },
        "102": {
          "x": 12,
          "y": 39,
          "w": 12,
          "h": 7
        },
        "103": {
          "x": 12,
          "y": 1,
          "w": 12,
          "h": 9
        },
        "104": {
          "x": 0,
          "y": 80,
          "w": 12,
          "h": 8
        },
        "105": {
          "x": 0,
          "y": 79,
          "w": 24,
          "h": 1
        },
        "106": {
          "x": 12,
          "y": 80,
          "w": 12,
          "h": 8
        },
        "107": {
          "x": 0,
          "y": 88,
          "w": 24,
          "h": 1
        },
        "108": {
          "x": 0,
          "y": 89,
          "w": 12,
          "h": 8
        },
        "109": {
          "x": 12,
          "y": 89,
          "w": 12,
          "h": 8
        },
        "111": {
          "x": 0,
          "y": 97,
          "w": 12,
          "h": 8
        },
        "112": {
          "x": 0,
          "y": 105,
          "w": 24,
          "h": 1
        },
        "113": {
          "x": 0,
          "y": 106,
          "w": 12,
          "h": 8
        },
        "114": {
          "x": 12,
          "y": 106,
          "w": 12,
          "h": 8
        },
        "115": {
          "x": 0,
          "y": 114,
          "w": 12,
          "h": 8
        },
        "116": {
          "x": 12,
          "y": 114,
          "w": 12,
          "h": 8
        },
        "117": {
          "x": 0,
          "y": 63,
          "w": 12,
          "h": 8
        },
        "118": {
          "x": 12,
          "y": 47,
          "w": 12,
          "h": 8
        },
        "119": {
          "x": 0,
          "y": 55,
          "w": 12,
          "h": 8
        },
        "120": {
          "x": 12,
          "y": 55,
          "w": 12,
          "h": 8
        },
        "121": {
          "x": 0,
          "y": 47,
          "w": 12,
          "h": 8
        },
        "122": {
          "x": 12,
          "y": 63,
          "w": 12,
          "h": 8
        }
      },
      "importedWithCode": false,
      "settings": {
        "gridLayout": {
          "mode": "responsive"
        }
      }
    }