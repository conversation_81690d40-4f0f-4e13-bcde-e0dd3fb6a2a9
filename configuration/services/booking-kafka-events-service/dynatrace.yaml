---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: DynatraceConfig
metadata:
  name: booking-kafka-events-service-dynatrace
  labels:
    service: booking-kafka-events-service
    scope: production
spec:
  oneAgent:
    enabled: true
    platform: "kubernetes"
---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: DynatraceConfig
metadata:
  name: booking-kafka-events-service-dynatrace
  labels:
    service: booking-kafka-events-service
    scope: non-production
spec:
  oneAgent:
    enabled: true
    platform: "kubernetes"
    version: edge
