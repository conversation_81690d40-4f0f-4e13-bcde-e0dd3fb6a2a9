---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: identity-common-dashboard
  labels:
    scope: global
spec:
  name: "[identity-common] Custom Dashboard"
  json: |
    {
    "version": 18,
    "variables": [
        {
            "key": "Cluster",
            "visible": true,
            "type": "query",
            "version": 1,
            "editable": true,
            "input": "fetch dt.entity.kubernetes_cluster\n| fields entity.name\n| sort entity.name asc",
            "multiple": false,
            "defaultValue": "captain-comms-service"
        },
        {
            "key": "Namespace",
            "visible": true,
            "type": "query",
            "version": 1,
            "editable": true,
            "input": "fetch dt.entity.cloud_application_namespace\n| fields id, name = entity.name\n| filter in(name, {\n  \"auth\",\n  \"authentication-service\",\n  \"authorization-policies\",\n  \"business-identity-service\",\n  \"captain-identity-service\",\n  \"captain-login-service\",\n  \"clients-service\",\n  \"communication-services\",\n  \"device-management-service\",\n  \"idp-service\",\n  \"marilyn-adapter\",\n  \"marilyn-authorization\",\n  \"marilyn-shard-service\",\n  \"onboarder-service\",\n  \"otp-service\",\n  \"password-recovery-service\",\n  \"profile-service\",\n  \"supplier-management\"\n  })\n| fields name\n| sort name asc",
            "multiple": false
        },
        {
            "key": "NamespaceID",
            "visible": false,
            "type": "query",
            "version": 1,
            "editable": true,
            "input": "fetch dt.entity.cloud_application_namespace\n| filter entity.name==$Namespace\n| filter in(id, classicEntitySelector(concat(\"type(CLOUD_APPLICATION_NAMESPACE),toRelationship.isClusterOfNamespace(type(KUBERNETES_CLUSTER),entityName.equals(\", $Cluster,\"))\")))\n| fields id",
            "multiple": false
        }
    ],
    "tiles": {
        "2": {
            "title": "[InfoBipProvider] Low OTP Verify vs Generate Ratio",
            "type": "data",
            "query": "timeseries verify = sum(otp_service_verify_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: { exception == \"none\" }\n| summarize {\n  verify_count = sum(verify[])\n},\nby: { timeframe, interval, provider }\n| join [\ntimeseries generate = sum(otp_service_generate_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: {exception == \"none\" }\n| summarize {\n  generate_count = sum(generate[])\n},\nby: { timeframe, interval, provider }\n],\nkind: inner,\non: { timeframe, interval, provider }\n| filter provider == \"InfoBipProvider\"\n// | filter provider == \"InfoBipProvider\"\n//         OR provider == \"EtisalatProvider\"\n//         OR provider == \"MessageBirdProvider\"\n//         OR provider == \"MittoSmsProvider\"\n//         OR provider == \"MittoWhatsappProvider\"\n//         OR provider == \"PhoenixProvider\"\n| fieldsAdd verify_15m   = arrayMovingSum(verify_count, 15),\n            generate_15m = arrayMovingSum(right.generate_count, 15)\n| fieldsAdd verify_generate_ratio_15m = (verify_15m[] / generate_15m[]) * 100\n| fieldsAdd entity.name = \"otp-service\"\n| fieldsAdd event_name = \"OTP Ratio verify/Gen\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, provider, event_name,verify_generate_ratio_15m",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "blue-steel",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "verify_generate_ratio_15m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "verify_generate_ratio_15m"
                            ],
                            "value": "sparkline",
                            "id": 1747308006657
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 26
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "4": {
            "type": "markdown",
            "content": "# Onboarder Service Alerts Dashboard"
        },
        "5": {
            "title": "[Identity] Onboarder-Login - Zero Successful Logins (ACMA|ICMA)",
            "type": "data",
            "query": "timeseries login_increases = sum(login_monitoring_total, rate:1m),\n  by: {device_type},\n  interval: 1m,\n  filter:\n      k8s.namespace.name == \"onboarder-service\"\n      and (device_type == \"ACMA\" or device_type == \"ICMA\")\n      and flow == \"LOGIN\"\n      and status == \"SUCCESS\"\n| fieldsAdd increases_over_30m = arrayMovingSum(login_increases, 30)\n| fieldsAdd entity.name = \"onboarder-service\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fieldsAdd event_name = \"Login Requests\"\n| fields timeframe, interval, event_name, increases_over_30m, device_type",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                },
                                "comparator": "≤",
                                "label": "30",
                                "value": 30
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≤",
                                "label": "5",
                                "value": 5
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "fireplace-inverted",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "increases_over_30m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "increases_over_30m"
                            ],
                            "value": "sparkline",
                            "id": 1746683388978
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 24
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "6": {
            "title": "[Identity] Onboarder-Onboard - Zero Successful Onboardings (ACMA|ICMA)",
            "type": "data",
            "query": "timeseries onboarding_increases = sum(onboard_monitoring_total, rate:1m),\n  by: {device_type},\n  filter:\n      k8s.namespace.name == \"onboarder-service\"\n    and (device_type == \"ACMA\" or device_type == \"ICMA\")\n    and flow == \"ONBOARD\"\n    and status == \"SUCCESS\",\n  interval: 1m\n| fieldsAdd increases_over_5m = arrayMovingSum(onboarding_increases, 5)\n| fieldsAdd entity.name = \"onboarder-service\"\n| fieldsAdd event_name = \"Onboard Requests\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, event_name, increases_over_5m, device_type",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-loglevel-emergency-default, #ae132d)"
                                },
                                "comparator": "≤",
                                "label": "1",
                                "value": 1
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "categorical",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "increases_over_5m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "increases_over_5m"
                            ],
                            "value": "sparkline",
                            "id": 1746683539123
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 24
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "7": {
            "title": "Low rate of successful onboardings",
            "type": "data",
            "query": "timeseries count = sum(onboard_monitoring_total),\n  by: {status, flow},\n  filter: { k8s.namespace.name == \"onboarder-service\"}\n| summarize {\n  {\n    success = sum(if(status == \"SUCCESS\", count[], else: count[] * 0)),\n    requests = sum(if(status == \"REQUEST_RECEIVED\", count[], else: count[] * 0))\n  },\n  by: { timeframe, interval, flow }\n}\n| fieldsAdd success_10m  = arrayMovingSum(success, 10),\n            requests_10m = arrayMovingSum(requests, 10)\n| fieldsAdd success_rate_over_10m = success_10m[] / requests_10m[]\n| fieldsAdd entity.name = \"onboarder-service\"\n| fieldsAdd event_name = \"Onboard\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, event_name, success_rate_over_10m, flow",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≤",
                                "label": "0.02",
                                "value": 0.02
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "categorical",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "success_rate_over_10m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "success_rate_over_10m"
                            ],
                            "value": "sparkline",
                            "id": 1746683735439
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 24
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "8": {
            "title": "[Identity] Onboarder-Create-Guest - High number of guest creations",
            "type": "data",
            "query": "\ntimeseries guest_creations = sum(guest_monitoring_total),\nfilter: k8s.namespace.name == \"onboarder-service\" and flow == \"GUEST_CREATION\",\ninterval: 1m\n| fieldsAdd entity.name = \"onboarder-service\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, guest_creations",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "300",
                                "value": 300
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "categorical",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "guest_creations"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "guest_creations"
                            ],
                            "value": "sparkline",
                            "id": 1746683949708
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 24
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "11": {
            "title": "[MittoSmsProvider] Low OTP Verify vs Generate Ratio",
            "type": "data",
            "query": "timeseries verify = sum(otp_service_verify_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: { exception == \"none\" }\n| summarize {\n  verify_count = sum(verify[])\n},\nby: { timeframe, interval, provider }\n| join [\ntimeseries generate = sum(otp_service_generate_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: {exception == \"none\" }\n| summarize {\n  generate_count = sum(generate[])\n},\nby: { timeframe, interval, provider }\n],\nkind: inner,\non: { timeframe, interval, provider }\n| filter provider == \"MittoSmsProvider\"\n// | filter provider == \"InfoBipProvider\"\n//         OR provider == \"EtisalatProvider\"\n//         OR provider == \"MessageBirdProvider\"\n//         OR provider == \"MittoSmsProvider\"\n//         OR provider == \"MittoWhatsappProvider\"\n//         OR provider == \"PhoenixProvider\"\n| fieldsAdd verify_15m   = arrayMovingSum(verify_count, 15),\n            generate_15m = arrayMovingSum(right.generate_count, 15)\n| fieldsAdd verify_generate_ratio_15m = (verify_15m[] / generate_15m[]) * 100\n| fieldsAdd entity.name = \"otp-service\"\n| fieldsAdd event_name = \"OTP Ratio verify/Gen\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, provider, event_name,verify_generate_ratio_15m",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "apdex-inverted",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "verify_generate_ratio_15m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "verify_generate_ratio_15m"
                            ],
                            "value": "sparkline",
                            "id": 1747308006870
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 26
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "12": {
            "title": "[EtisalatProvider] Low OTP Verify vs Generate Ratio",
            "type": "data",
            "query": "timeseries verify = sum(otp_service_verify_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: { exception == \"none\" }\n| summarize {\n  verify_count = sum(verify[])\n},\nby: { timeframe, interval, provider }\n| join [\ntimeseries generate = sum(otp_service_generate_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: {exception == \"none\" }\n| summarize {\n  generate_count = sum(generate[])\n},\nby: { timeframe, interval, provider }\n],\nkind: inner,\non: { timeframe, interval, provider }\n| filter provider == \"EtisalatProvider\"\n// | filter provider == \"InfoBipProvider\"\n//         OR provider == \"EtisalatProvider\"\n//         OR provider == \"MessageBirdProvider\"\n//         OR provider == \"MittoSmsProvider\"\n//         OR provider == \"MittoWhatsappProvider\"\n//         OR provider == \"PhoenixProvider\"\n| fieldsAdd verify_15m   = arrayMovingSum(verify_count, 15),\n            generate_15m = arrayMovingSum(right.generate_count, 15)\n| fieldsAdd verify_generate_ratio_15m = (verify_15m[] / generate_15m[]) * 100\n| fieldsAdd entity.name = \"otp-service\"\n| fieldsAdd event_name = \"OTP Ratio verify/Gen\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, provider, event_name,verify_generate_ratio_15m",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "log-level-inverted",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "verify_generate_ratio_15m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "verify_generate_ratio_15m"
                            ],
                            "value": "sparkline",
                            "id": 1747308049374
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 26
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "13": {
            "title": "[MessageBirdProvider] Low OTP Verify vs Generate Ratio",
            "type": "data",
            "query": "timeseries verify = sum(otp_service_verify_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: { exception == \"none\" }\n| summarize {\n  verify_count = sum(verify[])\n},\nby: { timeframe, interval, provider }\n| join [\ntimeseries generate = sum(otp_service_generate_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: {exception == \"none\" }\n| summarize {\n  generate_count = sum(generate[])\n},\nby: { timeframe, interval, provider }\n],\nkind: inner,\non: { timeframe, interval, provider }\n| filter provider == \"MessageBirdProvider\"\n// | filter provider == \"InfoBipProvider\"\n//         OR provider == \"EtisalatProvider\"\n//         OR provider == \"MessageBirdProvider\"\n//         OR provider == \"MittoSmsProvider\"\n//         OR provider == \"MittoWhatsappProvider\"\n//         OR provider == \"PhoenixProvider\"\n| fieldsAdd verify_15m   = arrayMovingSum(verify_count, 15),\n            generate_15m = arrayMovingSum(right.generate_count, 15)\n| fieldsAdd verify_generate_ratio_15m = (verify_15m[] / generate_15m[]) * 100\n| fieldsAdd entity.name = \"otp-service\"\n| fieldsAdd event_name = \"OTP Ratio verify/Gen\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, provider, event_name,verify_generate_ratio_15m",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "categorical",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "verify_generate_ratio_15m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "verify_generate_ratio_15m"
                            ],
                            "value": "sparkline",
                            "id": 1747308050609
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 26
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "14": {
            "title": "[PhoenixProvider] Low OTP Verify vs Generate Ratio",
            "type": "data",
            "query": "timeseries verify = sum(otp_service_verify_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: { exception == \"none\" }\n| summarize {\n  verify_count = sum(verify[])\n},\nby: { timeframe, interval, provider }\n| join [\ntimeseries generate = sum(otp_service_generate_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: {exception == \"none\" }\n| summarize {\n  generate_count = sum(generate[])\n},\nby: { timeframe, interval, provider }\n],\nkind: inner,\non: { timeframe, interval, provider }\n| filter provider == \"PhoenixProvider\"\n// | filter provider == \"InfoBipProvider\"\n//         OR provider == \"EtisalatProvider\"\n//         OR provider == \"MessageBirdProvider\"\n//         OR provider == \"MittoSmsProvider\"\n//         OR provider == \"MittoWhatsappProvider\"\n//         OR provider == \"PhoenixProvider\"\n| fieldsAdd verify_15m   = arrayMovingSum(verify_count, 15),\n            generate_15m = arrayMovingSum(right.generate_count, 15)\n| fieldsAdd verify_generate_ratio_15m = (verify_15m[] / generate_15m[]) * 100\n| fieldsAdd entity.name = \"otp-service\"\n| fieldsAdd event_name = \"OTP Ratio verify/Gen\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, provider, event_name,verify_generate_ratio_15m",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "swamps-inverted",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "verify_generate_ratio_15m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "verify_generate_ratio_15m"
                            ],
                            "value": "sparkline",
                            "id": 1747308051661
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 26
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "15": {
            "title": "[MittoWhatsappProvider] Low OTP Verify vs Generate Ratio",
            "type": "data",
            "query": "timeseries verify = sum(otp_service_verify_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: { exception == \"none\" }\n| summarize {\n  verify_count = sum(verify[])\n},\nby: { timeframe, interval, provider }\n| join [\ntimeseries generate = sum(otp_service_generate_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: {exception == \"none\" }\n| summarize {\n  generate_count = sum(generate[])\n},\nby: { timeframe, interval, provider }\n],\nkind: inner,\non: { timeframe, interval, provider }\n| filter provider == \"MittoWhatsappProvider\"\n// | filter provider == \"InfoBipProvider\"\n//         OR provider == \"EtisalatProvider\"\n//         OR provider == \"MessageBirdProvider\"\n//         OR provider == \"MittoSmsProvider\"\n//         OR provider == \"MittoWhatsappProvider\"\n//         OR provider == \"PhoenixProvider\"\n| fieldsAdd verify_15m   = arrayMovingSum(verify_count, 15),\n            generate_15m = arrayMovingSum(right.generate_count, 15)\n| fieldsAdd verify_generate_ratio_15m = (verify_15m[] / generate_15m[]) * 100\n| fieldsAdd entity.name = \"otp-service\"\n| fieldsAdd event_name = \"OTP Ratio verify/Gen\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, provider, event_name,verify_generate_ratio_15m",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "categoryAxisLabel": "host.name",
                        "valueAxisLabel": "interval",
                        "tooltipVariant": "single"
                    },
                    "colorPalette": "fireplace-inverted",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "verify_generate_ratio_15m"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": false,
                        "label": "Process CPU usage"
                    },
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.process.cpu.usage",
                    "prefixIcon": "",
                    "recordField": "dt.process.cpu.usage",
                    "autoscale": true,
                    "alignment": "center",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "verify_generate_ratio_15m"
                            ],
                            "value": "sparkline",
                            "id": 1747308052053
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "displayedFields": [
                        "host.name"
                    ],
                    "dataMappings": {
                        "value": "host.name"
                    },
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": "auto",
                    "yAxis": {
                        "label": "Frequency",
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "truncationMode": "middle",
                    "displayedFields": [
                        "host.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": false,
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 26
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "16": {
            "type": "markdown",
            "content": "# Common"
        },
        "17": {
            "title": "Request Rates (per min)",
            "type": "data",
            "subType": "dql-builder-metrics",
            "query": "timeseries { sum(dt.service.request.count, rate: 1m), value.A = avg(dt.service.request.count, rate: 1m, scalar: true) }, by: { http.response.status_code, endpoint.name }, filter: { matchesValue(k8s.namespace.name, $Namespace) }",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "valueAxisScale": "linear"
                    },
                    "colorPalette": "categorical",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisScaling": "analyzedTimeframe",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "interval",
                        "value.A"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "sum(dt.service.request.count, rate:1m)"
                        ]
                    },
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Service request count"
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "",
                    "prefixIcon": "AnalyticsIcon",
                    "isIconVisible": false,
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "sum(dt.service.request.count, rate:1m)"
                            ],
                            "value": "sparkline",
                            "id": 1747635912372
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto",
                        "ratio": "auto"
                    },
                    "dataMappings": {},
                    "displayedFields": [],
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": {
                        "position": "auto"
                    },
                    "yAxis": {
                        "label": "Frequency",
                        "isLabelVisible": true,
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [],
                    "variant": "single",
                    "truncationMode": "middle"
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": true
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "queryConfig": {
                "version": "13.5.2",
                "subQueries": [
                    {
                        "id": "A",
                        "isEnabled": true,
                        "datatype": "metrics",
                        "metric": {
                            "key": "dt.service.request.count",
                            "aggregation": "sum"
                        },
                        "by": [
                            "http.response.status_code",
                            "endpoint.name"
                        ],
                        "rate": "1m",
                        "filter": "k8s.namespace.name =$Namespace "
                    }
                ],
                "globalCommands": {}
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "18": {
            "type": "markdown",
            "content": "### Pods in Namespace: [$Namespace](/ui/intent/dynatrace.kubernetes/entityKubernetesNamespace/#{\"id\":\"$NamespaceID\"})"
        },
        "19": {
            "title": "Cluster CPU Utilization Contribution",
            "type": "data",
            "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage)\n}, filter: {\n  k8s.cluster.name == $Cluster\n}, by: { k8s.cluster.name },\nfrom: -2m\n| fieldsAdd cpu_usage_cluster = arrayFirst(cpu_usage)\n| fieldsRemove cpu_usage\n| join [\n  timeseries {\n    cpu_usage = sum(dt.kubernetes.container.cpu_usage)\n  }, filter: {\n    k8s.cluster.name == $Cluster AND\n    k8s.namespace.name == $Namespace\n  }, by: { k8s.namespace.name, k8s.cluster.name },\n  from: -2m\n], executionOrder: leftFirst, on: { k8s.cluster.name }, fields: { cpu_usage }\n| fieldsAdd cpu_usage_namespace = arrayFirst(cpu_usage)\n| fieldsAdd cpu_usage_percent = cpu_usage_namespace * 100  / cpu_usage_cluster",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "cpu_usage_percent",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 70
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 90
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.cluster.name",
                        "categoryAxisLabel": "k8s.cluster.name",
                        "valueAxis": "cpu_usage_cluster",
                        "valueAxisLabel": "cpu_usage_cluster"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "cpu_usage"
                        ],
                        "leftAxisDimensions": [
                            "k8s.cluster.name",
                            "cpu_usage_cluster",
                            "cpu_usage_namespace",
                            "cpu_usage_percent"
                        ]
                    },
                    "hiddenLegendFields": [
                        "interval",
                        "cpu_usage_cluster",
                        "cpu_usage_namespace",
                        "cpu_usage_percent"
                    ],
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - CPU usage"
                    }
                },
                "singleValue": {
                    "showLabel": false,
                    "label": "cpu_usage_percent",
                    "prefixIcon": "",
                    "recordField": "cpu_usage_percent",
                    "autoscale": true,
                    "sparklineSettings": {
                        "isVisible": false,
                        "showTicks": false
                    },
                    "alignment": "center",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto",
                        "upward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "neutral": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "downward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        }
                    },
                    "colorThresholdTarget": "background"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "cpu_usage"
                            ],
                            "value": "sparkline",
                            "id": 1734614775828
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "k8s.cluster.name"
                    },
                    "displayedFields": [
                        "k8s.cluster.name"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "cpu_usage_cluster",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "cpu_usage_namespace",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "cpu_usage_percent",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": [
                        "k8s.cluster.name"
                    ]
                },
                "unitsOverrides": [
                    {
                        "identifier": "cpu_usage_percent",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1716814972871
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "20": {
            "title": "CPU Requests Utilization",
            "type": "data",
            "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage),\n  requests_cpu = sum(dt.kubernetes.container.requests_cpu)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd cpu_usage_namespace = arrayFirst(cpu_usage)\n| fieldsAdd cpu_requests_namespace = arrayFirst(requests_cpu)\n| fieldsAdd cpu_requests_utilization_percent = cpu_usage_namespace * 100 / cpu_requests_namespace",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "cpu_requests_utilization_percent",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 100
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 130
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.namespace.name",
                        "categoryAxisLabel": "k8s.namespace.name",
                        "valueAxis": "cpu_usage_namespace",
                        "valueAxisLabel": "cpu_usage_namespace"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "cpu_usage",
                            "requests_cpu"
                        ],
                        "leftAxisDimensions": [
                            "k8s.namespace.name",
                            "cpu_usage_namespace",
                            "cpu_requests_namespace",
                            "cpu_requests_utilization_percent"
                        ]
                    },
                    "hiddenLegendFields": [
                        "k8sspace.name",
                        "interval",
                        "cpu_usage_namespace",
                        "cpu_requests_namespace",
                        "cpu_requests_utilization_percent"
                    ],
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - CPU usage • Kubernetes: Container - CPU requests"
                    }
                },
                "singleValue": {
                    "showLabel": false,
                    "label": "cpu_requests_utilization_percent",
                    "prefixIcon": "",
                    "recordField": "cpu_requests_utilization_percent",
                    "autoscale": true,
                    "sparklineSettings": {
                        "isVisible": false,
                        "showTicks": false
                    },
                    "alignment": "center",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto",
                        "upward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "neutral": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "downward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        }
                    },
                    "colorThresholdTarget": "background"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": []
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "k8s.namespace.name"
                    },
                    "displayedFields": [
                        "k8s.namespace.name"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "cpu_requests_utilization_percent",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715164486934
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 20000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "21": {
            "title": "CPU Limits Utilization",
            "type": "data",
            "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage),\n  limits_cpu = sum(dt.kubernetes.container.limits_cpu)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd cpu_usage_namespace = arrayFirst(cpu_usage)\n| fieldsAdd cpu_limits_namespace = arrayFirst(limits_cpu)\n| fieldsAdd cpu_limits_utilization_percent = cpu_usage_namespace * 100 / cpu_limits_namespace",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "cpu_limits_utilization_percent",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 100
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 150
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.namespace.name",
                        "categoryAxisLabel": "k8s.namespace.name",
                        "valueAxis": "cpu_usage_namespace",
                        "valueAxisLabel": "cpu_usage_namespace"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "cpu_usage",
                            "limits_cpu"
                        ],
                        "leftAxisDimensions": [
                            "k8s.namespace.name",
                            "cpu_usage_namespace",
                            "cpu_limits_namespace",
                            "cpu_limits_utilization_percent"
                        ]
                    },
                    "hiddenLegendFields": [
                        "k8sspace.name",
                        "interval",
                        "cpu_usage_namespace",
                        "cpu_limits_namespace",
                        "cpu_limits_utilization_percent"
                    ],
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - CPU usage • Kubernetes: Container - CPU limits"
                    }
                },
                "singleValue": {
                    "showLabel": false,
                    "label": "cpu_limits_utilization_percent",
                    "prefixIcon": "",
                    "recordField": "cpu_limits_utilization_percent",
                    "autoscale": true,
                    "sparklineSettings": {
                        "isVisible": false,
                        "showTicks": false
                    },
                    "alignment": "center",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto",
                        "neutral": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "upward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "downward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        }
                    },
                    "colorThresholdTarget": "background"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": []
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "k8s.namespace.name"
                    },
                    "displayedFields": [
                        "k8s.namespace.name"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "cpu_limits_utilization_percent",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715164486934
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 20000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "22": {
            "title": "Pods",
            "type": "data",
            "query": "timeseries {\n  pods_namespace = sum(dt.kubernetes.pods)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace AND\n  if(isNotNull(pod_phase), pod_phase == \"Running\", else: true)\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd pods_namespace = arrayFirst(pods_namespace)",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "pods_namespace",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": {
                                    "Default": "var(--dt-colors-charts-categorical-color-09-default, #649438)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 3
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 5
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 8
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "xAxisScaling": "analyzedTimeframe",
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": [
                            "k8s.namespace.name"
                        ],
                        "categoryAxisLabel": "k8s.namespace.name",
                        "valueAxis": [
                            "pods_namespace"
                        ],
                        "valueAxisLabel": "pods_namespace",
                        "tooltipVariant": "single"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "interval"
                        ]
                    },
                    "hiddenLegendFields": [
                        "k8sspace.name"
                    ],
                    "truncationMode": "middle",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "valueRepresentation": "absolute",
                    "leftYAxisSettings": {}
                },
                "singleValue": {
                    "showLabel": false,
                    "label": "pods_namespace",
                    "prefixIcon": "",
                    "recordField": "pods_namespace",
                    "autoscale": true,
                    "sparklineSettings": {
                        "isVisible": true,
                        "showTicks": false
                    },
                    "alignment": "center",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto",
                        "neutral": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "upward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "downward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "trendField": "pods_namespace"
                    },
                    "colorThresholdTarget": "background"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": []
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "pods_namespace"
                    },
                    "displayedFields": [
                        "k8s.namespace.name"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "blue"
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "pods_namespace",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": [
                        "k8s.namespace.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "unitsOverrides": [
                    {
                        "identifier": "pods_namespace",
                        "unitCategory": "unspecified",
                        "baseUnit": "count",
                        "displayUnit": null,
                        "decimals": 0,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1734622122485
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 20000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "23": {
            "title": "Cluster Memory Utilization Contribution",
            "type": "data",
            "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set)\n}, filter: {\n  k8s.cluster.name == $Cluster\n}, by: { k8s.cluster.name },\nfrom: -2m\n| fieldsAdd memory_usage_cluster = arrayFirst(memory_usage)\n| fieldsRemove memory_usage\n| join [\n  timeseries {\n    memory_usage = sum(dt.kubernetes.container.memory_working_set)\n  }, filter: {\n    k8s.cluster.name == $Cluster AND\n    k8s.namespace.name == $Namespace\n  }, by: { k8s.namespace.name, k8s.cluster.name },\n  from: -2m\n], executionOrder: leftFirst, on: { k8s.cluster.name }, fields: { memory_usage }\n| fieldsAdd memory_usage_namespace = arrayFirst(memory_usage)\n| fieldsAdd memory_usage_percent = memory_usage_namespace * 100  / memory_usage_cluster",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "memory_usage_percent",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 70
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 90
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.cluster.name",
                        "categoryAxisLabel": "k8s.cluster.name",
                        "valueAxis": "memory_usage_cluster",
                        "valueAxisLabel": "memory_usage_cluster"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "memory_usage"
                        ],
                        "leftAxisDimensions": [
                            "k8s.cluster.name",
                            "memory_usage_cluster",
                            "memory_usage_namespace",
                            "memory_usage_percent"
                        ]
                    },
                    "hiddenLegendFields": [
                        "k8s.cluster",
                        "interval",
                        "memory_usage_cluster",
                        "memory_usage_namespace",
                        "memory_usage_percent"
                    ],
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - working set memory"
                    }
                },
                "singleValue": {
                    "showLabel": false,
                    "label": "memory_usage_percent",
                    "prefixIcon": "",
                    "recordField": "memory_usage_percent",
                    "autoscale": true,
                    "sparklineSettings": {
                        "isVisible": false,
                        "showTicks": false
                    },
                    "alignment": "center",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto",
                        "neutral": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "upward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "downward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        }
                    },
                    "colorThresholdTarget": "background"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "memory_usage"
                            ],
                            "value": "sparkline",
                            "id": 1734615651470
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "k8s.cluster.name"
                    },
                    "displayedFields": [
                        "k8s.cluster.name"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "memory_usage_cluster",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "memory_usage_namespace",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "memory_usage_percent",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": [
                        "k8s.cluster.name"
                    ]
                },
                "unitsOverrides": [
                    {
                        "identifier": "memory_usage_percent",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715164486934
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "24": {
            "title": "Memory Requests Utilization",
            "type": "data",
            "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set),\n  requests_memory = sum(dt.kubernetes.container.requests_memory)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd memory_usage_namespace = arrayFirst(memory_usage)\n| fieldsAdd memory_requests_namespace = arrayFirst(requests_memory)\n| fieldsAdd memory_requests_utilization_percent = memory_usage_namespace * 100 / memory_requests_namespace",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "memory_requests_utilization_percent",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 100
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 130
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.namespace.name",
                        "categoryAxisLabel": "k8s.namespace.name",
                        "valueAxis": "memory_usage_namespace",
                        "valueAxisLabel": "memory_usage_namespace"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "memory_usage",
                            "requests_memory"
                        ],
                        "leftAxisDimensions": [
                            "k8s.namespace.name",
                            "memory_usage_namespace",
                            "memory_requests_namespace",
                            "memory_requests_utilization_percent"
                        ]
                    },
                    "hiddenLegendFields": [
                        "k8sspace.name",
                        "interval",
                        "memory_usage_namespace",
                        "memory_requests_namespace",
                        "memory_requests_utilization_percent"
                    ],
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - working set memory • Kubernetes: Container - memory requests"
                    }
                },
                "singleValue": {
                    "showLabel": false,
                    "label": "memory_requests_utilization_percent",
                    "prefixIcon": "",
                    "recordField": "memory_requests_utilization_percent",
                    "autoscale": true,
                    "sparklineSettings": {
                        "isVisible": false,
                        "showTicks": false
                    },
                    "alignment": "center",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto",
                        "upward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "neutral": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "downward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        }
                    },
                    "colorThresholdTarget": "background"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": []
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "k8s.namespace.name"
                    },
                    "displayedFields": [
                        "k8s.namespace.name"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "memory_requests_utilization_percent",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715164486934
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 20000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "25": {
            "title": "Memory Limits Utilization",
            "type": "data",
            "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set),\n  limits_memory = sum(dt.kubernetes.container.limits_memory)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { k8s.namespace.name },\nfrom: -2m\n| fieldsAdd memory_usage_namespace = arrayFirst(memory_usage)\n| fieldsAdd memory_limits_namespace = arrayFirst(limits_memory)\n| fieldsAdd memory_limits_utilization_percent = memory_usage_namespace * 100 / memory_limits_namespace",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "memory_limits_utilization_percent",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-categorical-color-14-default, #d56b1a)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 70
                            },
                            {
                                "id": 2,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 90
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.namespace.name",
                        "categoryAxisLabel": "k8s.namespace.name",
                        "valueAxis": "memory_usage_namespace",
                        "valueAxisLabel": "memory_usage_namespace"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "memory_usage",
                            "limits_memory"
                        ],
                        "leftAxisDimensions": [
                            "k8s.namespace.name",
                            "memory_usage_namespace",
                            "memory_limits_namespace",
                            "memory_limits_utilization_percent"
                        ]
                    },
                    "hiddenLegendFields": [
                        "k8sspace.name",
                        "interval",
                        "memory_usage_namespace",
                        "memory_limits_namespace",
                        "memory_limits_utilization_percent"
                    ],
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - working set memory • Kubernetes: Container - memory limits"
                    }
                },
                "singleValue": {
                    "showLabel": false,
                    "label": "memory_limits_utilization_percent",
                    "prefixIcon": "",
                    "recordField": "memory_limits_utilization_percent",
                    "autoscale": true,
                    "sparklineSettings": {
                        "isVisible": false,
                        "showTicks": false
                    },
                    "alignment": "center",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto",
                        "neutral": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "downward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        },
                        "upward": {
                            "Default": "var(--dt-colors-charts-categorical-color-02-default, #2c2f3f)"
                        }
                    },
                    "colorThresholdTarget": "background"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": []
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "k8s.namespace.name"
                    },
                    "displayedFields": [
                        "k8s.namespace.name"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "memory_limits_utilization_percent",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715164486934
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 20000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "26": {
            "title": "CPU Usage per Pod",
            "type": "data",
            "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage, rollup:avg)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name }\n| filter isNotNull(cpu_usage)\n| sort cpu_usage desc\n| limit 20",
            "visualization": "areaChart",
            "visualizationSettings": {
                "autoSelectVisualization": false,
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.pod.name",
                        "categoryAxisLabel": "k8s.pod.name",
                        "valueAxis": "interval",
                        "valueAxisLabel": "interval"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "cpu_usage"
                        ],
                        "leftAxisDimensions": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ]
                    },
                    "hiddenLegendFields": [
                        "dt.entity.cloud_application_instance",
                        "interval"
                    ],
                    "legend": {
                        "position": "bottom",
                        "hidden": false
                    },
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - CPU usage"
                    },
                    "xAxisScaling": "analyzedTimeframe",
                    "truncationMode": "middle",
                    "colorPalette": "swamps-inverted"
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "id",
                    "prefixIcon": "",
                    "recordField": "id",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "cpu_usage"
                            ],
                            "value": "sparkline",
                            "id": 1734615651544
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "dt.entity.cloud_application_instance"
                    },
                    "displayedFields": [
                        "dt.entity.cloud_application_instance"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": [
                        "dt.entity.cloud_application_instance",
                        "k8s.pod.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "unitsOverrides": [
                    {
                        "identifier": "cpu",
                        "unitCategory": "unspecified",
                        "baseUnit": "none",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715178821410
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "27": {
            "title": "CPU Quota",
            "type": "data",
            "query": "timeseries {\n  cpu_usage = sum(dt.kubernetes.container.cpu_usage),\n  cpu_throttled = sum(dt.kubernetes.container.cpu_throttled),\n  requests_cpu = sum(dt.kubernetes.container.requests_cpu),\n  limits_cpu = sum(dt.kubernetes.container.limits_cpu)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name },\nfrom: -2m,\nnonempty: true,\nunion: true\n| fieldsRemove interval, timeframe\n| fieldsAdd cpu_usage = arrayFirst(cpu_usage)\n| fieldsAdd cpu_throttled = arrayFirst(cpu_throttled)\n| fieldsAdd requests_cpu = arrayFirst(requests_cpu)\n| fieldsAdd limits_cpu = arrayFirst(limits_cpu)\n| fieldsAdd requests_cpu_percent = cpu_usage / requests_cpu * 100\n| fieldsAdd limits_cpu_percent = cpu_usage / limits_cpu * 100\n| fieldsAdd cpu_slack = (requests_cpu - cpu_usage) / 1000\n| sort cpu_usage desc\n| fieldsRename `Name` = k8s.pod.name, `CPU Usage` = cpu_usage, `CPU Throttled` = cpu_throttled, `CPU Requests` = requests_cpu, `CPU Requests %` = requests_cpu_percent, `CPU Limits` = limits_cpu, `CPU Limits %` = limits_cpu_percent, `CPU Slack` = cpu_slack\n",
            "visualization": "table",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "dt.entity.cloud_application_instance",
                        "categoryAxisLabel": "dt.entity.cloud_application_instance",
                        "valueAxis": "CPU Usage",
                        "valueAxisLabel": "CPU Usage"
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "id",
                    "prefixIcon": "",
                    "recordField": "id",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": false
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [
                        [
                            "id"
                        ],
                        [
                            "namespace.id"
                        ],
                        [
                            "namespace.labels"
                        ],
                        [
                            "namespace.annotations"
                        ],
                        [
                            "namespace.age"
                        ],
                        [
                            "cluster.id"
                        ],
                        [
                            "k8s.pod.uid"
                        ],
                        [
                            "dt.entity.cloud_application_instance"
                        ]
                    ],
                    "lineWrapIds": [],
                    "columnWidths": {
                        "[\"limits_cpu\"]": 124.046875,
                        "[\"requests_cpu\"]": 142.453125,
                        "[\"namespace.name\"]": 231.140625,
                        "[\"cpu_usage\"]": 157.8125,
                        "[\"cpu_throttled\"]": 164.46875,
                        "[\"requests_cpu_percent\"]": 191.28125,
                        "[\"limits_cpu_percent\"]": 175.859375
                    },
                    "sortBy": {
                        "columnId": "[\"CPU Usage\"]",
                        "direction": "descending"
                    },
                    "columnTypeOverrides": []
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "dt.entity.cloud_application_instance"
                    },
                    "displayedFields": [
                        "dt.entity.cloud_application_instance"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "CPU Usage",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": [
                        "dt.entity.cloud_application_instance",
                        "Name"
                    ]
                },
                "unitsOverrides": [
                    {
                        "identifier": "CPU Usage",
                        "unitCategory": "unspecified",
                        "baseUnit": "millicore",
                        "displayUnit": null,
                        "decimals": 1,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715618539759
                    },
                    {
                        "identifier": "CPU Throttled",
                        "unitCategory": "unspecified",
                        "baseUnit": "millicore",
                        "displayUnit": null,
                        "decimals": 1,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715618579313
                    },
                    {
                        "identifier": "CPU Requests",
                        "unitCategory": "unspecified",
                        "baseUnit": "millicore",
                        "displayUnit": null,
                        "decimals": 1,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715618597827
                    },
                    {
                        "identifier": "CPU Limits",
                        "unitCategory": "unspecified",
                        "baseUnit": "millicore",
                        "displayUnit": null,
                        "decimals": 1,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715618609828
                    },
                    {
                        "identifier": "CPU Slack",
                        "unitCategory": "unspecified",
                        "baseUnit": "core",
                        "displayUnit": null,
                        "decimals": 1,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715618618693
                    },
                    {
                        "identifier": "CPU Requests %",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715686358414
                    },
                    {
                        "identifier": "CPU Limits %",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715687708509
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "28": {
            "title": "Memory Usage per Pod",
            "type": "data",
            "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set, rollup:avg)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name }\n| sort memory_usage desc\n| limit 20",
            "visualization": "areaChart",
            "visualizationSettings": {
                "autoSelectVisualization": false,
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "k8s.pod.name",
                        "categoryAxisLabel": "k8s.pod.name",
                        "valueAxis": "interval",
                        "valueAxisLabel": "interval"
                    },
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "memory_usage"
                        ],
                        "leftAxisDimensions": [
                            "dt.entity.cloud_application_instance",
                            "k8s.pod.name"
                        ]
                    },
                    "hiddenLegendFields": [
                        "dt.entity.cloud_application_instance"
                    ],
                    "legend": {
                        "position": "bottom",
                        "hidden": false
                    },
                    "valueRepresentation": "absolute",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "leftYAxisSettings": {
                        "isLabelVisible": true,
                        "label": "Kubernetes: Container - working set memory"
                    },
                    "xAxisScaling": "analyzedTimeframe",
                    "truncationMode": "middle",
                    "colorPalette": "vulnerability-status-inverted"
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "id",
                    "prefixIcon": "",
                    "recordField": "id",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    },
                    "sparklineSettings": {
                        "record": "memory_usage"
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "memory_usage"
                            ],
                            "value": "sparkline",
                            "id": 1734614775475
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "dt.entity.cloud_application_instance"
                    },
                    "displayedFields": [
                        "dt.entity.cloud_application_instance"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": [
                        "dt.entity.cloud_application_instance",
                        "k8s.pod.name"
                    ]
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "unitsOverrides": [],
                "legend": {
                    "showLegend": false,
                    "position": "auto",
                    "ratio": 26
                },
                "dataMapping": {
                    "value": "interval"
                },
                "label": {
                    "showLabel": true,
                    "label": "interval"
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "29": {
            "title": "Memory Quota",
            "type": "data",
            "query": "timeseries {\n  memory_usage = sum(dt.kubernetes.container.memory_working_set),\n  requests_memory = sum(dt.kubernetes.container.requests_memory),\n  limits_memory = sum(dt.kubernetes.container.limits_memory)\n}, filter: {\n  k8s.cluster.name == $Cluster AND\n  k8s.namespace.name == $Namespace\n}, by: { dt.entity.cloud_application_instance, k8s.pod.name },\nfrom: -2m,\nnonempty: true,\nunion: true\n| fieldsRemove interval, timeframe\n| fieldsAdd memory_usage = arrayFirst(memory_usage)\n| fieldsAdd requests_memory = arrayFirst(requests_memory)\n| fieldsAdd limits_memory = arrayFirst(limits_memory)\n| fieldsAdd requests_memory_percent = memory_usage / requests_memory * 100\n| fieldsAdd limits_memory_percent = memory_usage / limits_memory * 100\n| fieldsAdd memory_slack = (requests_memory - memory_usage)\n| sort memory_usage desc\n| fieldsRename `Name` = k8s.pod.name, `Memory Usage` = memory_usage, `Memory Requests` = requests_memory, `Memory Requests %` = requests_memory_percent, `Memory Limits` = limits_memory, `Memory Limits %` = limits_memory_percent, `Memory Slack` = memory_slack",
            "visualization": "table",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "dt.entity.cloud_application_instance",
                        "categoryAxisLabel": "dt.entity.cloud_application_instance",
                        "valueAxis": "Memory Usage",
                        "valueAxisLabel": "Memory Usage"
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "id",
                    "prefixIcon": "",
                    "recordField": "id",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": false
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [
                        [
                            "id"
                        ],
                        [
                            "namespace.id"
                        ],
                        [
                            "namespace.labels"
                        ],
                        [
                            "namespace.annotations"
                        ],
                        [
                            "namespace.age"
                        ],
                        [
                            "cluster.id"
                        ],
                        [
                            "dt.entity.cloud_application_instance"
                        ]
                    ],
                    "lineWrapIds": [],
                    "columnWidths": {
                        "[\"limits_cpu\"]": 124.046875,
                        "[\"requests_cpu\"]": 142.453125,
                        "[\"namespace.name\"]": 231.140625,
                        "[\"cpu_usage\"]": 157.8125,
                        "[\"cpu_throttled\"]": 164.46875,
                        "[\"requests_cpu_percent\"]": 191.28125,
                        "[\"limits_cpu_percent\"]": 175.859375
                    },
                    "sortBy": {
                        "columnId": "[\"Memory Usage\"]",
                        "direction": "descending"
                    },
                    "columnTypeOverrides": []
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": "auto",
                    "dataMappings": {
                        "value": "dt.entity.cloud_application_instance"
                    },
                    "displayedFields": [
                        "dt.entity.cloud_application_instance"
                    ],
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "Memory Usage",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "Memory Requests",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "Memory Limits",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "Memory Requests %",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "Memory Limits %",
                            "rangeAxis": ""
                        },
                        {
                            "valueAxis": "Memory Slack",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": [
                        "dt.entity.cloud_application_instance",
                        "Name"
                    ]
                },
                "unitsOverrides": [
                    {
                        "identifier": "Memory Usage",
                        "unitCategory": "data",
                        "baseUnit": "byte",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715698159493
                    },
                    {
                        "identifier": "Memory Requests",
                        "unitCategory": "data",
                        "baseUnit": "byte",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715698181310
                    },
                    {
                        "identifier": "Memory Requests %",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715698189862
                    },
                    {
                        "identifier": "Memory Limits",
                        "unitCategory": "data",
                        "baseUnit": "byte",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715698204816
                    },
                    {
                        "identifier": "Memory Limits %",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715698213152
                    },
                    {
                        "identifier": "Memory Slack",
                        "unitCategory": "data",
                        "baseUnit": "byte",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1715698222294
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 100,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        },
        "30": {
            "type": "markdown",
            "content": "# OTP Service Alerts Dashboard"
        },
        "31": {
            "type": "markdown",
            "content": "# CPU Utilization"
        },
        "32": {
            "type": "markdown",
            "content": "# Memory Utilization"
        },
        "33": {
            "title": "",
            "type": "data",
            "query": "timeseries requests=sum(dt.service.request.count, rate:1m), filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd `Requests`=arrayAvg(requests)",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "Requests",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": "#0D9C29",
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                },
                                "comparator": "≥",
                                "label": ""
                            },
                            {
                                "id": 2,
                                "color": "#CD3741",
                                "comparator": "≥",
                                "label": ""
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxisLabel": "dt.entity.service.name",
                        "valueAxisLabel": "interval"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.service"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "requests"
                        ],
                        "leftAxisDimensions": [
                            "Requests"
                        ]
                    },
                    "legend": {
                        "position": "bottom",
                        "hidden": false
                    },
                    "colorPalette": "categorical"
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "Requests",
                    "prefixIcon": "",
                    "recordField": "Requests",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "background",
                    "trend": {
                        "isVisible": false,
                        "trendType": "auto"
                    },
                    "sparklineSettings": {
                        "showTicks": false,
                        "isVisible": false
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "blue",
                    "dataMappings": {
                        "value": "interval"
                    },
                    "displayedFields": [
                        null
                    ]
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "Requests",
                        "unitCategory": "unspecified",
                        "baseUnit": "none",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "/m",
                        "delimiter": false,
                        "added": 1721893726115
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "34": {
            "title": "",
            "type": "data",
            "query": "timeseries {failures=sum(dt.service.request.failure_count, default:0), requests=sum(dt.service.request.count, default:1)}, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd failureRate = failures[]/requests[]*100\n| fieldsAdd `Failures`=arrayAvg(failureRate)",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "Failures",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": "#0D9C29",
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 1
                            },
                            {
                                "id": 2,
                                "color": "#CD3741",
                                "comparator": "≥",
                                "label": "",
                                "value": 2
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxisLabel": "dt.entity.service.name",
                        "valueAxisLabel": "interval"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.service"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "failures",
                            "requests",
                            "failureRate"
                        ],
                        "leftAxisDimensions": [
                            "Failures"
                        ]
                    },
                    "legend": {
                        "position": "bottom",
                        "hidden": false
                    },
                    "colorPalette": "purple-rain"
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "Failures",
                    "prefixIcon": "",
                    "recordField": "Failures",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "background",
                    "trend": {
                        "isVisible": false,
                        "isRelative": true,
                        "trendType": "auto"
                    },
                    "sparklineSettings": {
                        "isVisible": false
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "blue",
                    "dataMappings": {
                        "value": "interval"
                    },
                    "displayedFields": [
                        null
                    ]
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "Failures",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1721893726115
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "componentState": {
                    "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                    "inputData": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "generalParameters": {
                                "timeframe": {
                                    "startTime": "now-24h",
                                    "endTime": "now"
                                },
                                "resolveDimensionalQueryData": true,
                                "logVerbosity": "INFO"
                            },
                            "numberOfSignalFluctuations": 1,
                            "alertCondition": "ABOVE",
                            "alertOnMissingData": false,
                            "violatingSamples": 3,
                            "slidingWindow": 5,
                            "dealertingSamples": 5,
                            "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                        }
                    },
                    "analyzerHints": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "unit": {
                                "unitCategory": "time",
                                "baseUnit": "microsecond"
                            }
                        }
                    }
                }
            }
        },
        "35": {
            "title": "",
            "type": "data",
            "query": "timeseries requests=percentile(dt.service.request.response_time, 90), filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd `P90`=arrayAvg(requests)",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "P90",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": "#0D9C29",
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 1000000
                            },
                            {
                                "id": 2,
                                "color": "#CD3741",
                                "comparator": "≥",
                                "label": "",
                                "value": 2000000
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxisLabel": "dt.entity.service.name",
                        "valueAxisLabel": "interval"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.service"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "requests"
                        ],
                        "leftAxisDimensions": [
                            "P90"
                        ]
                    },
                    "legend": {
                        "position": "bottom",
                        "hidden": false
                    },
                    "colorPalette": "purple-rain"
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "P90",
                    "prefixIcon": "",
                    "recordField": "P90",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "background",
                    "trend": {
                        "isVisible": false,
                        "isRelative": true,
                        "trendType": "auto"
                    },
                    "sparklineSettings": {
                        "isVisible": false
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "blue",
                    "dataMappings": {
                        "value": "interval"
                    },
                    "displayedFields": [
                        null
                    ]
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "P90",
                        "unitCategory": "time",
                        "baseUnit": "microsecond",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1721893726115
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "componentState": {
                    "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                    "inputData": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "generalParameters": {
                                "timeframe": {
                                    "startTime": "now-24h",
                                    "endTime": "now"
                                },
                                "resolveDimensionalQueryData": true,
                                "logVerbosity": "INFO"
                            },
                            "numberOfSignalFluctuations": 1,
                            "alertCondition": "ABOVE",
                            "alertOnMissingData": false,
                            "violatingSamples": 3,
                            "slidingWindow": 5,
                            "dealertingSamples": 5,
                            "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                        }
                    },
                    "analyzerHints": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "unit": {
                                "unitCategory": "time",
                                "baseUnit": "microsecond"
                            }
                        }
                    }
                }
            }
        },
        "36": {
            "title": "",
            "type": "data",
            "query": "timeseries requests=percentile(dt.service.request.response_time, 50), filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,$Namespace)\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | fields id\n] \n| filterOut contains(entity.name, \"Requests executed in background threads of \")\n| fields id\n]\n}\n| fieldsAdd `P50`=arrayAvg(requests)",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "P50",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": "#0D9C29",
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 1,
                                "color": {
                                    "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                },
                                "comparator": "≥",
                                "label": "",
                                "value": 100000
                            },
                            {
                                "id": 2,
                                "color": "#CD3741",
                                "comparator": "≥",
                                "label": "",
                                "value": 200000
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxisLabel": "dt.entity.service",
                        "valueAxisLabel": "P50"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.service"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "requests"
                        ],
                        "leftAxisDimensions": [
                            "P50"
                        ]
                    },
                    "legend": {
                        "position": "bottom",
                        "hidden": false
                    },
                    "colorPalette": "purple-rain"
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "P50",
                    "prefixIcon": "",
                    "recordField": "P50",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "background",
                    "trend": {
                        "isVisible": false,
                        "isRelative": true,
                        "trendType": "auto"
                    },
                    "sparklineSettings": {
                        "isVisible": false
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "blue",
                    "dataMappings": {
                        "value": "interval"
                    },
                    "displayedFields": [
                        null
                    ]
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "P50",
                        "unitCategory": "time",
                        "baseUnit": "microsecond",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1721893726115
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "componentState": {
                    "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                    "inputData": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "generalParameters": {
                                "timeframe": {
                                    "startTime": "now-24h",
                                    "endTime": "now"
                                },
                                "resolveDimensionalQueryData": true,
                                "logVerbosity": "INFO"
                            },
                            "numberOfSignalFluctuations": 1,
                            "alertCondition": "ABOVE",
                            "alertOnMissingData": false,
                            "violatingSamples": 3,
                            "slidingWindow": 5,
                            "dealertingSamples": 5,
                            "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                        }
                    },
                    "analyzerHints": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "unit": {
                                "unitCategory": "time",
                                "baseUnit": "microsecond"
                            }
                        }
                    }
                }
            }
        },
        "37": {
            "title": "",
            "type": "data",
            "query": "fetch dt.davis.problems\n| filter event.status==\"ACTIVE\" and dt.davis.is_duplicate==false\n| fieldsAdd severity=if(\n  event.category==\"AVAILABILITY\", 1, else: if(\n  event.category==\"ERROR\", 2, else: if(\n  event.category==\"SLOWDOWN\", 3, else: if(\n  event.category==\"RESOURCE_CONTENTION\", 4, else: if(\n  event.category==\"CUSTOM_ALERT\", 2)))))\n| sort severity, display_id\n| expand affected_entity_ids\n| filter (in(k8s.cluster.name, $Cluster) and in(k8s.namespace.name, $Namespace)) or affected_entity_ids in [\nfetch dt.entity.process_group_instance\n| filter belongs_to[dt.entity.container_group_instance] in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fields id\n| append [\nfetch dt.entity.service\n| filter belongs_to[dt.entity.cloud_application][0] in [\n  fetch dt.entity.cloud_application\n  | filter clustered_by[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fields id\n]\n]\n| dedup display_id\n| fieldsRename Start=event.start, ID=display_id, Severity=event.category, Name = event.name, Cluster=k8s.cluster.name, Namespace=k8s.namespace.name, Workload=k8s.workload.name\n| summarize count(), min(severity)",
            "visualization": "singleValue",
            "visualizationSettings": {
                "thresholds": [
                    {
                        "id": 1,
                        "field": "count()",
                        "title": "",
                        "isEnabled": true,
                        "rules": [
                            {
                                "id": 0,
                                "color": "#0D9C29",
                                "comparator": "≥",
                                "label": "",
                                "value": 0
                            },
                            {
                                "id": 2,
                                "color": "#CD3741",
                                "comparator": "≥",
                                "label": "",
                                "value": 1
                            }
                        ]
                    }
                ],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxisLabel": "ID",
                        "valueAxisLabel": "severity"
                    },
                    "hiddenLegendFields": []
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "Problems",
                    "recordField": "count()",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "background",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [
                        [
                            "severity"
                        ],
                        [
                            "affected_entity_ids"
                        ],
                        [
                            "affected_entity_types"
                        ],
                        [
                            "dt.davis.event_ids"
                        ],
                        [
                            "dt.davis.is_duplicate"
                        ],
                        [
                            "dt.davis.is_frequent_event"
                        ],
                        [
                            "dt.davis.mute.status"
                        ],
                        [
                            "event.id"
                        ],
                        [
                            "event.kind"
                        ],
                        [
                            "event.status"
                        ],
                        [
                            "event.status_transition"
                        ],
                        [
                            "labels.alerting_profile"
                        ],
                        [
                            "maintenance.is_under_maintenance"
                        ],
                        [
                            "event.start"
                        ],
                        [
                            "timestamp"
                        ]
                    ],
                    "lineWrapIds": [],
                    "columnWidths": {
                        "[\"Severity\"]": 350.21875
                    },
                    "colorThresholdTarget": "background"
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "blue",
                    "dataMappings": {
                        "value": "count()"
                    },
                    "displayedFields": [
                        null
                    ]
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "count()",
                            "rangeAxis": ""
                        }
                    ],
                    "variant": "single",
                    "displayedFields": []
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            },
            "timeframe": {
                "tileTimeframe": {
                    "from": "now()-2h",
                    "to": "now()"
                },
                "tileTimeframeEnabled": false
            }
        },
        "38": {
            "title": "🚩 Failure Rate",
            "type": "data",
            "query": "timeseries {failures=sum(dt.service.request.failure_count, default:0), requests=sum(dt.service.request.count, default:1)}, by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application][0] in [\n    fetch dt.entity.cloud_application\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n      fetch dt.entity.kubernetes_cluster\n      | filter in(entity.name, $Cluster)\n      | fields id\n    ]\n    | filter in(namespaceName, $Namespace)\n    | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service),failureRate = failures[]/requests[]*100\n| filterOut contains(dt.entity.service.name, \"Requests executed in background threads of \")\n| fieldsRemove failures, requests\n| sort arrayLast(failureRate) desc\n| limit 20",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "dt.entity.service.name",
                        "categoryAxisLabel": "dt.entity.service.name",
                        "valueAxis": "interval",
                        "valueAxisLabel": "interval"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.service"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "failureRate"
                        ],
                        "leftAxisDimensions": [
                            "dt.entity.service",
                            "dt.entity.service.name"
                        ]
                    },
                    "legend": {
                        "hidden": true
                    },
                    "colorPalette": "fireplace",
                    "leftYAxisSettings": {
                        "max": "data-max",
                        "min": 0
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.entity.service",
                    "prefixIcon": "",
                    "recordField": "dt.entity.service",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "blue",
                    "dataMappings": {
                        "value": "interval"
                    },
                    "displayedFields": [
                        "dt.entity.service"
                    ]
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "failureRate",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1721893726115
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "39": {
            "title": "⏱️ Response Time",
            "type": "data",
            "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application][0] in [\n    fetch dt.entity.cloud_application\n    | filter clustered_by[dt.entity.kubernetes_cluster] in [\n      fetch dt.entity.kubernetes_cluster\n      | filter in(entity.name, $Cluster)\n      | fields id\n    ]\n    | filter in(namespaceName, $Namespace)\n    | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| filterOut contains(dt.entity.service.name, \"Requests executed in background threads of \")\n| sort arrayAvg(requests) desc\n| limit 20",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "categoricalBarChartSettings": {
                        "categoryAxis": "dt.entity.service.name",
                        "categoryAxisLabel": "dt.entity.service.name",
                        "valueAxis": "interval",
                        "valueAxisLabel": "interval"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.service"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "requests"
                        ],
                        "leftAxisDimensions": [
                            "dt.entity.service",
                            "dt.entity.service.name"
                        ]
                    },
                    "legend": {
                        "hidden": true
                    },
                    "colorPalette": "purple-rain"
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "dt.entity.service",
                    "prefixIcon": "",
                    "recordField": "dt.entity.service",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value",
                    "trend": {
                        "trendType": "auto",
                        "isVisible": true
                    }
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "blue",
                    "dataMappings": {
                        "value": "interval"
                    },
                    "displayedFields": [
                        "dt.entity.service"
                    ]
                },
                "histogram": {
                    "dataMappings": [],
                    "variant": "single",
                    "displayedFields": []
                },
                "unitsOverrides": [
                    {
                        "identifier": "failureRate",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1721893726115
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "componentState": {
                    "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer",
                    "inputData": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "generalParameters": {
                                "timeframe": {
                                    "startTime": "now-24h",
                                    "endTime": "now"
                                },
                                "resolveDimensionalQueryData": true,
                                "logVerbosity": "INFO"
                            },
                            "numberOfSignalFluctuations": 1,
                            "alertCondition": "ABOVE",
                            "alertOnMissingData": false,
                            "violatingSamples": 3,
                            "slidingWindow": 5,
                            "dealertingSamples": 5,
                            "query": "timeseries requests=avg(dt.service.request.response_time), by: { dt.entity.service }, filter:{\n  dt.entity.service in [fetch dt.entity.service \n  |filter belongs_to[dt.entity.cloud_application_namespace][0] in [\n  fetch dt.entity.cloud_application_namespace\n  | filter in(entity.name,\"google-trip-id-service\")\n  | fields id\n] | fields id\n]\n}\n| fieldsAdd dt.entity.service.name = entityName(dt.entity.service)\n| sort arrayAvg(requests) desc\n| limit 20"
                        }
                    },
                    "analyzerHints": {
                        "dt.statistics.ui.anomaly_detection.AutoAdaptiveAnomalyDetectionAnalyzer": {
                            "unit": {
                                "unitCategory": "time",
                                "baseUnit": "microsecond"
                            }
                        }
                    }
                }
            }
        },
        "40": {
            "title": "JVM Memory",
            "type": "data",
            "query": "timeseries free=avg(dt.runtime.jvm.memory.free),total=avg(dt.runtime.jvm.memory.total), by: { dt.entity.process_group_instance}, filter: dt.entity.container_group_instance in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fieldsAdd dt.entity.process_group_instance.name = entityName(dt.entity.process_group_instance)\n| fieldsAdd `Memory used %`=100-free[]/total[]*100\n| fieldsRemove free, total\n| sort arrayAvg(`Memory used %`) desc\n| limit 20",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "Memory used %"
                        ],
                        "leftAxisDimensions": [
                            "dt.entity.process_group_instance",
                            "dt.entity.process_group_instance.name"
                        ]
                    },
                    "categoricalBarChartSettings": {
                        "categoryAxis": "dt.entity.process_group_instance.name",
                        "valueAxis": "interval",
                        "categoryAxisLabel": "dt.entity.process_group_instance.name",
                        "valueAxisLabel": "interval"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.process_group_instance"
                    ],
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "",
                    "prefixIcon": "",
                    "recordField": "dt.entity.process_group_instance",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "categorical",
                    "dataMappings": {
                        "value": "dt.entity.process_group_instance"
                    },
                    "displayedFields": [
                        "dt.entity.process_group_instance"
                    ]
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ]
                },
                "unitsOverrides": [
                    {
                        "identifier": "Memory used %",
                        "unitCategory": "percentage",
                        "baseUnit": "percent",
                        "displayUnit": null,
                        "decimals": 2,
                        "suffix": "",
                        "delimiter": false,
                        "added": 1721990934091
                    }
                ]
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "41": {
            "title": "Garbage Collection Time",
            "type": "data",
            "query": "timeseries `Garbage Collection Time`=avg(dt.runtime.jvm.gc.collection_time), by: { dt.entity.process_group_instance }, filter: dt.entity.container_group_instance in [\n  fetch dt.entity.container_group_instance\n  | filter belongs_to[dt.entity.kubernetes_cluster] in [\n    fetch dt.entity.kubernetes_cluster\n    | filter in(entity.name, $Cluster)\n    | fields id\n  ]\n  | filter in(namespaceName, $Namespace)\n  | fields id\n]\n| fieldsAdd dt.entity.process_group_instance.name = entityName(dt.entity.process_group_instance)\n| sort arrayAvg(`Garbage Collection Time`) desc\n| limit 20",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "connect",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "Garbage Collection Time"
                        ],
                        "leftAxisDimensions": [
                            "dt.entity.process_group_instance",
                            "dt.entity.process_group_instance.name"
                        ]
                    },
                    "categoricalBarChartSettings": {
                        "categoryAxis": "dt.entity.process_group_instance.name",
                        "valueAxis": "interval",
                        "categoryAxisLabel": "dt.entity.process_group_instance.name",
                        "valueAxisLabel": "interval"
                    },
                    "hiddenLegendFields": [
                        "dt.entity.process_group_instance"
                    ],
                    "legend": {
                        "hidden": true
                    }
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "",
                    "prefixIcon": "",
                    "recordField": "dt.entity.process_group_instance",
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "lineWrapIds": [],
                    "columnWidths": {}
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto"
                    },
                    "colorMode": "color-palette",
                    "colorPalette": "categorical",
                    "dataMappings": {
                        "value": "dt.entity.process_group_instance"
                    },
                    "displayedFields": [
                        "dt.entity.process_group_instance"
                    ]
                },
                "histogram": {
                    "dataMappings": [
                        {
                            "valueAxis": "interval",
                            "rangeAxis": ""
                        }
                    ]
                }
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {}
        },
        "42": {
            "title": "",
            "type": "data",
            "query": "timeseries verify = sum(otp_service_verify_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: { exception == \"none\" }\n| summarize {\n  verify_count = sum(verify[])\n},\nby: { timeframe, interval, provider }\n| join [\ntimeseries generate = sum(otp_service_generate_requests_total, rate:1m),\n  by: {provider},\n  interval:1m,\n  filter: {exception == \"none\" }\n| summarize {\n  generate_count = sum(generate[])\n},\nby: { timeframe, interval, provider }\n],\nkind: inner,\non: { timeframe, interval, provider }\n// | filter provider == \"MittoSmsProvider\"\n// | filter provider == \"InfoBipProvider\"\n//         OR provider == \"EtisalatProvider\"\n//         OR provider == \"MessageBirdProvider\"\n//         OR provider == \"MittoSmsProvider\"\n//         OR provider == \"MittoWhatsappProvider\"\n//         OR provider == \"PhoenixProvider\"\n| fieldsAdd verify_15m   = arrayMovingSum(verify_count, 15),\n            generate_15m = arrayMovingSum(right.generate_count, 15)\n| fieldsAdd verify_generate_ratio_15m = (verify_15m[] / generate_15m[]) * 100\n| fieldsAdd entity.name = \"otp-service\"\n| fieldsAdd event_name = \"OTP Ratio verify/Gen\"\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields timeframe, interval, provider, event_name,verify_generate_ratio_15m",
            "visualization": "lineChart",
            "visualizationSettings": {
                "thresholds": [],
                "chartSettings": {
                    "gapPolicy": "gap",
                    "circleChartSettings": {
                        "groupingThresholdType": "relative",
                        "groupingThresholdValue": 0,
                        "valueType": "relative"
                    },
                    "categoryOverrides": {},
                    "curve": "linear",
                    "pointsDisplay": "auto",
                    "categoricalBarChartSettings": {
                        "layout": "horizontal",
                        "categoryAxisTickLayout": "horizontal",
                        "scale": "absolute",
                        "groupMode": "stacked",
                        "colorPaletteMode": "multi-color",
                        "valueAxisScale": "linear"
                    },
                    "colorPalette": "categorical",
                    "valueRepresentation": "absolute",
                    "truncationMode": "middle",
                    "xAxisScaling": "analyzedTimeframe",
                    "xAxisLabel": "timeframe",
                    "xAxisIsLabelVisible": false,
                    "hiddenLegendFields": [
                        "interval",
                        "verify_generate_ratio_15m"
                    ],
                    "fieldMapping": {
                        "timestamp": "timeframe",
                        "leftAxisValues": [
                            "verify_generate_ratio_15m"
                        ]
                    },
                    "leftYAxisSettings": {}
                },
                "singleValue": {
                    "showLabel": true,
                    "label": "",
                    "prefixIcon": "AnalyticsIcon",
                    "isIconVisible": false,
                    "autoscale": true,
                    "alignment": "center",
                    "colorThresholdTarget": "value"
                },
                "table": {
                    "rowDensity": "condensed",
                    "enableSparklines": false,
                    "hiddenColumns": [],
                    "linewrapEnabled": false,
                    "lineWrapIds": [],
                    "monospacedFontEnabled": false,
                    "monospacedFontColumns": [],
                    "columnWidths": {},
                    "columnTypeOverrides": [
                        {
                            "fields": [
                                "verify_generate_ratio_15m"
                            ],
                            "value": "sparkline",
                            "id": 1747655950332
                        }
                    ]
                },
                "honeycomb": {
                    "shape": "hexagon",
                    "legend": {
                        "hidden": false,
                        "position": "auto",
                        "ratio": "auto"
                    },
                    "dataMappings": {},
                    "displayedFields": [],
                    "truncationMode": "middle",
                    "colorMode": "color-palette",
                    "colorPalette": "categorical"
                },
                "histogram": {
                    "legend": {
                        "position": "auto"
                    },
                    "yAxis": {
                        "label": "Frequency",
                        "isLabelVisible": true,
                        "scale": "linear"
                    },
                    "colorPalette": "categorical",
                    "dataMappings": [],
                    "variant": "single",
                    "truncationMode": "middle"
                },
                "valueBoundaries": {
                    "min": "auto",
                    "max": "auto"
                },
                "autoSelectVisualization": true
            },
            "querySettings": {
                "maxResultRecords": 1000,
                "defaultScanLimitGbytes": 500,
                "maxResultMegaBytes": 1,
                "defaultSamplingRatio": 10,
                "enableSampling": false
            },
            "davis": {
                "enabled": false,
                "davisVisualization": {
                    "isAvailable": true
                }
            }
        }
    },
    "layouts": {
        "2": {
            "x": 0,
            "y": 49,
            "w": 12,
            "h": 5
        },
        "4": {
            "x": 0,
            "y": 59,
            "w": 24,
            "h": 1
        },
        "5": {
            "x": 0,
            "y": 60,
            "w": 24,
            "h": 8
        },
        "6": {
            "x": 0,
            "y": 68,
            "w": 24,
            "h": 8
        },
        "7": {
            "x": 0,
            "y": 76,
            "w": 24,
            "h": 8
        },
        "8": {
            "x": 0,
            "y": 84,
            "w": 24,
            "h": 8
        },
        "11": {
            "x": 0,
            "y": 44,
            "w": 12,
            "h": 5
        },
        "12": {
            "x": 12,
            "y": 49,
            "w": 12,
            "h": 5
        },
        "13": {
            "x": 12,
            "y": 54,
            "w": 12,
            "h": 5
        },
        "14": {
            "x": 0,
            "y": 54,
            "w": 12,
            "h": 5
        },
        "15": {
            "x": 12,
            "y": 44,
            "w": 12,
            "h": 5
        },
        "16": {
            "x": 0,
            "y": 0,
            "w": 24,
            "h": 1
        },
        "17": {
            "x": 0,
            "y": 5,
            "w": 24,
            "h": 9
        },
        "18": {
            "x": 0,
            "y": 14,
            "w": 24,
            "h": 1
        },
        "19": {
            "x": 0,
            "y": 17,
            "w": 6,
            "h": 2
        },
        "20": {
            "x": 6,
            "y": 17,
            "w": 6,
            "h": 2
        },
        "21": {
            "x": 12,
            "y": 17,
            "w": 6,
            "h": 2
        },
        "22": {
            "x": 18,
            "y": 15,
            "w": 6,
            "h": 4
        },
        "23": {
            "x": 0,
            "y": 15,
            "w": 6,
            "h": 2
        },
        "24": {
            "x": 6,
            "y": 15,
            "w": 6,
            "h": 2
        },
        "25": {
            "x": 12,
            "y": 15,
            "w": 6,
            "h": 2
        },
        "26": {
            "x": 0,
            "y": 20,
            "w": 12,
            "h": 6
        },
        "27": {
            "x": 0,
            "y": 26,
            "w": 12,
            "h": 5
        },
        "28": {
            "x": 12,
            "y": 20,
            "w": 12,
            "h": 6
        },
        "29": {
            "x": 12,
            "y": 26,
            "w": 12,
            "h": 5
        },
        "30": {
            "x": 0,
            "y": 43,
            "w": 24,
            "h": 1
        },
        "31": {
            "x": 0,
            "y": 19,
            "w": 12,
            "h": 1
        },
        "32": {
            "x": 12,
            "y": 19,
            "w": 12,
            "h": 1
        },
        "33": {
            "x": 14,
            "y": 1,
            "w": 5,
            "h": 4
        },
        "34": {
            "x": 19,
            "y": 1,
            "w": 5,
            "h": 4
        },
        "35": {
            "x": 9,
            "y": 1,
            "w": 5,
            "h": 4
        },
        "36": {
            "x": 4,
            "y": 1,
            "w": 5,
            "h": 4
        },
        "37": {
            "x": 0,
            "y": 1,
            "w": 4,
            "h": 4
        },
        "38": {
            "x": 0,
            "y": 37,
            "w": 12,
            "h": 6
        },
        "39": {
            "x": 0,
            "y": 31,
            "w": 12,
            "h": 6
        },
        "40": {
            "x": 12,
            "y": 37,
            "w": 12,
            "h": 6
        },
        "41": {
            "x": 12,
            "y": 31,
            "w": 12,
            "h": 6
        },
        "42": {
            "x": 0,
            "y": 92,
            "w": 24,
            "h": 7
        }
    },
    "importedWithCode": false,
    "settings": {
        "defaultTimeframe": {
            "value": {
                "from": "now()-2h",
                "to": "now()"
            },
            "enabled": false
        },
        "defaultSegments": {
            "value": [],
            "enabled": false
        }
    }}