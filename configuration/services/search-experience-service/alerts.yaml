---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: search-eta-service-anomaly-detection
  labels:
    scope: global
    service: search-experience-service
spec:
  anomalies:
    staticThreshold:
      search-experience-service-eta-requests:
        title: "[search-experience-service] search ETA chunked requests (ms)"
        description: "Alert on high chunked requests time"
        alertCondition: "ABOVE"
        threshold: 50
        query: |
          timeseries eta = avg(search_experience_service_search_eta_service_chunked_requests_time), by:{k8s.namespace.name}
          | fieldsAdd eta_ms = eta[] * 100, entity.name = k8s.namespace.name
          | join [fetch dt.entity.service], prefix:"service.", on:{entity.name}
          | fields interval, timeframe, eta_ms, service.id, service.entity.name
        entityIdProperty: "service.id"
        entityNameProperty: "service.entity.name"
        eventType: "PERFORMANCE_EVENT"
        eventName: "has high chunked requests time"
