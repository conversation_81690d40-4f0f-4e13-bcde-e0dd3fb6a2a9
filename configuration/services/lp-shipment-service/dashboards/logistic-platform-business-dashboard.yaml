---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: logistic-platform-business-dashboard
  labels:
    scope: global
spec:
  name: "[Logistic-platform] Custom Dashboard"
  json: |
    {
      "version": 18,
      "variables": [
        {
          "key": "CityName",
          "visible": true,
          "type": "query",
          "version": 1,
          "editable": true,
          "input": "timeseries test = sum(logistics_platform_planner_job_plan_total),\nby: {cityname}\n| fields cityname",
          "multiple": false,
          "defaultValue": "Dubai"
        },
        {
          "key": "ProductId",
          "visible": true,
          "type": "query",
          "version": 1,
          "editable": true,
          "input": "timeseries test = sum(logistics_platform_shipment_created_successfully_total),\nby: {productid}\n| fields productid",
          "multiple": false,
          "defaultValue": "FOOD"
        }
      ],
      "tiles": {
        "1": {
          "title": "Number of Tasks",
          "description": "The number of tasks over time grouped by their type such as CREATED, UPDATED",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { sum(logistics_platform_planner_task_total), value.A = avg(logistics_platform_planner_task_total, scalar: true) }, by: { eventtype }, filter: { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A",
                "sum(logistics_platform_planner_task_total)"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "sum(logistics_platform_planner_task_total)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "total tasks created"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "sum(logistics_platform_planner_task_total)"
                  ],
                  "value": "sparkline",
                  "id": 1745790739222
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "logistics_platform_planner_task_total",
                  "aggregation": "sum"
                },
                "by": [
                  "eventtype"
                ],
                "filter": "cityname = $CityName AND productid = $ProductId "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "8": {
          "title": "Total Tasks Created",
          "description": "The total number of tasks Created within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname, eventtype}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventtype, \"CREATED\") }\n| fieldsAdd total_tasks=arraysum(result)\n| fields total_tasks ",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "total_tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "total_tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "9": {
          "title": "Total Tasks Updated",
          "description": "The total number of tasks updated within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname, eventtype}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventtype, \"UPDATED\") }\n| fieldsAdd total_tasks=arraysum(result)\n| fields total_tasks ",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "total_tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "total_tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "10": {
          "title": "Total Tasks Cancelled",
          "description": "The total number of tasks cancelled within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname, eventtype}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventtype, \"CANCELLED\") }\n| fieldsAdd total_tasks=arraysum(result)\n| fields total_tasks ",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "total_tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "total_tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "11": {
          "title": "Total Tasks Failed",
          "description": "The total number of tasks that failed creation within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname, eventtype}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventtype, \"CREATION_FAILED\") }\n| fieldsAdd total_tasks=arraysum(result)\n| fields total_tasks ",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "total_tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "total_tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ],
            "colorModeType": {
              "customNumericColors": [
                {
                  "id": 633328.899999976,
                  "value": null,
                  "color": {
                    "Default": "var(--dt-colors-charts-categorical-color-01-default, #134fc9)"
                  },
                  "comparator": "≥"
                }
              ]
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "12": {
          "title": "Total Tasks Update Failed",
          "description": "The total number of tasks that failed update within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname, eventtype}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventtype, \"UPDATE_FAILED\")}\n| fieldsAdd total_tasks=arraysum(result)\n| fields total_tasks ",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "total_tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "total_tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "13": {
          "title": "Total Tasks Cancellation Failed",
          "description": "The total number of tasks that failed cancellation within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname, eventtype}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventtype, \"CANCELLATION_FAILED\")}\n| fieldsAdd total_tasks=arraysum(result)\n| fields total_tasks ",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "total_tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "total_tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "14": {
          "type": "markdown",
          "content": "# Planner Metrics"
        },
        "15": {
          "type": "markdown",
          "content": "# Shipment Metrics"
        },
        "17": {
          "title": "Total Shipment Creation Requests",
          "description": "The total number of shipment creation requests over the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_shipment_creation_request_total), interval:1m, by: {productid, cityname,isscheduled}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(isscheduled, \"False\")}\n| fieldsAdd shipment=arraysum(result)\n| fields shipment",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "shipment"
            },
            "label": {
              "showLabel": true,
              "label": "shipments"
            },
            "unitsOverrides": [
              {
                "identifier": "shipment",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "19": {
          "title": "Total Shipments Created",
          "description": "The total number of shipments created over the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_shipment_created_successfully_total), interval:1m, by: {productid, cityname,isscheduled}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(isscheduled, \"False\")}\n| fieldsAdd shipment=arraysum(result)\n| fields shipment",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "shipment"
            },
            "label": {
              "showLabel": true,
              "label": "shipments"
            },
            "unitsOverrides": [
              {
                "identifier": "shipment",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "20": {
          "title": "package status updates",
          "description": "The number of package status updates grouped by the update type",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { sum(logistics_platform_package_status_update_total), value.A = avg(logistics_platform_package_status_update_total, scalar: true) }, by: { status }, filter: { matchesValue(productid, $ProductId) AND matchesValue(cityname, $CityName) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A",
                "sum(logistics_platform_shipment_creation_request_total)"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "sum(logistics_platform_package_status_update_total)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_shipment_creation_request_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "sum(logistics_platform_package_status_update_total)"
                  ],
                  "value": "sparkline",
                  "id": 1745919382943
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "logistics_platform_package_status_update_total",
                  "aggregation": "sum"
                },
                "by": [
                  "status"
                ],
                "filter": "productid = $ProductId AND cityname = $CityName "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "21": {
          "title": "Total delivered packages",
          "description": "The total number of packages delivered of shipments",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_package_status_update_total), interval:1m, by: {productid, cityname,status}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(status, \"DELIVERED\")}\n| fieldsAdd packages=arraysum(result)\n| fields packages",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "packages"
            },
            "label": {
              "showLabel": true,
              "label": "packages"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "22": {
          "type": "markdown",
          "content": "# Pooling Metrics"
        },
        "25": {
          "title": "Pooling Percentage",
          "description": "The pooling percentage over the selected time range",
          "type": "data",
          "query": "timeseries plan_increase = sum(logistics_platform_planner_job_plan_total), interval:1m, by: {productid, cityname, taskcount}\n| filter {\n    matchesValue(cityname, $CityName) AND \n    matchesValue(productid, $ProductId) AND \n    NOT matchesValue(taskcount, \"1\")\n}\n| join [\n  timeseries tasks_total = sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname}\n  | filter {\n      matchesValue(cityname, $CityName) AND \n      matchesValue(productid, $ProductId)\n  }\n], kind: leftOuter, on: {timeframe, productid, cityname}, prefix: \"task.\"\n\n| fieldsAdd plans = (arraySum(plan_increase) * 2 * 100) / arraySum(task.tasks_total)\n| fields plans\n",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "plans"
            },
            "label": {
              "showLabel": true,
              "label": "percent"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "26": {
          "title": "Total tasks replanned",
          "description": "The total number of tasks that were replanned due to unassignment within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_replanning_total), interval:1m, by: {productid, cityname,issuccessful}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(issuccessful, \"true\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "27": {
          "title": "Total tasks replanning Ineligible/Failed",
          "description": "The total number of tasks that failed replanning within the selected time range. Failed replanning could indicate that the task is not eligible for replanning, or an error occurred in the system during the replanning process",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_planner_replanning_total), interval:1m, by: {productid, cityname,issuccessful}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(issuccessful, \"false\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "28": {
          "title": "Total Number of Pooled Plans",
          "description": "The total number of pooled plans in the selected time range",
          "type": "data",
          "query": "timeseries result = sum(logistics_platform_planner_job_plan_total), interval:1m, by: {productid, cityname, taskcount}\n| filter { \n    matchesValue(cityname, $CityName) AND \n    matchesValue(productid, $ProductId) AND \n    NOT matchesValue(taskcount, \"1\")\n}\n| fieldsAdd plans = arraySum(result)\n| fields plans\n",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "plans"
            },
            "label": {
              "showLabel": true,
              "label": "Plans"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "29": {
          "title": "Eligible Pooling Percentage",
          "description": "The pooling percentage of eligible orders over the selected time range.",
          "type": "data",
          "query": "timeseries plan_increase = sum(logistics_platform_planner_job_plan_total), interval:1m, by: {productid, cityname, taskcount}\n| filter {\n    matchesValue(cityname, $CityName) AND \n    matchesValue(productid, $ProductId) AND \n    NOT matchesValue(taskcount, \"1\")\n}\n| join [\n  timeseries tasks_total = sum(logistics_platform_planner_task_total), interval:1m, by: {productid, cityname,poolingtype}\n  | filter {\n      matchesValue(cityname, $CityName) AND \n      matchesValue(productid, $ProductId) AND\n      NOT matchesValue(poolingtype, \"none\")\n  }\n], kind: leftOuter, on: {timeframe, productid, cityname}, prefix: \"task.\"\n\n| fieldsAdd plans = (arraySum(plan_increase) * 2 * 100) / arraySum(task.tasks_total)\n| fields plans\n",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "plans"
            },
            "label": {
              "showLabel": true,
              "label": "percent"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "30": {
          "type": "markdown",
          "content": "# Adapter Metrics"
        },
        "31": {
          "title": "Number of plans",
          "description": "The number of plans grouped by event name such as CREATED, CREATION_FAILED",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { sum(logistics_platform_adapter_plan_total), value.A = avg(logistics_platform_adapter_plan_total, scalar: true) }, by: { eventname }, filter: { matchesValue(productid, $ProductId) AND matchesValue(cityname, $CityName) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A",
                "sum(logistics_platform_adapter_plan_total)"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "sum(logistics_platform_adapter_plan_total)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_adapter_plan_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "sum(logistics_platform_adapter_plan_total)"
                  ],
                  "value": "sparkline",
                  "id": 1745923713889
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "logistics_platform_adapter_plan_total",
                  "aggregation": "sum"
                },
                "by": [
                  "eventname"
                ],
                "filter": "productid = $ProductId AND cityname = $CityName "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "32": {
          "title": "Number of tasks",
          "description": "The number of tasks over time grouped by the type such as CREATED, UPDATED",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { sum(logistics_platform_adapter_task_total), value.A = avg(logistics_platform_adapter_task_total, scalar: true) }, by: { eventname }, filter: { matchesValue(productid, $ProductId) AND matchesValue(cityname, $CityName) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A",
                "sum(logistics_platform_adapter_plan_total)"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "sum(logistics_platform_adapter_task_total)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_adapter_plan_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "sum(logistics_platform_adapter_task_total)"
                  ],
                  "value": "sparkline",
                  "id": 1745923792232
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "logistics_platform_adapter_task_total",
                  "aggregation": "sum"
                },
                "by": [
                  "eventname"
                ],
                "filter": "productid = $ProductId AND cityname = $CityName "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "33": {
          "title": "Total plans created",
          "description": "The total  number of created plans in adapter service within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_plan_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CREATED\")}\n| fieldsAdd plans=arraysum(result)\n| fields plans",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "plans"
            },
            "label": {
              "showLabel": true,
              "label": "plans"
            },
            "unitsOverrides": [
              {
                "identifier": "plans",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "34": {
          "title": "Total plans failed",
          "description": "The total  number of plans that failed creation in adapter service within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_plan_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CREATION_FAILED\")}\n| fieldsAdd plans=arraysum(result)\n| fields plans",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "plans"
            },
            "label": {
              "showLabel": true,
              "label": "plans"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "35": {
          "title": "Total tasks created",
          "description": "The total number of tasks created within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_task_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CREATED\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "36": {
          "title": "Total tasks updated",
          "description": "The total number of tasks updated within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_task_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"UPDATED\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "37": {
          "title": "Total tasks cancelled",
          "description": "The total number of tasks cancelled within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_task_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CANCELLED\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "tasks",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "38": {
          "title": "Total tasks failed",
          "description": "The total number of tasks that failed creation within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_task_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CREATION_FAILED\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "39": {
          "title": "Total tasks update failed",
          "description": "The total number of tasks that failed update within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_task_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"UPDATE_FAILED\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "40": {
          "title": "Total tasks cancellation failed",
          "description": "The total number of tasks that failed cancellation within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_task_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CANCELLATION_FAILED\")}\n| fieldsAdd tasks=arraysum(result)\n| fields tasks",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "tasks"
            },
            "label": {
              "showLabel": true,
              "label": "tasks"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "41": {
          "title": "Number of bookings",
          "description": "The number of bookings over time grouped by the type such as CREATED, CREATION_FAILED",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { sum(logistics_platform_adapter_booking_total), value.A = avg(logistics_platform_adapter_booking_total, scalar: true) }, by: { eventname }, filter: { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A",
                "sum(logistics_platform_adapter_booking_total)"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "sum(logistics_platform_adapter_booking_total)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_adapter_booking_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "sum(logistics_platform_adapter_booking_total)"
                  ],
                  "value": "sparkline",
                  "id": 1745958334881
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "logistics_platform_adapter_booking_total",
                  "aggregation": "sum"
                },
                "by": [
                  "eventname"
                ],
                "filter": "cityname = $CityName AND productid = $ProductId "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "42": {
          "title": "Total bookings created",
          "description": "The total number of bookings created within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_booking_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CREATED\")}\n| fieldsAdd bookings=arraysum(result)\n| fields bookings",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "bookings"
            },
            "label": {
              "showLabel": true,
              "label": "bookings"
            },
            "unitsOverrides": [
              {
                "identifier": "bookings",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "43": {
          "title": "Total bookings updated",
          "description": "The total number of bookings updated within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_booking_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"UPDATED\")}\n| fieldsAdd bookings=arraysum(result)\n| fields bookings",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "bookings"
            },
            "label": {
              "showLabel": true,
              "label": "bookings"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "44": {
          "title": "Total bookings cancelled",
          "description": "The total number of bookings cancelled within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_booking_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CANCELLED\")}\n| fieldsAdd bookings=arraysum(result)\n| fields bookings",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "bookings"
            },
            "label": {
              "showLabel": true,
              "label": "bookings"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "45": {
          "title": "Total bookings failed",
          "description": "The total number of bookings that failed creation within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_booking_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CREATION_FAILED\")}\n| fieldsAdd bookings=arraysum(result)\n| fields bookings",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "bookings"
            },
            "label": {
              "showLabel": true,
              "label": "bookings"
            },
            "unitsOverrides": [
              {
                "identifier": "bookings",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "46": {
          "title": "Total bookings update failed",
          "description": "The total number of bookings that failed to update within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_booking_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"UPDATE_FAILED\")}\n| fieldsAdd bookings=arraysum(result)\n| fields bookings",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "bookings"
            },
            "label": {
              "showLabel": true,
              "label": "bookings"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "47": {
          "title": "Total bookings cancellation failed",
          "description": "The total number of bookings that failed cancellation within the selected time range",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_booking_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"CANCELLATION_FAILED\")}\n| fieldsAdd bookings=arraysum(result)\n| fields bookings",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "bookings"
            },
            "label": {
              "showLabel": true,
              "label": "bookings"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "48": {
          "title": "Number of dispatches",
          "description": "The number of dispatches over time grouped by the type such as SUCCESS or FAILURE",
          "type": "data",
          "subType": "dql-builder-metrics",
          "query": "timeseries { sum(logistics_platform_adapter_dispatch_total), value.A = avg(logistics_platform_adapter_dispatch_total, scalar: true) }, by: { eventname }, filter: { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) }",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval",
                "value.A",
                "sum(logistics_platform_adapter_booking_total)"
              ],
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "sum(logistics_platform_adapter_dispatch_total)"
                ]
              },
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_adapter_booking_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "sum(logistics_platform_adapter_dispatch_total)"
                  ],
                  "value": "sparkline",
                  "id": 1746015911076
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "queryConfig": {
            "version": "13.5.2",
            "subQueries": [
              {
                "id": "A",
                "isEnabled": true,
                "datatype": "metrics",
                "metric": {
                  "key": "logistics_platform_adapter_dispatch_total",
                  "aggregation": "sum"
                },
                "by": [
                  "eventname"
                ],
                "filter": "cityname = $CityName AND productid = $ProductId "
              }
            ]
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "49": {
          "title": "Total Successful Dispatches",
          "description": "The total number of successful dispatch requests sent to the batching service",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_dispatch_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"SUCCESS\")}\n| fieldsAdd dispatches=arraysum(result)\n| fields dispatches",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "dispatches"
            },
            "label": {
              "showLabel": true,
              "label": "dispatches"
            },
            "unitsOverrides": [
              {
                "identifier": "dispatches",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 1,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "50": {
          "title": "Total Failed Dispatches",
          "description": "The total number of failed dispatch requests sent to the batching service",
          "type": "data",
          "query": "timeseries result =  sum(logistics_platform_adapter_dispatch_total), interval:1m, by: {productid, cityname,eventname}\n| filter { matchesValue(cityname, $CityName) AND matchesValue(productid, $ProductId) AND matchesValue(eventname, \"FAILURE\")}\n| fieldsAdd dispatches=arraysum(result)\n| fields dispatches",
          "visualization": "gauge",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxisLabel": "interval",
                "valueAxisLabel": "interval",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_task_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "sum_5m",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "recordField": "sum_5m",
              "autoscale": true,
              "sparklineSettings": {
                "record": "sum_5m"
              },
              "alignment": "center",
              "trend": {
                "trendType": "auto",
                "isVisible": true
              },
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "dispatches"
            },
            "label": {
              "showLabel": true,
              "label": "dispatches"
            },
            "unitsOverrides": [
              {
                "identifier": "packages",
                "unitCategory": "unspecified",
                "baseUnit": "count",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1745838471756
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "51": {
          "title": "Shipment Creation Requests",
          "description": "The number of shipment creation requests over time",
          "type": "data",
          "query": "timeseries shipments = sum(logistics_platform_shipment_creation_request_total, rate:5m), \nby: { isscheduled },\nfilter: {\n  matchesValue(productid, $ProductId) AND \n  matchesValue(cityname, $CityName)\n}\n| fieldsAdd ShipmentType = if(isscheduled == \"false\", \"OnDemand\", else: isscheduled)\n| fields interval, timeframe, shipments, ShipmentType\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "shipments"
                ]
              },
              "xAxisScaling": "auto",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "value.A",
                "value.total"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_shipment_creation_request_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "shipments"
                  ],
                  "value": "sparkline",
                  "id": 1746017314949
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 13
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "52": {
          "title": "Number of pooled plans trend",
          "description": "A trend for the number of pooled plans over time",
          "type": "data",
          "query": "timeseries pooled_plans = sum(logistics_platform_planner_job_plan_total, rate:5m), \nby: { isscheduled }, \nfilter: {\n  matchesValue(productid, $ProductId) AND \n  matchesValue(cityname, $CityName) AND \n  NOT matchesValue(taskcount, \"1\")\n}\n| fields interval, timeframe, pooled_plans\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "pooled_plans"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_job_plan_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": [
                {
                  "fields": [
                    "pooled_plans"
                  ],
                  "value": "sparkline",
                  "id": 1746015968134
                }
              ]
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "53": {
          "title": "Number of tasks for replanning",
          "description": "A trend for the number of tasks that were sent for replanning due to unassignment",
          "type": "data",
          "query": "timeseries tasks = sum(logistics_platform_planner_replanning_total, rate:5m), \nby: { issuccessful }, \nfilter: {\n  matchesValue(productid, $ProductId) AND \n  matchesValue(cityname, $CityName)\n}\n| fieldsAdd outcome = if(issuccessful == \"true\", \"success\", else: \"failure\")\n| fields interval, timeframe, tasks, outcome\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "tasks"
                ]
              },
              "xAxisScaling": "analyzedTimeframe",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "interval"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_planner_job_plan_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "54": {
          "title": "Shipments Created",
          "description": "The number of shipments created over time",
          "type": "data",
          "query": "timeseries shipments = sum(logistics_platform_shipment_created_successfully_total, rate:5m), \nby: { isscheduled },\nfilter: {\n  matchesValue(productid, $ProductId) AND \n  matchesValue(cityname, $CityName)\n}\n| fieldsAdd ShipmentType = if(isscheduled == \"false\", \"OnDemand\", else: isscheduled)\n| fields interval, timeframe, shipments, ShipmentType\n",
          "visualization": "lineChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {

              },
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "fieldMapping": {
                "timestamp": "timeframe",
                "leftAxisValues": [
                  "shipments"
                ]
              },
              "xAxisScaling": "auto",
              "xAxisLabel": "timeframe",
              "xAxisIsLabelVisible": false,
              "hiddenLegendFields": [
                "value.A",
                "value.total"
              ],
              "leftYAxisSettings": {
                "isLabelVisible": true,
                "label": "logistics_platform_shipment_creation_request_total"
              }
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "AnalyticsIcon",
              "isIconVisible": false,
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {

              },
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {

              },
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": true,
            "legend": {
              "showLegend": false,
              "position": "auto",
              "ratio": 11
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        }
      },
      "layouts": {
        "1": {
          "x": 0,
          "y": 20,
          "w": 8,
          "h": 6
        },
        "8": {
          "x": 8,
          "y": 20,
          "w": 8,
          "h": 6
        },
        "9": {
          "x": 16,
          "y": 26,
          "w": 8,
          "h": 6
        },
        "10": {
          "x": 16,
          "y": 20,
          "w": 8,
          "h": 6
        },
        "11": {
          "x": 8,
          "y": 26,
          "w": 8,
          "h": 6
        },
        "12": {
          "x": 0,
          "y": 32,
          "w": 8,
          "h": 6
        },
        "13": {
          "x": 0,
          "y": 26,
          "w": 8,
          "h": 6
        },
        "14": {
          "x": 0,
          "y": 19,
          "w": 24,
          "h": 1
        },
        "15": {
          "x": 0,
          "y": 38,
          "w": 24,
          "h": 1
        },
        "17": {
          "x": 8,
          "y": 39,
          "w": 8,
          "h": 6
        },
        "19": {
          "x": 8,
          "y": 45,
          "w": 8,
          "h": 6
        },
        "20": {
          "x": 0,
          "y": 51,
          "w": 8,
          "h": 6
        },
        "21": {
          "x": 8,
          "y": 51,
          "w": 8,
          "h": 6
        },
        "22": {
          "x": 0,
          "y": 0,
          "w": 24,
          "h": 1
        },
        "25": {
          "x": 0,
          "y": 7,
          "w": 8,
          "h": 6
        },
        "26": {
          "x": 16,
          "y": 7,
          "w": 8,
          "h": 6
        },
        "27": {
          "x": 0,
          "y": 13,
          "w": 8,
          "h": 6
        },
        "28": {
          "x": 8,
          "y": 1,
          "w": 8,
          "h": 6
        },
        "29": {
          "x": 16,
          "y": 1,
          "w": 8,
          "h": 6
        },
        "30": {
          "x": 0,
          "y": 57,
          "w": 24,
          "h": 1
        },
        "31": {
          "x": 0,
          "y": 58,
          "w": 8,
          "h": 6
        },
        "32": {
          "x": 0,
          "y": 64,
          "w": 8,
          "h": 6
        },
        "33": {
          "x": 8,
          "y": 58,
          "w": 8,
          "h": 6
        },
        "34": {
          "x": 16,
          "y": 58,
          "w": 8,
          "h": 6
        },
        "35": {
          "x": 8,
          "y": 64,
          "w": 8,
          "h": 6
        },
        "36": {
          "x": 8,
          "y": 70,
          "w": 8,
          "h": 6
        },
        "37": {
          "x": 16,
          "y": 64,
          "w": 8,
          "h": 6
        },
        "38": {
          "x": 16,
          "y": 76,
          "w": 8,
          "h": 6
        },
        "39": {
          "x": 16,
          "y": 70,
          "w": 8,
          "h": 6
        },
        "40": {
          "x": 0,
          "y": 70,
          "w": 8,
          "h": 6
        },
        "41": {
          "x": 0,
          "y": 76,
          "w": 8,
          "h": 6
        },
        "42": {
          "x": 8,
          "y": 76,
          "w": 8,
          "h": 6
        },
        "43": {
          "x": 8,
          "y": 82,
          "w": 8,
          "h": 6
        },
        "44": {
          "x": 0,
          "y": 82,
          "w": 8,
          "h": 6
        },
        "45": {
          "x": 16,
          "y": 82,
          "w": 8,
          "h": 6
        },
        "46": {
          "x": 0,
          "y": 88,
          "w": 8,
          "h": 6
        },
        "47": {
          "x": 8,
          "y": 88,
          "w": 8,
          "h": 6
        },
        "48": {
          "x": 0,
          "y": 94,
          "w": 8,
          "h": 6
        },
        "49": {
          "x": 8,
          "y": 94,
          "w": 8,
          "h": 6
        },
        "50": {
          "x": 16,
          "y": 88,
          "w": 8,
          "h": 6
        },
        "51": {
          "x": 0,
          "y": 39,
          "w": 8,
          "h": 6
        },
        "52": {
          "x": 0,
          "y": 1,
          "w": 8,
          "h": 6
        },
        "53": {
          "x": 8,
          "y": 7,
          "w": 8,
          "h": 6
        },
        "54": {
          "x": 0,
          "y": 45,
          "w": 8,
          "h": 6
        }
      },
      "importedWithCode": false,
      "settings": {

      }
    }