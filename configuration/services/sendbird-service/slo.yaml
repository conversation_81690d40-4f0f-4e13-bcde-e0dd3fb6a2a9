apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: sendbird-service-slo
  labels:
    service: sendbird-service
    scope: global
spec:
  global:
    latency: 4294
  keyRequests:
  - name: sendbird-pushtokens
    path: /sendbird/pushTokens
    target: 99.9
    latency: 4294
  - name: sendbird-users
    path: /sendbird/users
    target: 99.9
    latency: 4294
  - name: sendbird-features
    path: /sendbird/features
    target: 99.9
    latency: 4294
  failureDetection:
    exceptions:
      ignoredExceptions:
      - classPattern: com.careem.lib.commons.exception.v2.GatewayServiceClientException
        messagePattern: ''
      - classPattern: org.springframework.web.util.NestedServletException
        messagePattern: ''
