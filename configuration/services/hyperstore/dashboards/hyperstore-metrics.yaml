---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: hyperstore-metrics
  labels:
    scope: global
spec:
  name: "[data-ml-platform] Hyperstore Metrics"
  json: |
    {
      "version":17,
      "variables":[
          
      ],
      "tiles":{
          "0":{
            "type":"data",
            "title":"Feature Read Traffic",
            "query":"timeseries avg(hyperstore_feature_traffic), by: { feature }, filter: { matchesValue(action, \"feature_read_traffic\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_feature_traffic)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_feature_traffic",
                        "aggregation":"avg"
                      },
                      "by":[
                        "feature"
                      ],
                      "filter":"action = feature_read_traffic "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "feature"
                      ],
                      "categoryAxisLabel":"feature",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_feature_traffic)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_feature_traffic"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_feature_traffic)"
                        ],
                        "value":"sparkline",
                        "id":1743181915025
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "feature"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "1":{
            "type":"data",
            "title":"Feature Write Traffic",
            "query":"timeseries avg(hyperstore_feature_traffic), by: { feature }, filter: { matchesValue(action, \"feature_write_traffic\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_feature_traffic)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_feature_traffic",
                        "aggregation":"avg"
                      },
                      "by":[
                        "feature"
                      ],
                      "filter":"action = feature_write_traffic "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "feature"
                      ],
                      "categoryAxisLabel":"feature",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_feature_traffic)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_feature_traffic"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_feature_traffic)"
                        ],
                        "value":"sparkline",
                        "id":1743182099270
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "feature"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "2":{
            "type":"data",
            "title":"Operations per Request",
            "query":"timeseries avg(hyperstore_ops_per_req), by: { action }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_ops_per_req)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_ops_per_req",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":""
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_ops_per_req)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_ops_per_req"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_ops_per_req)"
                        ],
                        "value":"sparkline",
                        "id":1743182163999
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "3":{
            "type":"data",
            "title":"Features per Request",
            "query":"timeseries avg(hyperstore_features_per_req)\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_features_per_req)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_features_per_req",
                        "aggregation":"avg"
                      }
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "interval",
                        "value.A"
                      ],
                      "categoryAxisLabel":"interval,value.A",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_features_per_req)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_features_per_req"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_features_per_req)"
                        ],
                        "value":"sparkline",
                        "id":1743182431816
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "4":{
            "type":"data",
            "title":"Entities per Request",
            "query":"timeseries avg(hyperstore_entities_per_req)\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_entities_per_req)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_entities_per_req",
                        "aggregation":"avg"
                      }
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "interval",
                        "value.A"
                      ],
                      "categoryAxisLabel":"interval,value.A",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_entities_per_req)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_entities_per_req"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_entities_per_req)"
                        ],
                        "value":"sparkline",
                        "id":1743182617934
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "5":{
            "type":"data",
            "title":"Hit Rates",
            "query":"timeseries avg(hyperstore_status), by: { name }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_status)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_status",
                        "aggregation":"avg"
                      },
                      "by":[
                        "name"
                      ]
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"donutChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "autoSelectVisualization":false,
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "name"
                      ],
                      "categoryAxisLabel":"name",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_status)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_status"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_status)"
                        ],
                        "value":"sparkline",
                        "id":1743182680054
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "name"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                }
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "6":{
            "type":"data",
            "title":"Memory Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"memory\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = memory "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "7":{
            "type":"data",
            "title":"grpc Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"grpc\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = grpc    "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "8":{
            "type":"data",
            "title":"Santize Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"sanitize\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = sanitize   "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "9":{
            "type":"data",
            "title":"Redis Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"redis\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = redis  "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "10":{
            "type":"data",
            "title":"Redis Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"redis\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = redis   "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "11":{
            "type":"data",
            "title":"Partition Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"partition\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = partition  "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "12":{
            "type":"data",
            "title":"Santize Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"sanitize\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = sanitize   "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "13":{
            "type":"data",
            "title":"Tikv Latency",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"tikv\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = tikv   "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          },
          "14":{
            "type":"data",
            "title":"Traffic",
            "query":"timeseries avg(hyperstore_latency), by: { action }, filter: { matchesValue(name, \"traffic\") }\n| fieldsAdd value.A = arrayAvg(`avg(hyperstore_latency)`)",
            "queryConfig":{
                "version":"12.5.4",
                "subQueries":[
                  {
                      "id":"A",
                      "isEnabled":true,
                      "datatype":"metrics",
                      "metric":{
                        "key":"hyperstore_latency",
                        "aggregation":"avg"
                      },
                      "by":[
                        "action"
                      ],
                      "filter":"name = traffic    "
                  }
                ]
            },
            "subType":"dql-builder-metrics",
            "visualization":"lineChart",
            "davis":{
                "enabled":false,
                "davisVisualization":{
                  "isAvailable":true
                }
            },
            "visualizationSettings":{
                "thresholds":[
                  
                ],
                "chartSettings":{
                  "xAxisScaling":"analyzedTimeframe",
                  "gapPolicy":"gap",
                  "circleChartSettings":{
                      "groupingThresholdType":"relative",
                      "groupingThresholdValue":0,
                      "valueType":"relative"
                  },
                  "categoryOverrides":{
                      
                  },
                  "curve":"linear",
                  "pointsDisplay":"auto",
                  "categoricalBarChartSettings":{
                      "layout":"horizontal",
                      "categoryAxisTickLayout":"horizontal",
                      "scale":"absolute",
                      "groupMode":"stacked",
                      "colorPaletteMode":"multi-color",
                      "categoryAxis":[
                        "action"
                      ],
                      "categoryAxisLabel":"action",
                      "valueAxis":[
                        "value.A"
                      ],
                      "valueAxisLabel":"value.A",
                      "tooltipVariant":"single"
                  },
                  "colorPalette":"categorical",
                  "valueRepresentation":"absolute",
                  "truncationMode":"middle",
                  "xAxisLabel":"timeframe",
                  "xAxisIsLabelVisible":false,
                  "hiddenLegendFields":[
                      "interval",
                      "value.A"
                  ],
                  "fieldMapping":{
                      "timestamp":"timeframe",
                      "leftAxisValues":[
                        "avg(hyperstore_latency)"
                      ]
                  },
                  "leftYAxisSettings":{
                      "isLabelVisible":true,
                      "label":"hyperstore_latency"
                  }
                },
                "singleValue":{
                  "showLabel":true,
                  "label":"",
                  "prefixIcon":"",
                  "autoscale":true,
                  "alignment":"center",
                  "colorThresholdTarget":"value"
                },
                "table":{
                  "rowDensity":"condensed",
                  "enableSparklines":false,
                  "hiddenColumns":[
                      
                  ],
                  "linewrapEnabled":false,
                  "lineWrapIds":[
                      
                  ],
                  "monospacedFontEnabled":false,
                  "monospacedFontColumns":[
                      
                  ],
                  "columnWidths":{
                      
                  },
                  "columnTypeOverrides":[
                      {
                        "fields":[
                            "avg(hyperstore_latency)"
                        ],
                        "value":"sparkline",
                        "id":1743184207575
                      }
                  ]
                },
                "honeycomb":{
                  "shape":"hexagon",
                  "legend":{
                      "hidden":false,
                      "position":"auto",
                      "ratio":"auto"
                  },
                  "dataMappings":{
                      
                  },
                  "displayedFields":[
                      
                  ],
                  "truncationMode":"middle",
                  "colorMode":"color-palette",
                  "colorPalette":"categorical"
                },
                "histogram":{
                  "legend":"auto",
                  "yAxis":{
                      "label":"Frequency",
                      "scale":"linear"
                  },
                  "colorPalette":"categorical",
                  "dataMappings":[
                      {
                        "valueAxis":"value.A",
                        "rangeAxis":""
                      },
                      {
                        "valueAxis":"interval",
                        "rangeAxis":""
                      }
                  ],
                  "variant":"single",
                  "truncationMode":"middle",
                  "displayedFields":[
                      "action"
                  ]
                },
                "valueBoundaries":{
                  "min":"auto",
                  "max":"auto"
                },
                "autoSelectVisualization":true
            },
            "querySettings":{
                "maxResultRecords":1000,
                "defaultScanLimitGbytes":500,
                "maxResultMegaBytes":1,
                "defaultSamplingRatio":10,
                "enableSampling":false
            }
          }
      },
      "layouts":{
          "0":{
            "x":0,
            "y":6,
            "w":24,
            "h":7
          },
          "1":{
            "x":0,
            "y":13,
            "w":24,
            "h":7
          },
          "2":{
            "x":0,
            "y":0,
            "w":7,
            "h":6
          },
          "3":{
            "x":7,
            "y":0,
            "w":8,
            "h":6
          },
          "4":{
            "x":15,
            "y":0,
            "w":9,
            "h":6
          },
          "5":{
            "x":0,
            "y":38,
            "w":8,
            "h":6
          },
          "6":{
            "x":0,
            "y":20,
            "w":8,
            "h":6
          },
          "7":{
            "x":8,
            "y":20,
            "w":8,
            "h":6
          },
          "8":{
            "x":0,
            "y":32,
            "w":8,
            "h":6
          },
          "9":{
            "x":16,
            "y":20,
            "w":8,
            "h":6
          },
          "10":{
            "x":8,
            "y":26,
            "w":8,
            "h":6
          },
          "11":{
            "x":0,
            "y":26,
            "w":8,
            "h":6
          },
          "12":{
            "x":16,
            "y":26,
            "w":8,
            "h":6
          },
          "13":{
            "x":8,
            "y":32,
            "w":8,
            "h":6
          },
          "14":{
            "x":16,
            "y":32,
            "w":8,
            "h":6
          }
      },
      "importedWithCode":false,
      "settings":{
          
      }
    }