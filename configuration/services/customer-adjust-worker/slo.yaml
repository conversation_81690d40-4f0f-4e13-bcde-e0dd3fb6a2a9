---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: customer-adjust-worker-slo
  labels:
    service: customer-adjust-worker
    scope: global
spec:
  global:
    latency: 10000
  failureDetection:
    exceptions:
      ignoredExceptions:
      - classPattern: com.careem.lib.commons.exception.v2.GatewayServiceClientException
        messagePattern: ''
      - classPattern: org.springframework.web.util.NestedServletException
        messagePattern: ''
