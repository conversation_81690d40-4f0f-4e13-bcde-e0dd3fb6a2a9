---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: navigation-service-dashboard
  labels:
    service: navigation-service
    scope: global
spec:
  name: "[plt-routing] Navigation Service Dashboard"
  json: |
    {
      "version":18,
      "variables":
      [],
      "tiles":
      {
        "0":
        {
          "title":"[navigation-service][osrm]",
          "type":"data",
          "subType":"dql-builder-metrics",
          "query":"timeseries { avg(osrm_service_responses, rate: 1m), value.B = avg(osrm_service_responses, rate: 1m, scalar: true) }, by: { code, endpoint }, filter: { matchesValue(c_service, \"*navigation-service*\") }",
          "visualization":"lineChart",
          "visualizationSettings":
          {
            "thresholds":
            [],
            "chartSettings":
            {
              "gapPolicy":"gap",
              "circleChartSettings":
              {
                "groupingThresholdType":"relative",
                "groupingThresholdValue":0,
                "valueType":"relative"
              },
              "categoryOverrides":
              {},
              "curve":"linear",
              "pointsDisplay":"auto",
              "categoricalBarChartSettings":
              {
                "layout":"horizontal",
                "categoryAxisTickLayout":"horizontal",
                "scale":"absolute",
                "groupMode":"stacked",
                "colorPaletteMode":"multi-color",
                "valueAxisScale":"linear"
              },
              "colorPalette":"categorical",
              "valueRepresentation":"absolute",
              "truncationMode":"middle",
              "xAxisScaling":"analyzedTimeframe",
              "xAxisLabel":"timeframe",
              "xAxisIsLabelVisible":false,
              "hiddenLegendFields":
              [
                "interval",
                "value.A"
              ],
              "fieldMapping":
              {
                "timestamp":"timeframe",
                "leftAxisValues":
                [
                  "avg(osrm_service_responses, rate:1m)"
                ]
              },
              "leftYAxisSettings":
              {}
            },
            "singleValue":
            {
              "showLabel":true,
              "label":"",
              "prefixIcon":"AnalyticsIcon",
              "isIconVisible":false,
              "autoscale":true,
              "alignment":"center",
              "colorThresholdTarget":"value"
            },
            "table":
            {
              "rowDensity":"condensed",
              "enableSparklines":false,
              "hiddenColumns":
              [],
              "linewrapEnabled":false,
              "lineWrapIds":
              [],
              "monospacedFontEnabled":false,
              "monospacedFontColumns":
              [],
              "columnWidths":
              {},
              "columnTypeOverrides":
              []
            },
            "honeycomb":
            {
              "shape":"hexagon",
              "legend":
              {
                "hidden":false,
                "position":"auto",
                "ratio":"auto"
              },
              "dataMappings":
              {},
              "displayedFields":
              [],
              "truncationMode":"middle",
              "colorMode":"color-palette",
              "colorPalette":"categorical"
            },
            "histogram":
            {
              "legend":
              {
                "position":
                "auto"
              },
              "yAxis":
              {
                "label":"Frequency",
                "isLabelVisible":true,
                "scale":"linear"
              },
              "colorPalette":"categorical",
              "dataMappings":
              [],
              "variant":"single",
              "truncationMode":"middle"
            },
            "valueBoundaries":
            {
              "min":"auto",
              "max":"auto"
            },
            "autoSelectVisualization":true
          },
          "querySettings":
          {
            "maxResultRecords":1000,
            "defaultScanLimitGbytes":500,
            "maxResultMegaBytes":1,
            "defaultSamplingRatio":10,
            "enableSampling":false
          },
          "queryConfig":
          {
            "version":"13.6.4",
            "subQueries":
            [
              {
                "id":"B",
                "isEnabled":true,
                "datatype":"metrics",
                "metric":
                {
                  "key":"osrm_service_responses",
                  "aggregation":"avg"
                },
                "by":
                [
                  "code",
                  "endpoint"
                ],
                "rate":"1m",
                "filter":"c_service = *navigation-service*"
              }
            ]
          },
          "davis":
          {
            "enabled":false,
            "davisVisualization":
            {
              "isAvailable":true
            }
          }
        },
        "1":
        {
          "title":"[nav-service][osrm] Errors",
          "type":"data",
          "subType":"dql-builder-metrics",
          "query":"timeseries { avg(osrm_service_errors, rate: 1m), value.A = avg(osrm_service_errors, rate: 1m, scalar: true) }, by: { endpoint, attempts, error }, filter: { matchesValue(c_service, \"*navigation-service*\") }",
          "visualization":"lineChart",
          "visualizationSettings":
          {
            "thresholds":[],
            "chartSettings":
            {
              "gapPolicy":"gap",
              "circleChartSettings":
              {
                "groupingThresholdType":"relative",
                "groupingThresholdValue":0,
                "valueType":"relative"
              },
              "categoryOverrides":
              {},
              "curve":"linear",
              "pointsDisplay":"auto",
              "categoricalBarChartSettings":
              {
                "layout":"horizontal",
                "categoryAxisTickLayout":"horizontal",
                "scale":"absolute",
                "groupMode":"stacked",
                "colorPaletteMode":"multi-color",
                "valueAxisScale":"linear"
              },
              "colorPalette":"categorical",
              "valueRepresentation":"absolute",
              "truncationMode":"middle",
              "xAxisScaling":"analyzedTimeframe",
              "xAxisLabel":"timeframe",
              "xAxisIsLabelVisible":false,
              "hiddenLegendFields":
              [
                "interval","value.A",
                "sum(osrm_service_errors)"
              ],
              "fieldMapping":
              {
                "timestamp":"timeframe",
                "leftAxisValues":
                [
                  "avg(osrm_service_errors, rate:1m)"
                ]
              },
              "leftYAxisSettings":
              {}
            },
            "singleValue":
            {
              "showLabel":true,
              "label":"",
              "prefixIcon":"AnalyticsIcon",
              "isIconVisible":false,
              "autoscale":true,
              "alignment":"center",
              "colorThresholdTarget":"value"
            },
            "table":
            {
              "rowDensity":"condensed",
              "enableSparklines":false,
              "hiddenColumns":
              [],
              "linewrapEnabled":false,
              "lineWrapIds":
              [],
              "monospacedFontEnabled":false,
              "monospacedFontColumns":
              [],
              "columnWidths":
              {},
              "columnTypeOverrides":
              []
            },
            "honeycomb":
            {
              "shape":"hexagon",
              "legend":
              {
                "hidden":false,
                "position":"auto",
                "ratio":"auto"
              },
              "dataMappings":
              {},
              "displayedFields":
              [],
              "truncationMode":"middle",
              "colorMode":"color-palette",
              "colorPalette":"categorical"
            },
            "histogram":
            {
              "legend":
              {
                "position":"auto"
              },
              "yAxis":
              {
                "label":"Frequency",
                "isLabelVisible":true,
                "scale":"linear"
              },
              "colorPalette":"categorical",
              "dataMappings":
              [],
              "variant":"single",
              "truncationMode":"middle"
            },
            "valueBoundaries":
            {
              "min":"auto",
              "max":"auto"
            },
            "autoSelectVisualization":true
          },
          "querySettings":
          {
            "maxResultRecords":1000,
            "defaultScanLimitGbytes":500,
            "maxResultMegaBytes":1,
            "defaultSamplingRatio":10,
            "enableSampling":false
          },
          "queryConfig":
          {
            "version":"13.6.4",
            "subQueries":
            [
              {
                "id":"A",
                "isEnabled":true,
                "datatype":"metrics",
                "metric":
                  {
                    "key":"osrm_service_errors",
                    "aggregation":"avg"
                  },
                  "by":
                  [
                    "endpoint",
                    "attempts",
                    "error"
                  ]
                  ,"rate":"1m",
                  "filter":"c_service = *navigation-service*"
              }
            ]
          },
          "davis":
          {
            "enabled":false,
            "davisVisualization":
            {
              "isAvailable":true
            }
          }
        },
        "2":
        {
          "title":"[nav-service][osrm] Client by variant",
          "type":"data",
          "subType":"dql-builder-metrics",
          "query":"timeseries { avg(osrm_client_requests_total, rate: 1m), value.A = avg(osrm_client_requests_total, rate: 1m, scalar: true) }, by: { osrm_client_name, variant }",
          "visualization":"lineChart",
          "visualizationSettings":
          {
            "thresholds":
            [],
            "chartSettings":
            {
              "gapPolicy":"gap",
              "circleChartSettings":
              {
                "groupingThresholdType":"relative",
                "groupingThresholdValue":0,
                "valueType":"relative"
              },
              "categoryOverrides":
              {},
              "curve":"linear",
              "pointsDisplay":"auto",
              "categoricalBarChartSettings":
              {
                "layout":"horizontal",
                "categoryAxisTickLayout":"horizontal",
                "scale":"absolute",
                "groupMode":"stacked",
                "colorPaletteMode":"multi-color",
                "valueAxisScale":"linear"
              },
              "colorPalette":"categorical",
              "valueRepresentation":"absolute",
              "truncationMode":"middle",
              "xAxisScaling":"analyzedTimeframe",
              "xAxisLabel":"timeframe",
              "xAxisIsLabelVisible":false,
              "hiddenLegendFields":
              [
                "interval",
                "value.A",
                "sum(osrm_client_requests_total, rate:1m)"
              ],
              "fieldMapping":
              {
                "timestamp":"timeframe",
                "leftAxisValues":
                [
                  "avg(osrm_client_requests_total, rate:1m)"
                ]
              },
              "leftYAxisSettings":
              {}
            },
            "singleValue":
            {
              "showLabel":true,
              "label":"",
              "prefixIcon":"AnalyticsIcon",
              "isIconVisible":false,
              "autoscale":true,
              "alignment":"center",
              "colorThresholdTarget":"value"
            },
            "table":
            {
              "rowDensity":"condensed",
              "enableSparklines":false,
              "hiddenColumns":
              [],
              "linewrapEnabled":false,
              "lineWrapIds":
              [],
              "monospacedFontEnabled":false,
              "monospacedFontColumns":
              [],
              "columnWidths":{},
              "columnTypeOverrides":
              []
            },
            "honeycomb":
            {
              "shape":"hexagon",
              "legend":
              {
                "hidden":false,
                "position":"auto",
                "ratio":"auto"
              },
              "dataMappings":
              {},
              "displayedFields":
              [],
              "truncationMode":"middle",
              "colorMode":"color-palette",
              "colorPalette":"categorical"
            },
            "histogram":
            {
              "legend":
              {
                "position":"auto"
              },
              "yAxis":
              {
                "label":"Frequency",
                "isLabelVisible":true,
                "scale":"linear"
              },
              "colorPalette":"categorical",
              "dataMappings":
              [],
              "variant":"single",
              "truncationMode":"middle"
            },
            "valueBoundaries":
            {
              "min":"auto",
              "max":"auto"
            },
            "autoSelectVisualization":true
          },
          "querySettings":
          {
            "maxResultRecords":1000,
            "defaultScanLimitGbytes":500,
            "maxResultMegaBytes":1,
            "defaultSamplingRatio":10,
            "enableSampling":false
          },
          "queryConfig":
          {
            "version":"13.6.4",
            "subQueries":
            [
              {
                "id":"A",
                "isEnabled":true,
                "datatype":"metrics",
                "metric":
                {
                  "key":"osrm_client_requests_total",
                  "aggregation":"avg"
                },
                "by":
                [
                  "osrm_client_name","variant"
                ],
                "rate":"1m","filter":""
              }
            ],
            "globalCommands":
            {}
          },
          "davis":
          {
            "enabled":false,
            "davisVisualization":
            {
              "isAvailable":true
            }
          }
        },
        "3":
        {
          "title":
          "[nav-service][osrm] client by mode",
          "type":"data",
          "subType":"dql-builder-metrics",
          "query":"timeseries { sum(osrm_client_requests_total, rate: 1m), value.A = avg(osrm_client_requests_total, rate: 1m, scalar: true) }, by: { client, osrm_client_name, mode }, filter: { matchesValue(kubernetes_namespace, \"*navigation-service*\") }",
          "visualization":"lineChart",
          "visualizationSettings":
          {
            "thresholds":
            [],
            "chartSettings":
            {
              "gapPolicy":"gap",
              "circleChartSettings":
              {
                "groupingThresholdType":"relative",
                "groupingThresholdValue":0,
                "valueType":"relative"
              },
              "categoryOverrides":
              {},
              "curve":"linear",
              "pointsDisplay":"auto",
              "categoricalBarChartSettings":
              {
                "layout":"horizontal",
                "categoryAxisTickLayout":"horizontal",
                "scale":"absolute",
                "groupMode":"stacked",
                "colorPaletteMode":"multi-color",
                "valueAxisScale":"linear"
              },
              "colorPalette":"categorical",
              "valueRepresentation":"absolute",
              "truncationMode":"middle",
              "xAxisScaling":"analyzedTimeframe",
              "xAxisLabel":"timeframe",
              "xAxisIsLabelVisible":false,
              "hiddenLegendFields":
              [
                "interval",
                "value.A",
                "sum(osrm_client_requests_total)"
              ],
              "fieldMapping":
              {
                "timestamp":"timeframe",
                "leftAxisValues":
                [
                  "sum(osrm_client_requests_total, rate:1m)"
                ]
              },
              "leftYAxisSettings":
              {}
            },
            "singleValue":
            {
              "showLabel":true,
              "label":"",
              "prefixIcon":"AnalyticsIcon",
              "isIconVisible":false,
              "autoscale":true,
              "alignment":"center",
              "colorThresholdTarget":"value"
            },
            "table":
            {
              "rowDensity":"condensed",
              "enableSparklines":false,
              "hiddenColumns":
              [],
              "linewrapEnabled":false,
              "lineWrapIds":
              [],
              "monospacedFontEnabled":false,
              "monospacedFontColumns":
              [],
              "columnWidths":{},
              "columnTypeOverrides":
              [
                {
                  "fields":
                  [
                    "sum(osrm_client_requests_total, rate:1m)"
                  ],
                  "value":"sparkline",
                  "id":1747824666756
                }
              ]
            },
            "honeycomb":
            {
              "shape":"hexagon",
              "legend":
              {
                "hidden":false,
                "position":"auto",
                "ratio":"auto"
              },
              "dataMappings":
              {},
              "displayedFields":
              [],
              "truncationMode":"middle",
              "colorMode":"color-palette",
              "colorPalette":"categorical"
            },
            "histogram":
            {
              "legend":
              {
                "position":"auto"
              },
              "yAxis":
              {
                "label":"Frequency",
                "isLabelVisible":true,
                "scale":"linear"
              },
              "colorPalette":"categorical",
              "dataMappings":
              [],
              "variant":"single",
              "truncationMode":"middle"
            },
            "valueBoundaries":
            {
              "min":"auto",
              "max":"auto"
            },
            "autoSelectVisualization":true
          },
          "querySettings":
          {
            "maxResultRecords":1000,
            "defaultScanLimitGbytes":500,
            "maxResultMegaBytes":1,
            "defaultSamplingRatio":10,
            "enableSampling":false
          },
          "queryConfig":
          {
            "version":"13.6.4",
            "subQueries":
            [
              {
                "id":"A",
                "isEnabled":true,
                "datatype":"metrics",
                "metric":
                  {
                    "key":"osrm_client_requests_total",
                    "aggregation":"sum"
                  },
                  "by":
                  [
                    "client",
                    "osrm_client_name",
                    "mode"
                  ],
                  "rate":"1m",
                  "filter":"kubernetes_namespace = *navigation-service*"
              }
            ],
            "globalCommands":
            {}
          },
          "davis":
          {
            "enabled":false,
            "davisVisualization":
            {
              "isAvailable":true
            }
          }
        },
        "4":
        {
          "title":"[nav service][osrm][route] not OK ",
          "type":"data",
          "subType":"dql-builder-metrics",
          "query":"timeseries { `sum(osrm_service_responses, rate:1m)·A` = sum(osrm_service_responses, rate: 1m), value.A = avg(osrm_service_responses, rate: 1m, scalar: true) }, by: { code }, filter: { matchesValue(c_service, \"*navigation-service*\") AND matchesValue(endpoint, \"*Route*\") AND NOT matchesValue(code, \"*Ok*\") }\n| join [\n    timeseries { `sum(osrm_service_responses, rate:1m)·B` = sum(osrm_service_responses, rate: 1m), value.B = avg(osrm_service_responses, rate: 1m, scalar: true) },\n    filter: { matchesValue(c_service, \"*navigation-service*\") AND matchesValue(endpoint, \"*Route*\") }\n  ], on: { interval }, fields: { `sum(osrm_service_responses, rate:1m)·B`, value.B }\n| fieldsAdd D = (`sum(osrm_service_responses, rate:1m)·A`[]/`sum(osrm_service_responses, rate:1m)·B`[])",
          "visualization":"lineChart",
          "visualizationSettings":
          {
            "thresholds":
            [],
            "chartSettings":
            {
              "gapPolicy":"gap",
              "circleChartSettings":
              {
                "groupingThresholdType":"relative",
                "groupingThresholdValue":0,
                "valueType":"relative"
              },
              "categoryOverrides":
              {},
              "curve":"linear",
              "pointsDisplay":"auto",
              "categoricalBarChartSettings":
              {
                "layout":"horizontal",
                "categoryAxisTickLayout":"horizontal",
                "scale":"absolute",
                "groupMode":"stacked",
                "colorPaletteMode":"multi-color",
                "valueAxisScale":"linear"
              },
              "colorPalette":"categorical",
              "valueRepresentation":"absolute",
              "truncationMode":"middle",
              "bandChartSettings":
              {
                "lower":"sum(osrm_service_responses, rate:1m)·A",
                "upper":"sum(osrm_service_responses, rate:1m)·B"
              },
              "xAxisScaling":"analyzedTimeframe",
              "xAxisLabel":"timeframe",
              "xAxisIsLabelVisible":false,
              "hiddenLegendFields":
              [
                "interval","value.A",
                "sum(osrm_service_responses)"
              ],
              "fieldMapping":
              {
                "timestamp":"timeframe",
                "leftAxisValues":
                [
                  "sum(osrm_service_responses, rate:1m)·A",
                  "sum(osrm_service_responses, rate:1m)·B",
                  "D"
                ]
              },
              "leftYAxisSettings":
              {}
            },
            "singleValue":
            {
              "showLabel":true,
              "label":"",
              "prefixIcon":"AnalyticsIcon",
              "isIconVisible":false,
              "autoscale":true,
              "alignment":"center",
              "colorThresholdTarget":"value"
            },
            "table":
            {
              "rowDensity":"condensed",
              "enableSparklines":false,
              "hiddenColumns":
              [],
              "linewrapEnabled":false,
              "lineWrapIds":
              [],
              "monospacedFontEnabled":false,
              "monospacedFontColumns":
              [],
              "columnWidths":
              {},
              "columnTypeOverrides":
              [
                {
                  "fields":
                  [
                    "sum(osrm_service_responses, rate:1m)·A",
                    "sum(osrm_service_responses, rate:1m)·B",
                    "D"
                  ],
                  "value":"sparkline",
                  "id":1747825184475
                }
              ]
            },
            "honeycomb":
            {
              "shape":"hexagon",
              "legend":
              {
                "hidden":false,
                "position":"auto",
                "ratio":"auto"
              },
              "dataMappings":
              {},
              "displayedFields":
              [],
              "truncationMode":"middle",
              "colorMode":"color-palette",
              "colorPalette":"categorical"
            },
            "histogram":
            {
              "legend":
              {
                "position":"auto"
              },
              "yAxis":
              {
                "label":"Frequency",
                "isLabelVisible":true,
                "scale":"linear"
              },
              "colorPalette":"categorical",
              "dataMappings":
              [],
              "variant":"single",
              "truncationMode":"middle"
            },
            "valueBoundaries":
            {
              "min":"auto",
              "max":"auto"
            },
            "autoSelectVisualization":true
          },
          "querySettings":
          {
            "maxResultRecords":1000,
            "defaultScanLimitGbytes":500,
            "maxResultMegaBytes":1,
            "defaultSamplingRatio":10,
            "enableSampling":false
          },
          "queryConfig":
          {
            "version":"13.6.4",
            "subQueries":
            [
              {
                "id":"A",
                "isEnabled":true,
                "datatype":"metrics",
                "metric":
                {
                  "key":"osrm_service_responses",
                  "aggregation":"sum"
                },
                "by":
                [
                  "code"
                ],
                "rate":"1m",
                "filter":"c_service = *navigation-service* endpoint = *Route* code != *Ok*"
              },
              {
                "id":"B",
                "isEnabled":true,
                "datatype":"metrics",
                "metric":
                {
                  "key":"osrm_service_responses",
                  "aggregation":"sum"
                },
                "rate":"1m",
                "filter":"c_service = *navigation-service* endpoint = *Route* "
              },
              {
                "id":"D",
                "isEnabled":true,
                "datatype":"expression",
                "expression":"(A/B)"
              }
            ]
          },
          "davis":
          {
            "enabled":false,
            "davisVisualization":
            {
              "isAvailable":true
            }
          }
        },
        "5":
        {
          "title":"[nav service][osrm][match] not OK ",
          "type":"data",
          "query":"timeseries { `sum(osrm_service_responses, rate:1m)·A` = sum(osrm_service_responses, rate: 1m), value.A = avg(osrm_service_responses, rate: 1m, scalar: true) }, by: { code }, filter: { matchesValue(c_service, \"*navigation-service*\") AND matchesValue(endpoint, \"*Match*\") AND NOT matchesValue(code, \"*Ok*\") }\n| join [\n    timeseries { `sum(osrm_service_responses, rate:1m)·B` = sum(osrm_service_responses, rate: 1m), value.B = avg(osrm_service_responses, rate: 1m, scalar: true) },\n    filter: { matchesValue(c_service, \"*navigation-service*\") AND matchesValue(endpoint, \"*Match*\") }\n  ], on: { interval }, fields: { `sum(osrm_service_responses, rate:1m)·B`, value.B }\n| fieldsAdd D = (`sum(osrm_service_responses, rate:1m)·A`[]/`sum(osrm_service_responses, rate:1m)·B`[])",
          "visualization":"lineChart",
          "visualizationSettings":
          {
            "thresholds":
            [],
            "chartSettings":
            {
              "gapPolicy":"gap",
              "circleChartSettings":
              {
                "groupingThresholdType":"relative",
                "groupingThresholdValue":0,
                "valueType":"relative"
              },
              "categoryOverrides":
              {},
              "curve":"linear",
              "pointsDisplay":"auto",
              "categoricalBarChartSettings":
              {
                "layout":"horizontal",
                "categoryAxisTickLayout":"horizontal",
                "scale":"absolute",
                "groupMode":"stacked",
                "colorPaletteMode":"multi-color",
                "valueAxisScale":"linear"
              },
              "colorPalette":"categorical",
              "valueRepresentation":"absolute",
              "truncationMode":"middle",
              "bandChartSettings":
              {
                "lower":"sum(osrm_service_responses, rate:1m)·A",
                "upper":"sum(osrm_service_responses, rate:1m)·B"
              },
              "xAxisScaling":"analyzedTimeframe",
              "xAxisLabel":"timeframe","xAxisIsLabelVisible":false,
              "hiddenLegendFields":
              [
                "interval","value.A",
                "sum(osrm_service_responses)"
              ],
              "fieldMapping":
              {
                "timestamp":"timeframe",
                "leftAxisValues":
                [
                  "sum(osrm_service_responses, rate:1m)·A",
                  "sum(osrm_service_responses, rate:1m)·B",
                  "D"
                ]
              },
              "leftYAxisSettings":
              {}
            },
            "singleValue":
            {
              "showLabel":true,
              "label":"",
              "prefixIcon":"AnalyticsIcon",
              "isIconVisible":false,
              "autoscale":true,
              "alignment":"center",
              "colorThresholdTarget":"value"
            },
            "table":
            {
              "rowDensity":"condensed",
              "enableSparklines":false,
              "hiddenColumns":
              [],
              "linewrapEnabled":false,
              "lineWrapIds":
              [],
              "monospacedFontEnabled":false,
              "monospacedFontColumns":
              [],
              "columnWidths":
              {},
              "columnTypeOverrides":
              [
                {
                  "fields":
                  [
                    "sum(osrm_service_responses, rate:1m)·A",
                    "sum(osrm_service_responses, rate:1m)·B",
                    "D"
                  ],
                  "value":"sparkline",
                  "id":1747825550655
                }
              ]
            },
            "honeycomb":
            {
              "shape":"hexagon",
              "legend":
              {
                "hidden":false,
                "position":"auto",
                "ratio":"auto"
              },
              "dataMappings":
              {},
              "displayedFields":
              [],
              "truncationMode":"middle",
              "colorMode":"color-palette",
              "colorPalette":"categorical"
            },
            "histogram":
            {
              "legend":
              {
                "position":"auto"
              },
              "yAxis":
              {
                "label":"Frequency",
                "isLabelVisible":true,
                "scale":"linear"
              },
              "colorPalette":"categorical",
              "dataMappings":
              [],
              "variant":"single",
              "truncationMode":"middle"
            },
            "valueBoundaries":
            {
              "min":"auto",
              "max":"auto"
            },
            "autoSelectVisualization":true
          },
          "querySettings":
          {
            "maxResultRecords":1000,
            "defaultScanLimitGbytes":500,
            "maxResultMegaBytes":1,
            "defaultSamplingRatio":10,
            "enableSampling":false
          },
          "davis":
          {
            "enabled":false,
            "davisVisualization":
            {
              "isAvailable":true
            }
          }
        }
      },
      "layouts":
      {
        "0":
        {
          "x":0,
          "y":0,
          "w":9,
          "h":8
        },
        "1":
        {
          "x":9,
          "y":0,
          "w":9,
          "h":8
        },
        "2":
        {
          "x":0,
          "y":8,
          "w":9,
          "h":6
        },
        "3":
        {
          "x":9,
          "y":8,
          "w":9,
          "h":6
        },
        "4":
        {
          "x":0,
          "y":14,
          "w":9,
          "h":8
        },
        "5":
        {
          "x":9,
          "y":14,
          "w":9,
          "h":8
        }
      },
      "importedWithCode":false,
      "settings":
      {}
    }