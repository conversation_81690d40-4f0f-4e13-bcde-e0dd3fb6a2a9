---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: autosuggestion-service-slo
  labels:
    service: autosuggestion-service
    scope: global
spec:
  global:
    latency: 100
  keyRequests:
    - name: "sync"
      path: /v1/sync/<id>
      target: 99.95
    - name: "manual-curation-autosuggestions"
      path: v1/manual-curation/autosuggestions
      target: 99.95
    - name: "manual-curation-autosuggestions-by-id"
      path: v1/manual-curation/autosuggestions/<id>
      target: 99.95
    - name: "info"
      path: info
      target: 99.95
  kafka:
    target: 99.95
    groups:
      - name: yoda-integrations
        topics:
          - yoda-autosuggestion-food-text-search
          - yoda-autosuggestion-groceries
        latency: 1.0
        max_age: 600.0