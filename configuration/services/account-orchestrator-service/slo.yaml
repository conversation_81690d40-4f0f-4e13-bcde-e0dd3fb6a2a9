---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: account-orchestrator-service-slo
  labels:
    service: account-orchestrator-service
    scope: global
spec:
  global:
    latency: 10000
  keyRequests:
    - name: "users-balance"
      path: /users/balance
      target: 99.9
      latency: 500
    - name: "accounts-userid-balance"
      path: /v<apiVersion>/accounts/<userId>/balance
      target: 99.9
      latency: 500
    - name: "accounts-balance"
      path: /v<apiVersion>/accounts/balance
      target: 99.9
      latency: 500
    - name: "active-downtime-userid"
      path: /v1/active-downtime/<userId>
      target: 99.9
      latency: 500
    - name: "active-downtime-all"
      path: /v1/active-downtime
      target: 99.9
      latency: 10000
    - name: "accounts-transactions-process"
      path: /v<apiVersion>/accounts/transactions/process
      target: 99.9
      latency: 10000
    - name: "accounts-transactions-refund"
      path: /v<apiVersion>/accounts/transactions/refund
      target: 99.9
      latency: 10000
    - name: "accounts-transaction-verify"
      path: /v<apiVersion>/accounts/transaction/verify
      target: 99.9
      latency: 10000
    - name: "accounts-underpayments-process"
      path: /v<apiVersion>/accounts/underpayments/process
      target: 99.9
      latency: 10000
    - name: "withdrawal-transaction-process"
      path: /v2/withdrawal/transaction/process
      target: 99.9
      latency: 10000
    - name: "accounts-transaction-publish"
      path: /v2/accounts/transaction/publish
      target: 99.9
      latency: 10000
    - name: "user-remaining-limits"
      path: /v<apiVersion>/user/remainingLimits
      target: 99.9
      latency: 10000
  failureDetection:
    exceptions:
      ignoredExceptions:
        - classPattern: com.careem
    httpErrorCodes: 500-599