---
apiVersion: observability.careem.com/v1
kind: CustomMetrics
metadata:
  name: account-orchestrator-service-metrics
  labels:
    service: account-orchestrator-service
    scope: global
spec:
  metrics:
    - name: "aos_downstream_wallet_service_error_total"
    - name: "aos_wallet_service_thread_executor_rejection_total"
    - name: "aos_atomic_transaction_revert_error_total"
    - name: "aos_success_and_failed_transactions_error_total"
    - name: "aos_underpayment_passive_settlement_error_total"
    - name: "aos_error_extracting_eand_breach_limits_total"
    - name: "aos_idempotency_db_cleanup_failure_total"
    - name: "aos_downstream_idempotency_error_total"
    - name: "aos_downstream_wallet_service_success_total"
    - name: "aos_more_than_one_request_error_total"
    - name: "aos_atomic_transaction_revert_attempt_total"
    - name: "aos_underpayment_passive_settlement_debit_revert_total"
    - name: "aos_user_missing_accounts_in_ams_error_total"
    - name: "aos_user_missing_balance_in_abs_error_total"
    - name: "aos_error_transferring_balance_to_user_primary_account_total"
    - name: "aos_withdrawal_initiation_error_total"
    - name: "aos_publish_transaction_error_total"
    - name: "aos_galileo_access_error_total"
