---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: account-orchestrator-service-anomaly-detection
  labels:
    scope: global
    service: account-orchestrator-service
spec:
  anomalies:
    staticThreshold:
      account-orchestrator-service-critical-errors:
        title: "[account-orchestrator-service] critical errors > 0"
        description: "[account-orchestrator-service] critical errors > 0"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries errors = sum(aos_downstream_wallet_service_error_total),
          by: {c_service},
          interval: 1m
          | fieldsAdd entity.name = c_service
          | fieldsAdd event_name = "error rate in downstream wallet service legacy flow"
          | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          | append [
            timeseries errors = sum(aos_wallet_service_thread_executor_rejection_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "wallet-service async executor task rejection"
          ]
          | append [
            timeseries errors = sum(aos_atomic_transaction_revert_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "atomic transaction revert error"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_success_and_failed_transactions_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "some transactions succeeded and some failed when processing bulk transactions"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_underpayment_passive_settlement_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "during passive settlement, active loop account debited, but underpayments not settled"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_error_extracting_eand_breach_limits_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "error extracting eand limits"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
          timeseries errors = sum(aos_galileo_access_error_total),
          by: {c_service},
          interval: 1m
          | fieldsAdd entity.name = c_service
          | fieldsAdd event_name = "error accessing galileo variable"
          | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_idempotency_db_cleanup_failure_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "idempotency db cleanup failure"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_downstream_idempotency_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "downstream idempotency error"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_atomic_transaction_revert_attempt_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "error while executing second dry-run transaction"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_user_missing_balance_in_abs_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "user missing account in ABS"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_error_transferring_balance_to_user_primary_account_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "error transferring balance to user primary account"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_withdrawal_initiation_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "withdrawal initiation error"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(aos_publish_transaction_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "publishing transaction to wallet-statement-service and analytika failed"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | append [
            timeseries errors = sum(kafka_producer_message_dropped_total),
            filter: c_service == "account-orchestrator-service",
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "publishing kafka message failed"
            | fieldsAdd errors_5m = arrayMovingSum(errors, 5)
          ]
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, errors_5m, dt_service.id, dt_service.entity.name, c_service, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "ERROR_EVENT"
        eventName: "is more than 0"
        dimensionNameProperty: event_name
