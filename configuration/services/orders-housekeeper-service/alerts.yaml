---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: orders-housekeeper-service-jobs-anomaly-detection-static
  labels:
    scope: global
    service: orders-housekeeper-service
spec:
  anomalies:
    staticThreshold:
      high-locked-jobs-threshold:
        title: "[orders-housekeeper-service] More Than 10 Locked Jobs in Last 10 Minutes"
        description: "Alert whenever the number of locked jobs exceeds 10 in the last 10 minutes"
        threshold: 10
        alertCondition: "ABOVE"
        evaluationWindow: "10"
        query: |
          timeseries locked_jobs = sum(jobs_total, default:0), interval:1m, by:{c_service, type, job_name, status}, filter:{status=="locked"}
          | fieldsAdd entity.name = concat(c_service, ":custom - Jobs")
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fieldsAdd locked_jobs_10m = arrayMovingSum(locked_jobs, 10)
          | fields interval, timeframe, locked_jobs_10m, dt_service.id, dt_service.entity.name, c_service, type, job_name, status
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        dimensionNameProperty: "job_name"
        eventType: "AVAILABILITY_EVENT"
        eventName: "has more than 10 locked jobs in last 10 minutes"
      zero-job-initiation-rate-threshold:
        title: "[orders-housekeeper-service] Zero Initiated Jobs Over The Last 15 Minutes"
        description: "Detect scenarios where the scheduler produces no work in the interval"
        alertCondition: "BELOW"
        evaluationWindow: "15"
        threshold: 1
        query: |
          timeseries initiated_jobs = sum(jobs_total, default:0), interval:1m, by:{c_service, type, job_name, status}, filter:{status == "initiated"}
          | fieldsAdd entity.name = concat(c_service, ":custom - Jobs")
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fieldsAdd initiation_rate_15m = arrayMovingSum(initiated_jobs, 15)
          | fields interval, timeframe, initiation_rate_15m, dt_service.id, dt_service.entity.name, c_service, type, job_name, status
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "AVAILABILITY_EVENT"
        eventName: "no work detected in the past 15 minutes"
      high-executor-failure-count-threshold:
        title: "[orders-housekeeper-service] More Than 3 Failures per Executor in 10 Minutes"
        description: "Alert if any executor records 3 or more failures in the last 10 minutes"
        threshold: 3
        alertCondition: "ABOVE"
        evaluationWindow: "10"
        query: |
          timeseries failed_jobs = sum(jobs_total, default:0), interval:1m, by:{c_service, type, job_name, status}, filter:{status=="failed"}
          | fieldsAdd entity.name = concat(c_service, ":custom - Jobs")
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fieldsAdd failures_10m = arrayMovingSum(failed_jobs, 10)
          | fields interval, timeframe, failures_10m, dt_service.id, dt_service.entity.name, c_service, type, job_name, status
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        dimensionNameProperty: "job_name"
        eventType: "AVAILABILITY_EVENT"
        eventName: "executor has too many failures"
      low-job-completion-rate-threshold:
        title: "[orders-housekeeper-service] Low Job Completion Rate"
        description: "Alert triggered when the 5-minute smoothed completion rate drops below 0.5%"
        alertCondition: "ABOVE"
        evaluationWindow: "5"
        threshold: 0.5
        query: |
          timeseries {completed_jobs = sum(jobs_total, default: 0, filter: status == "completed"), initiated_jobs = sum(jobs_total, default: 0, filter: status == "initiated")}, interval: 1m, by:{c_service, type, job_name}
            | fieldsAdd completed_jobs_5m = arrayMovingSum(completed_jobs, 5)
            | fieldsAdd initiated_jobs_5m = arrayMovingSum(initiated_jobs, 5)
            | fieldsAdd failure_rate_5m = abs((1 - (completed_jobs_5m[] / initiated_jobs_5m[]))) * 100, entity.name = concat(c_service, ":custom - Jobs")
            | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
            | fields interval, timeframe, failure_rate_5m, dt_service.id, dt_service.entity.name, c_service, type, job_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "AVAILABILITY_EVENT"
        eventName: "low job completion rate"
      zero-saved-job-count-threshold:
        title: "[orders-housekeeper-service] Zero Saved Jobs Over The Last 10 Minutes"
        description: "Scenarios when events produces no saved jobs in the interval"
        alertCondition: "BELOW"
        evaluationWindow: "10"
        threshold: 1
        query: |
          timeseries saved_jobs = sum(jobs_total, default:0), interval:1m, by:{c_service, type, job_name, status}, filter:{status == "saved"}
          | fieldsAdd entity.name = concat(c_service, ":custom - Jobs")
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fieldsAdd saved_rate_10m = arrayMovingSum(saved_jobs, 10)
          | fields interval, timeframe, saved_rate_10m, dt_service.id, dt_service.entity.name, c_service, type, job_name, status
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "AVAILABILITY_EVENT"
        eventName: "no new jobs detected in the past 10 minutes"