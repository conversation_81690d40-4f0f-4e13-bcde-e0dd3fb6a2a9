---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: payments-dashboard
  labels:
    scope: production
spec:
  name: "[Payments] Payments Monitoring Dashboard"
  json: |
    {
        "version": 18,
        "variables": [],
        "tiles": {
            "0": {
                "title": "[PO] Mark Underpayment for Deferred Payment [Throughput]",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(rh_wallet_wo_transaction_total), value.A = avg(rh_wallet_wo_transaction_total, scalar: true) }, by: { rh_wallet_wo_status }, filter: { matchesValue(application, \"payment-orchestrator\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(rh_wallet_wo_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(rh_wallet_wo_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "rh_wallet_wo_transaction_total"
                        },
                        "tooltip": {
                            "seriesDisplayMode": "single-line",
                            "variant": "single"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(rh_wallet_wo_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745236144149
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "rh_wallet_wo_transaction_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "rh_wallet_wo_status"
                            ],
                            "filter": "application = payment-orchestrator "
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "2": {
                "title": "Trxs processed - Webhook notifications sent",
                "type": "data",
                "query": "timeseries total = sum(rh_wallet_wo_transaction_total),\nby: { rh_wallet_wo_merchant_ref },\ninterval: 5m,\nfilter: { matchesValue(application, \"payment-orchestrator\")\nAND matchesValue(rh_wallet_wo_status, { \"Success\", \"Failure\", \"Amount_on_hold\" }) }\n| join [\n  timeseries total = sum(rh_wallet_wo_trx_webhook_notification_total),\n  by: {rh_wallet_wo_merchant_ref},\n  interval: 5m,\n  filter: {application == \"payment-orchestrator\" \n          AND in(rh_wallet_wo_status, { \"Success\", \"Failure\", \"Amount_on_hold\" })\n          AND rh_wallet_wo_flag == \"true\"}\n], kind:inner, on: {rh_wallet_wo_merchant_ref}, prefix:\"webhook.\"\n| fieldsAdd diff = abs(total[] - webhook.total[])\n| fields interval, timeframe, diff, rh_wallet_wo_merchant_ref\n                ",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "end",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "diff"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "diff"
                        ],
                        "leftYAxisSettings": {},
                        "tooltip": {
                            "variant": "shared"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        },
                        "seriesOverrides": []
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "diff"
                                ],
                                "value": "sparkline",
                                "id": 1745238773717
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 22
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "4": {
                "title": "Checkout Failure Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"FAILURE\"}) \nAND matchesValue(gateway,\"CHECKOUT\")}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "always",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxisLabel": "reason,merchant",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "seriesDisplayMode": "single-line",
                            "variant": "single"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 22
                    },
                    "dataMapping": {
                        "value": "interval"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "interval"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "5": {
                "title": "Charge Card Overall Success Rate per Gateway",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND state == \"SUCCESSFUL\"\n        AND transaction_type == \"SALE\"\n        AND paymentinformationtype != \"APPLE_PAY\"\n    }\n| join [\n    timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 1, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND state == \"SUCCESSFUL\"\n        AND transaction_type == \"COMPLETE\"\n        AND parenttransactiontype == \"SALE\"\n        AND paymentinformationtype != \"APPLE_PAY\"\n    }\n], kind: inner, on: {gateway}, prefix: \"complete.\"\n| join [\n    timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND transaction_type == \"SALE\"\n        AND in(state, {\"REDIRECT_SHOPPER\", \"SUCCESSFUL\", \"ERROR\", \"FAILURE\"})\n        AND paymentinformationtype != \"APPLE_PAY\"\n    }\n], kind: inner, on: {gateway}, prefix: \"totalAttempts.\"\n| fieldsAdd total_sale_txn = if(isNull(total[]) or total[] == 0, complete.total[], else: total[])\n| fieldsAdd total_attempt = if(isNotNull(totalAttempts.total[]) or totalAttempts.total[] != 0, totalAttempts.total[], else: 1)\n| fieldsAdd charge_card_sr_raw = 100 * (total_sale_txn[] / total_attempt[])\n| fieldsAdd charge_card_sr = if(charge_card_sr_raw[] > 100, 100, else: charge_card_sr_raw[])\n| fields interval, timeframe, charge_card_sr, gateway",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "charge_card_sr"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "charge_card_sr"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "charge_card_sr"
                                ],
                                "value": "sparkline",
                                "id": 1745313654220
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "6": {
                "title": "Success Rate Apple Pay",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 1, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND parenttransactiontype != \"AUTH\"\n        AND transaction_type != \"COMPLETE\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND state == \"SUCCESSFUL\"\n    }\n| join [\n    timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 1, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND transaction_type != \"COMPLETE\"\n        AND parenttransactiontype != \"AUTH\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n    }\n], kind: inner, on: {gateway}, prefix: \"totalTxn.\"\n| fieldsAdd success_txn = if(isNull(total[]), 0, else: total[])\n| fieldsAdd total_txn = if(isNotNull(totalTxn.total[]) or totalTxn.total[] != 0, totalTxn.total[], else: 1)\n| fieldsAdd applepay_sr_raw = 100 * (success_txn[] / total_txn[])\n| fieldsAdd applepay_sr = if(applepay_sr_raw[] > 100, 100, else: applepay_sr_raw[])\n| fields interval, timeframe, applepay_sr, gateway",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "applepay_sr"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "applepay_sr"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "applepay_sr"
                                ],
                                "value": "sparkline",
                                "id": 1745313985753
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "7": {
                "title": "Apple Pay Errors",
                "type": "data",
                "query": "// sum(increase(payment_processing_switch_counter_payment_transaction_total{application=~\"payment-processing-switch\",paymentInformationType=\"APPLE_PAY\", state = \"FAILURE\"}[5m])) by (reason)\n\ntimeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\nby: {gateway, reason},\ninterval: 5m,\nfilter: {\n  application_name == \"payment-processing-switch\"\n  AND paymentinformationtype == \"APPLE_PAY\"\n  AND state == \"FAILURE\"\n}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "total"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1745319271710
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "8": {
                "title": "Auth Rate by Gateway (Add Card)",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 1, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND in(parenttransactiontype,{\"AUTH\", \"NONE\"})\n        AND in(transaction_type, {\"COMPLETE\", \"AUTH\"})\n        AND parenttransactiontype != \"SALE\"\n        AND state == \"SUCCESSFUL\"\n    }\n| join [\n    timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 1, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND transaction_type == \"AUTH\"\n        AND parenttransactiontype != \"SALE\"\n    }\n], kind: inner, on: {gateway}, prefix: \"totalTxn.\"\n| fieldsAdd success_txn = if(isNull(total[]), 0, else: total[])\n| fieldsAdd total_txn = if(isNotNull(totalTxn.total[]) or totalTxn.total[] != 0, totalTxn.total[], else: 1)\n| fieldsAdd applepay_sr_raw = 100 * (success_txn[] / total_txn[])\n| fieldsAdd applepay_sr = if(applepay_sr_raw[] > 100, 100, else: applepay_sr_raw[])\n| fields interval, timeframe, applepay_sr, gateway",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "applepay_sr"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "applepay_sr"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "applepay_sr"
                                ],
                                "value": "sparkline",
                                "id": 1745320148075
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "9": {
                "title": "Issuing Bank Failure Throughput",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(payment_processing_switch_counter_issuer_status_total), value.A = avg(payment_processing_switch_counter_issuer_status_total, scalar: true) }, by: { issuingbankname }, filter: { NOT matchesValue(issuingbankname, \"NONE\") AND matchesValue(application, \"payment-processing-switch\") AND NOT matchesValue(state, \"SUCCESSFUL\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(payment_processing_switch_counter_issuer_status_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_issuer_status_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_issuer_status_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_issuer_status_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745320307041
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 31
                    }
                },
                "querySettings": {
                    "maxResultRecords": 5000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 2,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "payment_processing_switch_counter_issuer_status_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "issuingbankname"
                            ],
                            "filter": "issuingbankname != \"NONE\"application = payment-processing-switch state != \"SUCCESSFUL\""
                        }
                    ],
                    "globalCommands": {}
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "title": "Acquiring Bank Failure Throughput",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(payment_processing_switch_counter_acquirer_status_total), value.A = avg(payment_processing_switch_counter_acquirer_status_total, scalar: true) }, by: { acquiringbankname }, filter: { matchesValue(application, \"payment-processing-switch\") AND NOT matchesValue(acquiringbankname, \"NONE\") AND NOT matchesValue(state, \"SUCCESSFUL\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_acquirer_status_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_acquirer_status_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_acquirer_status_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745320437107
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "payment_processing_switch_counter_acquirer_status_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "acquiringbankname"
                            ],
                            "filter": "application = payment-processing-switch acquiringbankname != \"NONE\" state != \"SUCCESSFUL\""
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "11": {
                "title": "[PP] Failed Transaction per Vertical",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(payment_processing_switch_counter_payment_transaction_total), value.A = avg(payment_processing_switch_counter_payment_transaction_total, scalar: true) }, by: { merchant }, filter: { matchesValue(application_name, \"payment-processing-switch\") AND matchesValue(state, { \"FAILURE\", \"ERROR\" }) }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745320568306
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 5000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 100,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "payment_processing_switch_counter_payment_transaction_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "merchant"
                            ],
                            "filter": "application_name = payment-processing-switch state in (FAILURE,ERROR) "
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "title": "[PO] Overall Transaction Failure count per merchant",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(rh_wallet_wo_transaction_total), value.A = avg(rh_wallet_wo_transaction_total, scalar: true) }, by: { rh_wallet_wo_merchant_ref }, filter: { matchesValue(application, \"payment-orchestrator\") AND matchesValue(rh_wallet_wo_status, \"Failure\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(rh_wallet_wo_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(rh_wallet_wo_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "rh_wallet_wo_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(rh_wallet_wo_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745320990004
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "rh_wallet_wo_transaction_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "rh_wallet_wo_merchant_ref"
                            ],
                            "filter": "application = payment-orchestrator rh_wallet_wo_status = Failure "
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "13": {
                "title": "[PO] Overall Transaction Status Counts",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(rh_wallet_wo_transaction_total), value.A = avg(rh_wallet_wo_transaction_total, scalar: true) }, by: { rh_wallet_wo_status }, filter: { matchesValue(application, \"payment-orchestrator\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(rh_wallet_wo_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(rh_wallet_wo_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "rh_wallet_wo_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(rh_wallet_wo_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745321056764
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "rh_wallet_wo_transaction_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "rh_wallet_wo_status"
                            ],
                            "filter": "application = payment-orchestrator "
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "14": {
                "title": "[PO] Overall Transaction success % by payment instrument",
                "type": "data",
                "query": "timeseries total = sum(rh_wallet_wo_transaction_total, default: 1),\nby:{rh_wallet_wo_payment_type},\ninterval: 1m,\nfilter:{\n    rh_wallet_wo_status == \"Success\"\n}\n| join [\n    timeseries total = sum(rh_wallet_wo_transaction_total, default: 1),\n        by:{rh_wallet_wo_payment_type},\n        interval: 1m,\n        filter:{\n            rh_wallet_wo_status == \"Success\"\n            OR rh_wallet_wo_status == \"Failure\"\n        }\n], kind: inner, on:{rh_wallet_wo_payment_type}, prefix: \"final.\"\n| fieldsAdd success_rate = 100 * (total[] / final.total[])\n| fields interval, timeframe, success_rate, rh_wallet_wo_payment_type",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "success_rate"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "success_rate"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "success_rate"
                                ],
                                "value": "sparkline",
                                "id": 1745321477232
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "Apple Pay Failure Throughput by Country",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\nby: {application_name},\ninterval: 5m,\nfilter: {\n  application_name == \"payment-processing-switch\"\n  AND paymentinformationtype == \"APPLE_PAY\"\n  AND matchesValue(merchant, \"*AED*\") \n  AND state == \"FAILURE\"\n}\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*SAR*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"KSA.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*JOD*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"JOD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*KWD*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"KWT.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*MAD*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"MAD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*BHD*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"BHD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*EGP*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"EGP.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*QAR*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"QAR.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n    by: {application_name},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND paymentinformationtype == \"APPLE_PAY\"\n        AND matchesValue(merchant, \"*PKR*\") \n        AND state == \"FAILURE\"\n    }\n], kind:outer, on:{application_name}, prefix:\"PAK.\"\n| fieldsAdd Dubai = total[]\n| fieldsAdd `Saudi Arabia` = KSA.total[]\n| fieldsAdd Jordan = JOD.total[]\n| fieldsAdd Pakistan = PAK.total[]\n| fieldsAdd Kuwait = KWT.total[]\n| fieldsAdd Bahrain = BHD.total[]\n| fieldsAdd Morocco = MAD.total[]\n| fieldsAdd Egypt = EGP.total[]\n| fieldsAdd Qatar = QAR.total[]\n| fields interval, timeframe,  Dubai, `Saudi Arabia`, Jordan, Kuwait, Bahrain, Egypt, Qatar, Morocco, Pakistan\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "bandChartSettings": {
                            "lower": "Dubai",
                            "upper": "Saudi Arabia"
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "Dubai",
                                "Saudi Arabia",
                                "Jordan",
                                "Kuwait",
                                "Bahrain",
                                "Egypt"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "Dubai",
                                    "Saudi Arabia",
                                    "Jordan",
                                    "Kuwait",
                                    "Egypt",
                                    "Bahrain"
                                ],
                                "value": "sparkline",
                                "id": 1745390877105
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "16": {
                "title": "Failed Transactions per Country",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\nby: { application_name },\nfilter: { \n  matchesValue(application_name, \"payment-processing-switch\")\n  AND matchesValue(merchant, \"*AED*\")\n}\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*PKR*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"PKR.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*USD*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"USD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*IQD*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"IQD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*KWD*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"KWD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*BHD*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"BHD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*MAD*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"MAD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*SAR*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"SAR.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*JOD*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"JOD.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*EGP*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"EGP.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*QAR*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"QAR.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*OMR*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"OMR.\"\n| join [\n  timeseries total = sum(payment_processing_switch_counter_payment_transaction_total),\n  by: { application_name },\n  filter: { \n    matchesValue(application_name, \"payment-processing-switch\")\n    AND matchesValue(merchant, \"*TRY*\")\n  }\n], kind: leftOuter, on:{application_name}, prefix:\"TRY.\"\n| fieldsAdd Dubai = total[]\n| fieldsAdd Pakistan = PKR.total[]\n| fieldsAdd `United States` = USD.total[]\n| fieldsAdd Iraq = IQD.total[]\n| fieldsAdd Kuwait = KWD.total[]\n| fieldsAdd Bahrain = BHD.total[]\n| fieldsAdd Morocco = MAD.total[]\n| fieldsAdd `Saudi Arabia` = SAR.total[]\n| fieldsAdd Egypt = EGP.total[]\n| fieldsAdd Jordan = JOD.total[]\n| fieldsAdd Qatar = QAR.total[]\n| fieldsAdd Oman = OMR.total[]\n| fieldsAdd Turkey = TRY.total[]\n| fields interval, timeframe, Dubai, Pakistan, `United States`, Iraq, Kuwait, Bahrain, Morocco, `Saudi Arabia`, Egypt, Jordan, Qatar, Oman\n, Turkey",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "bandChartSettings": {
                            "lower": "Dubai",
                            "upper": "Pakistan"
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "Dubai",
                                "Pakistan",
                                "United States",
                                "Iraq",
                                "Kuwait",
                                "Bahrain",
                                "Morocco",
                                "Saudi Arabia",
                                "Egypt",
                                "Jordan",
                                "Qatar",
                                "Oman"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "Dubai",
                                    "Pakistan",
                                    "United States",
                                    "Iraq",
                                    "Kuwait",
                                    "Bahrain",
                                    "Morocco",
                                    "Saudi Arabia",
                                    "Egypt",
                                    "Jordan",
                                    "Qatar",
                                    "Oman"
                                ],
                                "value": "sparkline",
                                "id": 1745391989749
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "17": {
                "title": "Adyen Failure Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"FAILURE\"}) \nAND matchesValue(gateway,\"ADYEN\")}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "avg(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "18": {
                "title": "Payrails Failure Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"FAILURE\"}) \nAND matchesValue(gateway,\"Payrails\")}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "avg(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "19": {
                "title": "[PP] Error State count / PG",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { gateway, state },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"ERROR\", \"FAILURE\", \"UNKNOWN\"}) }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "avg(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "20": {
                "title": "Adyen Error Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"ERROR\"}) \nAND matchesValue(gateway,\"ADYEN\")}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "avg(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "21": {
                "title": "Payrails Error Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"ERROR\" }) \nAND matchesValue(gateway,\"Payrails\")}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "avg(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "22": {
                "title": "Checkout Error Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"ERROR\" }) \nAND matchesValue(gateway,\"CHECKOUT\")}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "always",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "seriesDisplayMode": "single-line",
                            "variant": "single"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 22
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "23": {
                "title": "Adyen Defer Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"UNKNOWN\"}) \nAND matchesValue(gateway,\"ADYEN\")}",
                "visualization": "recordView",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "avg(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "24": {
                "title": "Checkout Defer Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"UNKNOWN\" }) \nAND matchesValue(gateway,\"CHECKOUT\")}",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "always",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        },
                        "legend": {
                            "position": "bottom",
                            "hidden": false
                        },
                        "tooltip": {
                            "seriesDisplayMode": "single-line",
                            "variant": "single"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "legend": {
                        "showLegend": false,
                        "position": "auto",
                        "ratio": 22
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "25": {
                "title": "Payrails Defer Count",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { reason },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"UNKNOWN\" }) \nAND matchesValue(gateway,\"Payrails\")}",
                "visualization": "recordView",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "avg(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745248543406
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "26": {
                "title": "Defer Count by Merchant",
                "type": "data",
                "query": "timeseries sum(payment_processing_switch_counter_payment_transaction_total),\nby: { merchant },\ninterval: 5m,\nfilter: { matchesValue(application_name, \"payment-processing-switch\")\nAND matchesValue(state, { \"UNKNOWN\" }) }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "sum(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1748327414499
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "27": {
                "title": "Success Rate Cards",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 1, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND ((parent_transaction_type == \"SALE\" AND transaction_type == \"COMPLETE\") OR\n        transaction_type == \"SALE\")\n        AND paymentinformationtype != \"APPLE_PAY\"\n        AND state == \"SUCCESSFUL\"\n    }\n| join [\n    timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 1, rate: 5m),\n    by: {gateway},\n    interval: 5m,\n    filter: {\n        application_name == \"payment-processing-switch\"\n        AND transaction_type == \"SALE\"\n        AND not matchesValue(state, {\"REDIRECT_SHOPPER, SUCCESSFUL, ERROR, FAILURE\"})\n        AND paymentinformationtype != \"APPLE_PAY\"\n    }\n], kind: inner, on: {gateway}, prefix: \"totalTxn.\"\n| fieldsAdd success_txn = if(isNull(total[]), 0, else: total[])\n| fieldsAdd total_txn = if(isNotNull(totalTxn.total[]) or totalTxn.total[] != 0, totalTxn.total[], else: 1)\n| fieldsAdd applepay_sr_raw = 100 * (success_txn[] / total_txn[])\n| fieldsAdd applepay_sr = if(applepay_sr_raw[] > 100, 100, else: applepay_sr_raw[])\n| fields interval, timeframe, applepay_sr, gateway",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "applepay_sr"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "applepay_sr"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "applepay_sr"
                                ],
                                "value": "sparkline",
                                "id": 1745313985753
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "28": {
                "title": "[PP] Success Transaction per Vertical",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(payment_processing_switch_counter_payment_transaction_total), value.A = avg(payment_processing_switch_counter_payment_transaction_total, scalar: true) }, by: { merchant }, filter: { matchesValue(application_name, \"payment-processing-switch\") AND matchesValue(state, \"SUCCESSFUL\") }\n| sort `sum(payment_processing_switch_counter_payment_transaction_total)` desc",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_payment_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_payment_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1745320568306
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 5000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 100,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "payment_processing_switch_counter_payment_transaction_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "merchant"
                            ],
                            "filter": "application_name = payment-processing-switch state = SUCCESSFUL "
                        }
                    ],
                    "globalCommands": {
                        "sort": {
                            "field": "`sum(payment_processing_switch_counter_payment_transaction_total)`",
                            "direction": "desc"
                        }
                    }
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "30": {
                "title": "[PP] Deferred Transactions per Vertical",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total), \nby: { merchant },\nfilter: { matchesValue(application_name, \"payment-processing-switch\") AND matchesValue(state, \"UNKNOWN\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxis": [
                                "merchant"
                            ],
                            "valueAxis": [
                                "interval"
                            ],
                            "categoryAxisLabel": "merchant",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single",
                            "isValueLabelVisible": false
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        },
                        "legend": {
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1748329205030
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "merchant"
                        },
                        "displayedFields": [
                            "merchant"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "dataMapping": {
                        "bucketValue": "sum(payment_processing_switch_counter_payment_transaction_total)",
                        "xAxis": "timeframe",
                        "yAxis": "merchant",
                        "value": "value.A"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "value.A"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 5000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 100,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "31": {
                "title": "[PP] IN_PROGRESS Transactions per Vertical",
                "type": "data",
                "query": "timeseries total = sum(payment_processing_switch_counter_payment_transaction_total), \nby: { merchant },\nfilter: { matchesValue(application_name, \"payment-processing-switch\") AND matchesValue(state, \"IN_PROGRESS\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "smooth",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxisLabel": "merchant",
                            "valueAxisLabel": "interval",
                            "tooltipVariant": "single",
                            "isValueLabelVisible": false
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(payment_processing_switch_counter_payment_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "payment_processing_switch_counter_payment_transaction_total"
                        },
                        "legend": {
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1748329205030
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "merchant"
                        },
                        "displayedFields": [
                            "merchant"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "bucketValue": "sum(payment_processing_switch_counter_payment_transaction_total)",
                        "xAxis": "timeframe",
                        "yAxis": "merchant",
                        "value": "value.A"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "value.A"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 5000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 100,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "32": {
                "title": "[PP] Adyen Latency",
                "type": "data",
                "query": "timeseries sum_latency = sum(cpay_pp_switch_gateway_adyen_seconds_sum, default: 0, rate: 1m),\n            by: {method},\n            interval: 1m,\n            filter: { matchesValue(application, \"payment-processing-switch\") }\n          | join [\n              timeseries count_latency = sum(cpay_pp_switch_gateway_adyen_seconds_count, default: 0, rate: 1m),\n                by: {method},\n                interval: 1m,\n                filter: { matchesValue(application, \"payment-processing-switch\") }\n          ], kind: inner, on: {method}, prefix: \"count.\"\n          | fieldsAdd avg_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)\n          | fieldsAdd event_name = concat(\"Adyen Latency > 8s - \", method)\n          | fieldsAdd entity.name = \"payment-processing-switch\"\n          | join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n          | fields interval, timeframe, avg_latency, method   ",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "avg_latency"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "avg_latency"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "avg_latency"
                                ],
                                "value": "sparkline",
                                "id": 1748330582088
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "33": {
                "title": "[PP] Checkout Latency",
                "type": "data",
                "query": "timeseries sum_latency = sum(cpay_pp_switch_gateway_checkout_seconds_sum, default: 0, rate: 1m),\n            by: {method},\n            interval: 1m,\n            filter: { matchesValue(application, \"payment-processing-switch\") }\n          | join [\n              timeseries count_latency = sum(cpay_pp_switch_gateway_checkout_seconds_count, default: 0, rate: 1m),\n                by: {method},\n                interval: 1m,\n                filter: { matchesValue(application, \"payment-processing-switch\") }\n          ], kind: inner, on: {method}, prefix: \"count.\"\n          | fieldsAdd avg_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)\n          | fieldsAdd event_name = concat(\"Checkout Latency > 3s - \", method)\n          | fieldsAdd entity.name = \"payment-processing-switch\"\n          | join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n          | fields interval, timeframe, avg_latency, method   ",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "avg_latency"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "avg_latency"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "avg_latency"
                                ],
                                "value": "sparkline",
                                "id": 1748330582088
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "34": {
                "title": "[PP] Payrails Latency",
                "type": "data",
                "query": "timeseries sum_latency = sum(cpay_pp_switch_gateway_payrails_seconds_sum, default: 0, rate: 1m),\n            by: {method},\n            interval: 1m,\n            filter: { matchesValue(application, \"payment-processing-switch\") }\n          | join [\n              timeseries count_latency = sum(cpay_pp_switch_gateway_payrails_seconds_count, default: 0, rate: 1m),\n                by: {method},\n                interval: 1m,\n                filter: { matchesValue(application, \"payment-processing-switch\") }\n          ], kind: inner, on: {method}, prefix: \"count.\"\n          | fieldsAdd avg_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)\n          | fieldsAdd event_name = concat(\"Payrails Latency > 4s - \", method)\n          | fieldsAdd entity.name = \"payment-processing-switch\"\n          | join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n          | fields interval, timeframe, avg_latency, method",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "avg_latency"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "avg_latency"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "avg_latency"
                                ],
                                "value": "sparkline",
                                "id": 1748330829079
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "36": {
                "title": "Food Success count per Payment Method",
                "type": "data",
                "query": "timeseries total = sum(rh_wallet_wo_transaction_total)\n,by: { rh_wallet_wo_payment_type }\n,filter: { matchesValue(rh_wallet_wo_status, \"Success\")\nAND matchesValue(rh_wallet_wo_merchant_ref, \"*food.settlement.careem.com*\") \nAND NOT matchesValue(rh_wallet_wo_transaction_type, \"*REFUND*\") }\n| fieldsAdd `count` = arraySum(total)\n| fieldsKeep rh_wallet_wo_payment_type, `count`",
                "visualization": "categoricalBarChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxis": [
                                "rh_wallet_wo_payment_type"
                            ],
                            "categoryAxisLabel": "rh_wallet_wo_payment_type",
                            "valueAxis": [
                                "count"
                            ],
                            "valueAxisLabel": "count",
                            "tooltipVariant": "single",
                            "isCategoryLabelVisible": false,
                            "isValueLabelVisible": false
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(rh_wallet_wo_transaction_total)"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "rh_wallet_wo_transaction_total"
                        },
                        "legend": {
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "value.A"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "value.A"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "37": {
                "title": "Food Failure count per Payment Method",
                "type": "data",
                "query": "timeseries total = sum(rh_wallet_wo_transaction_total)\n,by: { rh_wallet_wo_payment_type }\n,filter: { matchesValue(rh_wallet_wo_status, \"Failure\")\nAND matchesValue(rh_wallet_wo_merchant_ref, \"*food.settlement.careem.com*\") \nAND NOT matchesValue(rh_wallet_wo_transaction_type, \"*REFUND*\") }\n| fieldsAdd `count` = arraySum(total)\n| fieldsKeep rh_wallet_wo_payment_type, `count`",
                "visualization": "categoricalBarChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxis": [
                                "rh_wallet_wo_payment_type"
                            ],
                            "categoryAxisLabel": "rh_wallet_wo_payment_type",
                            "valueAxis": [
                                "count"
                            ],
                            "valueAxisLabel": "count",
                            "tooltipVariant": "single",
                            "isCategoryLabelVisible": false,
                            "isValueLabelVisible": false
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(rh_wallet_wo_transaction_total)"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "rh_wallet_wo_transaction_total"
                        },
                        "legend": {
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "dataMapping": {
                        "value": "value.A"
                    },
                    "label": {
                        "showLabel": true,
                        "label": "value.A"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "38": {
                "title": "Pending Payments today",
                "type": "data",
                "query": "timeseries { max(total_payment_active_today), max(total_payment_failed_today), max(total_payment_in_progress_today)},\nunion: TRUE\n| fieldsAdd active = arrayMax(`max(total_payment_active_today)`)\n| fieldsAdd failed = arrayMax(`max(total_payment_failed_today)`)\n| fieldsAdd in_progress = arrayMax(`max(total_payment_in_progress_today)`)\n| fieldsKeep active, failed, in_progress",
                "visualization": "table",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxis": [
                                "active",
                                "failed",
                                "in_progress"
                            ],
                            "categoryAxisLabel": "active,failed,in_progress",
                            "valueAxis": [
                                "active",
                                "failed",
                                "in_progress"
                            ],
                            "valueAxisLabel": "active,failed,in_progress",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "active",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "active",
                        "autoscale": true,
                        "sparklineSettings": {},
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "39": {
                "title": "Pending Payments yesterday",
                "type": "data",
                "query": "timeseries { max(total_payment_active_yesterday), max(total_payment_failed_yesterday), max(total_payment_in_progress_yesterday)},\nunion: TRUE\n| fieldsAdd active = arrayMax(`max(total_payment_active_yesterday)`)\n| fieldsAdd failed = arrayMax(`max(total_payment_failed_yesterday)`)\n| fieldsAdd in_progress = arrayMax(`max(total_payment_in_progress_yesterday)`)\n| fieldsKeep active, failed, in_progress",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxisLabel": "active,failed,in_progress",
                            "valueAxisLabel": "active,failed,in_progress",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "active",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "active",
                        "autoscale": true,
                        "sparklineSettings": {},
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "41": {
                "title": "Pending Payments Due Before 2 days",
                "type": "data",
                "query": "timeseries { max(total_payment_active_due_n_days), max(total_payment_failed_due_n_days), max(total_payment_in_progress_due_n_days)},\nunion: TRUE\n| fieldsAdd active = arrayMax(`max(total_payment_active_due_n_days)`)\n| fieldsAdd failed = arrayMax(`max(total_payment_failed_due_n_days)`)\n| fieldsAdd in_progress = arrayMax(`max(total_payment_in_progress_due_n_days)`)\n| fieldsKeep active, failed, in_progress",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear",
                            "categoryAxisLabel": "active,failed,in_progress",
                            "valueAxisLabel": "active,failed,in_progress",
                            "tooltipVariant": "single"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "active",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "recordField": "active",
                        "autoscale": true,
                        "sparklineSettings": {},
                        "alignment": "center",
                        "trend": {
                            "trendType": "auto",
                            "isVisible": true
                        },
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "42": {
                "title": "Overall Transaction status",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(rh_wallet_wo_transaction_total), value.A = avg(rh_wallet_wo_transaction_total, scalar: true) }, by: { rh_wallet_wo_status }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(rh_wallet_wo_transaction_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(rh_wallet_wo_transaction_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "rh_wallet_wo_transaction_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(rh_wallet_wo_transaction_total)"
                                ],
                                "value": "sparkline",
                                "id": 1748344152445
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "rh_wallet_wo_transaction_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "rh_wallet_wo_status"
                            ]
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "43": {
                "title": "Retryable Invoices per Merchant",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(rh_wallet_wo_retryability_total), value.A = avg(rh_wallet_wo_retryability_total, scalar: true) }, by: { rh_wallet_wo_merchant_ref }, filter: { rh_wallet_wo_is_retryable_invoice_retry == TRUE }",
                "visualization": "recordView",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "rh_wallet_wo_retryability_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "rh_wallet_wo_merchant_ref"
                            ],
                            "filter": "rh_wallet_wo_is_retryable_invoice_retry = true "
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "44": {
                "title": "[CARD] Transactions Failure Rate Deviation 10% from Yesterday",
                "type": "data",
                "query": "timeseries total = sum(rh_wallet_wo_transaction_total)\n, by: {application}\n,filter: { \nmatchesValue(rh_wallet_wo_status, \"Failure\")\nAND matchesValue(rh_wallet_wo_payment_type, \"_card\")\nAND NOT matchesValue(rh_wallet_wo_merchant_ref, \"*underpayment.cpay.careem.com*\")\nAND NOT matchesValue(rh_wallet_wo_error_code, { \"Card Expired\", \"Insufficient funds\", \"Invalid credentials provided by user\" })}\n| join [\n  timeseries total = sum(rh_wallet_wo_transaction_total)\n, by: {application}\n,filter: { \nnot matchesValue(rh_wallet_wo_status, {\"Success\", \"Failure\"})\nAND matchesValue(rh_wallet_wo_payment_type, \"_card\")\nAND NOT matchesValue(rh_wallet_wo_merchant_ref, \"*underpayment.cpay.careem.com*\")}\n], kind:outer, on:{application}, prefix:\"totall.\"\n| fieldsAdd totalFailure = if(isNotNull(total[]), total[], else: 0)\n| fieldsAdd total_attempt = if(isNull(totall.total[]) or totall.total[] == 0, 1, else: totall.total[])\n| fieldsAdd Failure_rate_deviation = 100 * (totalFailure[] / total_attempt[])\n| fields interval, timeframe, Failure_rate_deviation\n\n\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "Failure_rate_deviation"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "Failure_rate_deviation"
                                ],
                                "value": "sparkline",
                                "id": 1748345422460
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "45": {
                "title": "",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(rh_wallet_wo_retryability_total), value.A = avg(rh_wallet_wo_retryability_total, scalar: true) }, by: { rh_wallet_wo_invoice_type }, filter: { rh_wallet_wo_is_retryable_invoice_retry == TRUE }",
                "visualization": "recordView",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "rh_wallet_wo_retryability_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "rh_wallet_wo_invoice_type"
                            ],
                            "filter": "rh_wallet_wo_is_retryable_invoice_retry = true"
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "46": {
                "title": "[Lean] Payment Beneficiary Created",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(lean_webhook_paymentSourceBeneficiaryCreated_seconds_count), value.A = avg(lean_webhook_paymentSourceBeneficiaryCreated_seconds_count, scalar: true) }, by: { metric.key }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(lean_webhook_paymentSourceBeneficiaryCreated_seconds_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "lean_webhook_paymentSourceBeneficiaryCreated_seconds_count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(lean_webhook_paymentSourceBeneficiaryCreated_seconds_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748347140389
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "lean_webhook_paymentSourceBeneficiaryCreated_seconds_count",
                                "aggregation": "sum"
                            },
                            "by": [
                                "metric.key"
                            ],
                            "filter": ""
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "47": {
                "title": "[Lean] Payment Reconciliation Update",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(lean_webhook_paymentReconciliationUpdated_seconds_count), value.A = avg(lean_webhook_paymentReconciliationUpdated_seconds_count, scalar: true) }, by: { metric.key }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(lean_webhook_paymentReconciliationUpdated_seconds_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "lean_webhook_paymentSourceBeneficiaryCreated_seconds_count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "lean_webhook_paymentReconciliationUpdated_seconds_count",
                                "aggregation": "sum"
                            },
                            "by": [
                                "metric.key"
                            ],
                            "filter": ""
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "48": {
                "title": "[Lean] Payment Created",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(lean_webhook_paymentCreated_seconds_count), value.A = avg(lean_webhook_paymentCreated_seconds_count, scalar: true) }, by: { metric.key }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(lean_webhook_paymentCreated_seconds_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "lean_webhook_paymentSourceBeneficiaryCreated_seconds_count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "lean_webhook_paymentCreated_seconds_count",
                                "aggregation": "sum"
                            },
                            "by": [
                                "metric.key"
                            ],
                            "filter": ""
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "49": {
                "title": "[CleoPay] Failed refunds",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(cleopay_failed_refund_total), value.A = avg(cleopay_failed_refund_total, scalar: true) }, by: { metric.key }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(cleopay_failed_refund_total)"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(cleopay_failed_refund_total)"
                                ],
                                "value": "sparkline",
                                "id": 1748347431923
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "cleopay_failed_refund_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "metric.key"
                            ],
                            "filter": ""
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "50": {
                "title": "[PP] Random Charge Per Status",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(cpay_pp_core_random_charge_total), value.A = avg(cpay_pp_core_random_charge_total, scalar: true) }, by: { cpay_pp_core_card_verification_status }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(cpay_pp_core_random_charge_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(cpay_pp_core_random_charge_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "cpay_pp_core_random_charge_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(cpay_pp_core_random_charge_total)"
                                ],
                                "value": "sparkline",
                                "id": 1748347541519
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "cpay_pp_core_random_charge_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "cpay_pp_core_card_verification_status"
                            ]
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "51": {
                "title": "[PP][Core] Validation Exceptions",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(cpay_pp_core_exceptions_total), value.A = avg(cpay_pp_core_exceptions_total, scalar: true) }, by: { cpay_pp_core_error_code }, filter: { matchesValue(cpay_pp_core_error_exception_class, \"ValidationException'\") }",
                "visualization": "recordView",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "cpay_pp_core_exceptions_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "cpay_pp_core_error_code"
                            ],
                            "filter": "cpay_pp_core_error_exception_class = \"ValidationException'\""
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "54": {
                "title": "Downgraded 3ds transactions",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(payment_processing_switch_counter_downgraded_3ds_transactions_total), value.A = avg(payment_processing_switch_counter_downgraded_3ds_transactions_total, scalar: true) }, by: { metric.key }",
                "visualization": "recordView",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "payment_processing_switch_counter_downgraded_3ds_transactions_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "metric.key"
                            ]
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "55": {
                "title": "[Lean] Errors Count / API",
                "type": "data",
                "query": "timeseries { \nsum(dt.service.request.failure_count), \nerrorSum = sum(dt.service.request.failure_count, scalar: true) }, \nby: { endpoint.name, http.response.status_code }, \nfilter: { \nmatchesValue(k8s.cluster.name, \"prod-pay-eks\") \nAND matchesValue(entityAttr(dt.entity.service, \"entity.name\"), \"lean-integration\") \nAND http.response.status_code != 200 \nAND http.response.status_code != 201 }\n| sort  errorSum desc",
                "visualization": "table",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "endpoint",
                            "interval",
                            "http.response.status_code",
                            "errorSum",
                            "sum(dt.service.request.failure_count)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(dt.service.request.failure_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Service failure count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(dt.service.request.failure_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748424385869
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "56": {
                "title": "[PO] Errors Count / API",
                "type": "data",
                "query": "timeseries { \nsum(dt.service.request.failure_count), \nerrorSum = sum(dt.service.request.failure_count, scalar: true) }, \nby: { endpoint.name, http.response.status_code }, \nfilter: { \nmatchesValue(k8s.cluster.name, \"prod-pay-eks\") \nAND matchesValue(entityAttr(dt.entity.service, \"entity.name\"), \"payment-orchestrator\") \nAND http.response.status_code != 200 \nAND http.response.status_code != 201 }\n| sort  errorSum desc",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "endpoint",
                            "interval",
                            "http.response.status_code",
                            "errorSum",
                            "sum(dt.service.request.failure_count)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(dt.service.request.failure_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Service failure count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(dt.service.request.failure_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748424385869
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "57": {
                "title": "[PP][PS] Errors Count / API",
                "type": "data",
                "query": "timeseries { \nsum(dt.service.request.failure_count), \nerrorSum = sum(dt.service.request.failure_count, scalar: true) }, \nby: { endpoint.name, http.response.status_code }, \nfilter: { \nmatchesValue(k8s.cluster.name, \"prod-pay-eks\") \nAND matchesValue(entityAttr(dt.entity.service, \"entity.name\"), \"payment-processing-payment-service\") \nAND http.response.status_code != 200 \nAND http.response.status_code != 201 }\n| sort  errorSum desc",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "endpoint",
                            "interval",
                            "http.response.status_code",
                            "errorSum",
                            "sum(dt.service.request.failure_count)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(dt.service.request.failure_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Service failure count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(dt.service.request.failure_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748424385869
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "58": {
                "title": "[PP][Core] Errors Count / API",
                "type": "data",
                "query": "timeseries { \nsum(dt.service.request.failure_count), \nerrorSum = sum(dt.service.request.failure_count, scalar: true) }, \nby: { endpoint.name, http.response.status_code }, \nfilter: { \nmatchesValue(k8s.cluster.name, \"prod-pay-eks\") \nAND matchesValue(entityAttr(dt.entity.service, \"entity.name\"), \"payment-processing-core\") \nAND http.response.status_code != 200 \nAND http.response.status_code != 201 }\n| sort  errorSum desc",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "endpoint",
                            "interval",
                            "http.response.status_code",
                            "errorSum",
                            "sum(dt.service.request.failure_count)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(dt.service.request.failure_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Service failure count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(dt.service.request.failure_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748424385869
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "59": {
                "title": "[PP][Switch] Errors Count / API",
                "type": "data",
                "query": "timeseries { \nsum(dt.service.request.failure_count), \nerrorSum = sum(dt.service.request.failure_count, scalar: true) }, \nby: { endpoint.name, http.response.status_code }, \nfilter: { \nmatchesValue(k8s.cluster.name, \"prod-pay-eks\") \nAND matchesValue(entityAttr(dt.entity.service, \"entity.name\"), \"payment-processing-switch\") \nAND http.response.status_code != 200 \nAND http.response.status_code != 201 }\n| sort  errorSum desc",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "endpoint",
                            "interval",
                            "http.response.status_code",
                            "errorSum",
                            "sum(dt.service.request.failure_count)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(dt.service.request.failure_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "Service failure count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(dt.service.request.failure_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748424385869
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "60": {
                "title": "[PP] Gateway Failover",
                "type": "data",
                "query": "timeseries un_avalibilty_count = sum(payment_processing_switch_counter_partner_availability_total),\nby: {partner_type}, \nfilter: { matchesValue(is_available, \"FALSE\")\n AND matchesValue(application, \"payment-processing-switch\") },\n interval: 10m \n| fieldsAdd partner_name = partner_type\n| fieldsAdd entity.name = \"payment-processing-switch\" \n| fieldsAdd event_name = concat(\"Payment Gateway\", partner_name, \"is unavaliblw\")\n| join [fetch dt.entity.service], kind:leftOuter, prefix:\"dt_service.\", on:{entity.name}\n| fields interval, timeframe, un_avalibilty_count , partner_name",
                "visualization": "recordView",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle"
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "61": {
                "title": "[PP] VGS Exceptions",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(payment_service_vgs_tokenize_seconds_count), value.A = avg(payment_service_vgs_tokenize_seconds_count, scalar: true) }, by: { exception }, filter: { NOT matchesValue(exception, \"none\") }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(payment_service_vgs_tokenize_seconds_count)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_service_vgs_tokenize_seconds_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "payment_service_vgs_tokenize_seconds_count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_service_vgs_tokenize_seconds_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748425420757
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "payment_service_vgs_tokenize_seconds_count",
                                "aggregation": "sum"
                            },
                            "by": [
                                "exception"
                            ],
                            "filter": "exception != none "
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "62": {
                "title": "",
                "type": "data",
                "subType": "dql-builder-metrics",
                "query": "timeseries { sum(pay_lis_requests_lean_seconds_count), value.A = avg(pay_lis_requests_lean_seconds_count, scalar: true) }, by: { metric.key }",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(pay_lis_requests_lean_seconds_count)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(pay_lis_requests_lean_seconds_count)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "pay_lis_requests_lean_seconds_count"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(pay_lis_requests_lean_seconds_count)"
                                ],
                                "value": "sparkline",
                                "id": 1748425604292
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "pay_lis_requests_lean_seconds_count",
                                "aggregation": "sum"
                            },
                            "by": [
                                "metric.key"
                            ],
                            "filter": ""
                        }
                    ]
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "63": {
                "type": "data",
                "title": "Dynamic Deferred count by (Error_code, gateway, state)",
                "query": "timeseries { sum(payment_processing_switch_counter_dynamic_deferred_errors_total), value.A = avg(payment_processing_switch_counter_dynamic_deferred_errors_total, scalar: true) }, by: { gateway, state, error_code }",
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "payment_processing_switch_counter_dynamic_deferred_errors_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "gateway",
                                "state",
                                "error_code"
                            ]
                        }
                    ]
                },
                "subType": "dql-builder-metrics",
                "visualization": "lineChart",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(payment_processing_switch_counter_dynamic_deferred_errors_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(payment_processing_switch_counter_dynamic_deferred_errors_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "payment_processing_switch_counter_dynamic_deferred_errors_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(payment_processing_switch_counter_dynamic_deferred_errors_total)"
                                ],
                                "value": "sparkline",
                                "id": 1748792933196
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            },
            "64": {
                "type": "data",
                "title": "Recommendation Engine Circuit state by (status, merchant ref, currency)",
                "query": "timeseries { sum(rh_wallet_wo_recommendation_engine_total), value.A = avg(rh_wallet_wo_recommendation_engine_total, scalar: true) }, by: { rh_wallet_wo_recommendation_engine_status, rh_wallet_wo_merchant_ref, rh_wallet_wo_currency }",
                "queryConfig": {
                    "version": "13.6.4",
                    "subQueries": [
                        {
                            "id": "A",
                            "isEnabled": true,
                            "datatype": "metrics",
                            "metric": {
                                "key": "rh_wallet_wo_recommendation_engine_total",
                                "aggregation": "sum"
                            },
                            "by": [
                                "rh_wallet_wo_recommendation_engine_status",
                                "rh_wallet_wo_merchant_ref",
                                "rh_wallet_wo_currency"
                            ]
                        }
                    ]
                },
                "subType": "dql-builder-metrics",
                "visualization": "lineChart",
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                },
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval",
                            "value.A",
                            "sum(rh_wallet_wo_recommendation_engine_total)"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "sum(rh_wallet_wo_recommendation_engine_total)"
                            ]
                        },
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "rh_wallet_wo_recommendation_engine_total"
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "sum(rh_wallet_wo_recommendation_engine_total)"
                                ],
                                "value": "sparkline",
                                "id": 1748793006278
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                }
            }
        },
        "layouts": {
            "0": {
                "x": 0,
                "y": 61,
                "w": 24,
                "h": 6
            },
            "2": {
                "x": 0,
                "y": 193,
                "w": 24,
                "h": 6
            },
            "4": {
                "x": 8,
                "y": 12,
                "w": 8,
                "h": 6
            },
            "5": {
                "x": 12,
                "y": 67,
                "w": 12,
                "h": 6
            },
            "6": {
                "x": 0,
                "y": 67,
                "w": 12,
                "h": 6
            },
            "7": {
                "x": 7,
                "y": 0,
                "w": 17,
                "h": 6
            },
            "8": {
                "x": 0,
                "y": 73,
                "w": 24,
                "h": 6
            },
            "9": {
                "x": 0,
                "y": 79,
                "w": 12,
                "h": 7
            },
            "10": {
                "x": 12,
                "y": 79,
                "w": 12,
                "h": 7
            },
            "11": {
                "x": 0,
                "y": 42,
                "w": 24,
                "h": 7
            },
            "12": {
                "x": 0,
                "y": 36,
                "w": 24,
                "h": 6
            },
            "13": {
                "x": 0,
                "y": 107,
                "w": 24,
                "h": 6
            },
            "14": {
                "x": 0,
                "y": 113,
                "w": 24,
                "h": 6
            },
            "15": {
                "x": 12,
                "y": 119,
                "w": 12,
                "h": 8
            },
            "16": {
                "x": 0,
                "y": 119,
                "w": 12,
                "h": 8
            },
            "17": {
                "x": 0,
                "y": 12,
                "w": 8,
                "h": 6
            },
            "18": {
                "x": 16,
                "y": 12,
                "w": 8,
                "h": 6
            },
            "19": {
                "x": 0,
                "y": 6,
                "w": 24,
                "h": 6
            },
            "20": {
                "x": 0,
                "y": 18,
                "w": 8,
                "h": 6
            },
            "21": {
                "x": 16,
                "y": 18,
                "w": 8,
                "h": 6
            },
            "22": {
                "x": 8,
                "y": 18,
                "w": 8,
                "h": 6
            },
            "23": {
                "x": 0,
                "y": 24,
                "w": 8,
                "h": 6
            },
            "24": {
                "x": 8,
                "y": 24,
                "w": 8,
                "h": 6
            },
            "25": {
                "x": 16,
                "y": 24,
                "w": 8,
                "h": 6
            },
            "26": {
                "x": 0,
                "y": 127,
                "w": 24,
                "h": 6
            },
            "27": {
                "x": 0,
                "y": 133,
                "w": 24,
                "h": 6
            },
            "28": {
                "x": 0,
                "y": 86,
                "w": 24,
                "h": 7
            },
            "30": {
                "x": 0,
                "y": 100,
                "w": 24,
                "h": 7
            },
            "31": {
                "x": 0,
                "y": 93,
                "w": 24,
                "h": 7
            },
            "32": {
                "x": 0,
                "y": 139,
                "w": 8,
                "h": 6
            },
            "33": {
                "x": 8,
                "y": 139,
                "w": 8,
                "h": 6
            },
            "34": {
                "x": 16,
                "y": 139,
                "w": 8,
                "h": 6
            },
            "36": {
                "x": 0,
                "y": 151,
                "w": 12,
                "h": 6
            },
            "37": {
                "x": 12,
                "y": 151,
                "w": 12,
                "h": 6
            },
            "38": {
                "x": 0,
                "y": 199,
                "w": 5,
                "h": 2
            },
            "39": {
                "x": 0,
                "y": 201,
                "w": 5,
                "h": 2
            },
            "41": {
                "x": 0,
                "y": 203,
                "w": 5,
                "h": 2
            },
            "42": {
                "x": 0,
                "y": 157,
                "w": 24,
                "h": 6
            },
            "43": {
                "x": 0,
                "y": 163,
                "w": 15,
                "h": 6
            },
            "44": {
                "x": 0,
                "y": 169,
                "w": 24,
                "h": 6
            },
            "45": {
                "x": 15,
                "y": 163,
                "w": 9,
                "h": 6
            },
            "46": {
                "x": 0,
                "y": 175,
                "w": 8,
                "h": 6
            },
            "47": {
                "x": 8,
                "y": 175,
                "w": 8,
                "h": 6
            },
            "48": {
                "x": 16,
                "y": 175,
                "w": 8,
                "h": 6
            },
            "49": {
                "x": 0,
                "y": 181,
                "w": 24,
                "h": 6
            },
            "50": {
                "x": 0,
                "y": 187,
                "w": 24,
                "h": 6
            },
            "51": {
                "x": 5,
                "y": 199,
                "w": 8,
                "h": 6
            },
            "54": {
                "x": 13,
                "y": 199,
                "w": 11,
                "h": 6
            },
            "55": {
                "x": 16,
                "y": 49,
                "w": 8,
                "h": 6
            },
            "56": {
                "x": 0,
                "y": 49,
                "w": 16,
                "h": 6
            },
            "57": {
                "x": 0,
                "y": 55,
                "w": 8,
                "h": 6
            },
            "58": {
                "x": 8,
                "y": 55,
                "w": 8,
                "h": 6
            },
            "59": {
                "x": 16,
                "y": 55,
                "w": 8,
                "h": 6
            },
            "60": {
                "x": 0,
                "y": 0,
                "w": 7,
                "h": 6
            },
            "61": {
                "x": 0,
                "y": 145,
                "w": 8,
                "h": 6
            },
            "62": {
                "x": 8,
                "y": 145,
                "w": 16,
                "h": 6
            },
            "63": {
                "x": 0,
                "y": 30,
                "w": 12,
                "h": 6
            },
            "64": {
                "x": 12,
                "y": 30,
                "w": 12,
                "h": 6
            }
        },
        "importedWithCode": false,
        "settings": {}
    }