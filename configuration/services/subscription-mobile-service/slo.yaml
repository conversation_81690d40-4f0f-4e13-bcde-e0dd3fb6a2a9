apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: subscription-mobile-service-slo
  labels:
    service: subscription-mobile-service
    scope: global
spec:
  global:
    latency: 2600
  keyRequests:
  - name: start-subscription
    path: /start-subscription
    target: 99.95
    latency: 2600
  - name: renew
    path: /renew
    target: 99.95
    latency: 2600
  - name: renew-subscription
    path: /renew-subscription
    target: 99.95
    latency: 2600
  - name: cancel-subscription
    path: /cancel-subscription
    target: 99.95
    latency: 2600
  - name: verify-subscription
    path: /verify-subscription
    target: 99.95
    latency: 2600
  - name: renew-verify
    path: /renew/verify
    target: 99.95
    latency: 2600
  - name: subscription-status
    path: /subscription-status
    target: 99.95
    latency: 2600
  - name: update-subscription
    path: /update-subscription
    target: 99.95
    latency: 2600
  - name: superapp-profile
    path: /superapp-profile
    target: 99.95
    latency: 2600
  - name: subscription-partners
    path: /subscription-partners/<partnerId>/benefits
    target: 99.95
    latency: 2600
  failureDetection:
    exceptions:
      ignoredExceptions:
      - classPattern: org.springframework.http.converter.HttpMessageNotReadableException
        messagePattern: ''
