apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: utility-bills-partner-adaptor-service-slo
  labels:
    service: utility-bills-partner-adaptor-service
    scope: global
spec:
  global:
    latency: 10000
  keyRequests:
  - name: health
    path: /health
    target: 99.9
    latency: 1000
  - name: prometheus
    path: /prometheus
    target: 99.9
    latency: 1000
  - name: v1-billers
    path: /v1/billers
    target: 99.9
    latency: 1000
  - name: payment-upstream-trnx-id-trnxid
    path: /v1/payment/upstream-trnx-id/<trnxId>
    target: 99.9
    latency: 1000
  - name: upstream-trnx-id-trnxid-verify
    path: /v1/payment/upstream-trnx-id/<trnxId>/verify
    target: 99.9
    latency: 1000
  - name: partnerid-countrycode-send
    path: /v1/admin/report/<partnerId>/<countryCode>/send
    target: 99.9
    latency: 1000
  - name: balance-fetch-billerid
    path: /v1/balance/fetch/<billerId>
    target: 99.9
    latency: 4000
  - name: payment-process-billerid
    path: /v1/payment/process/<billerId>
    target: 99.9
    latency: 10000
  - name: bill-fetch-billerid
    path: /v1/bill/fetch/<billerId>
    target: 99.9
    latency: 10000
  failureDetection:
    httpErrorCodes: 401-599
