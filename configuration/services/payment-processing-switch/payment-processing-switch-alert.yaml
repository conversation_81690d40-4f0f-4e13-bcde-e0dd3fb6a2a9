---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: payment-processing-switch-anomaly-detection
  labels:
    scope: global
    service: payment-processing-switch
spec:
  anomalies:
    staticThreshold:
      overall-auth-success-rate-low:
        title: "[payment-processing-switch] Overall Auth Success Rate Below Baseline"
        description: "Low auth rate for payment-processing-switch < 25% over 3m"
        alertCondition: "BELOW"
        threshold: 25
        query: |
          timeseries auth_successful = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            by: {application_name},
            interval: 1m,
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(state, "SUCCESSFUL") AND matchesValue(transaction_type, { "AUTH", "COMPLETE" }) AND matchesValue(parenttransactiontype, { "AUTH", "NONE" }) AND NOT matchesValue(parenttransactiontype, "SALE") AND NOT matchesValue(reason, { "Transaction not Permitted to Cardholder", "Stolen Card - Pick Up", "Restricted Card", "Lost Card - Pick Up", "Invalid card number", "Invalid Card Number", "INSUFFICIENT_FUNDS : Transaction declined due to insufficient funds (51 : Not sufficient funds)", "Insufficient funds", "Insufficient Funds", "Incorrect PIN", "EXPIRED_CARD : Transaction declined due to expired card (54 : Expired card)", "Expired Card", "Exceeds Withdrawal Value/Amount Limits", "Exceeds Withdrawal Frequency Limit", "DECLINED : Transaction declined by issuer (61 : Exceeds withdrawal amount limits)", "DECLINED : Transaction declined by issuer (57 : Transaction not permitted to cardholder)", "DECLINED : Transaction declined by issuer (43 : Stolen Card, Pick up)", "DECLINED : Transaction declined by issuer (41 : Lost Card)", "DECLINED : Transaction declined by issuer (05 : Do not honour)", "DECLINED : Transaction declined by issuer (04 : Pick up card)", "Declined - Do Not Honour", "103 CVC is not the right length", "062 : Restricted card", "055 : Incorrect PIN (Personal Identification Number)", "051 : Insufficient funds - Limit exceeded override possible", "5 : Transaction Declined - Insufficient funds (Insufficient Funds)", "4 : Transaction Declined - Expired Card (Expired Card)", "FRAUD", "Pick Up Card (No Fraud)" }) }
          | join [
              timeseries auth_total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
                by: {application_name},
                interval: 1m,
                filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(transaction_type, { "AUTH" }) AND NOT matchesValue(parenttransactiontype, "SALE") AND NOT matchesValue(reason, { "Transaction not Permitted to Cardholder", "Stolen Card - Pick Up", "Restricted Card", "Lost Card - Pick Up", "Invalid card number", "Invalid Card Number", "INSUFFICIENT_FUNDS : Transaction declined due to insufficient funds (51 : Not sufficient funds)", "Insufficient funds", "Insufficient Funds", "Incorrect PIN", "EXPIRED_CARD : Transaction declined due to expired card (54 : Expired card)", "Expired Card", "Exceeds Withdrawal Value/Amount Limits", "Exceeds Withdrawal Frequency Limit", "DECLINED : Transaction declined by issuer (61 : Exceeds withdrawal amount limits)", "DECLINED : Transaction declined by issuer (57 : Transaction not permitted to cardholder)", "DECLINED : Transaction declined by issuer (43 : Stolen Card, Pick up)", "DECLINED : Transaction declined by issuer (41 : Lost Card)", "DECLINED : Transaction declined by issuer (05 : Do not honour)", "DECLINED : Transaction declined by issuer (04 : Pick up card)", "Declined - Do Not Honour", "103 CVC is not the right length", "062 : Restricted card", "055 : Incorrect PIN (Personal Identification Number)", "051 : Insufficient funds - Limit exceeded override possible", "5 : Transaction Declined - Insufficient funds (Insufficient Funds)", "4 : Transaction Declined - Expired Card (Expired Card)", "FRAUD", "Pick Up Card (No Fraud)" }) }
          ], kind: inner, on: {application_name}, prefix: "total."
          | fieldsAdd auth_success_rate_raw = 100 * (auth_successful[] / total.auth_total[])
          | fieldsAdd auth_success_rate = if(total.auth_total[] > 5 and auth_success_rate_raw[] > 100, 100, else: auth_success_rate_raw[])
          | fieldsAdd event_name = "Auth Success Rate Below 25%"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, auth_success_rate , dt_service.id, dt_service.entity.name, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "25"
        evaluationWindow: "30"
        eventName: "Auth Success Rate Low"
        dimensionNameProperty: "event_name"

      overall-charge-success-rate-low:
        title: "[payment-processing-switch] Overall Charge Success Rate Below Baseline"
        description: "Low charge rate for payment-processing-switch < 70% over 5m"
        alertCondition: "BELOW"
        threshold: 70
        query: |
          timeseries charge_success = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            by: {application_name},
            interval: 1m,
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(state, "SUCCESSFUL") AND ((matchesValue(transaction_type, "SALE")) OR (matchesValue(transaction_type, "COMPLETE") AND matchesValue(parenttransactiontype, "SALE"))) AND NOT matchesValue(reason, { "Transaction not Permitted to Cardholder", "Stolen Card - Pick Up", "Restricted Card", "Lost Card - Pick Up", "Invalid card number", "Invalid Card Number", "INSUFFICIENT_FUNDS : Transaction declined due to insufficient funds (51 : Not sufficient funds)", "Insufficient funds", "Insufficient Funds", "Incorrect PIN", "EXPIRED_CARD : Transaction declined due to expired card (54 : Expired card)", "Expired Card", "Exceeds Withdrawal Value/Amount Limits", "Exceeds Withdrawal Frequency Limit", "DECLINED : Transaction declined by issuer (61 : Exceeds withdrawal amount limits)", "DECLINED : Transaction declined by issuer (57 : Transaction not permitted to cardholder)", "DECLINED : Transaction declined by issuer (43 : Stolen Card, Pick up)", "DECLINED : Transaction declined by issuer (41 : Lost Card)", "DECLINED : Transaction declined by issuer (05 : Do not honour)", "DECLINED : Transaction declined by issuer (04 : Pick up card)", "Declined - Do Not Honour", "103 CVC is not the right length", "062 : Restricted card", "055 : Incorrect PIN (Personal Identification Number)", "051 : Insufficient funds - Limit exceeded override possible", "5 : Transaction Declined - Insufficient funds (Insufficient Funds)", "4 : Transaction Declined - Expired Card (Expired Card)", "FRAUD", "Pick Up Card (No Fraud)" }) }
          | join [
              timeseries charge_total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
                by: {application_name},
                interval: 1m,
                filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(transaction_type, "SALE") AND matchesValue(state, { "SUCCESSFUL", "ERROR", "FAILURE", "REDIRECT_SHOPPER" }) AND NOT matchesValue(reason, { "Transaction not Permitted to Cardholder", "Stolen Card - Pick Up", "Restricted Card", "Lost Card - Pick Up", "Invalid card number", "Invalid Card Number", "INSUFFICIENT_FUNDS : Transaction declined due to insufficient funds (51 : Not sufficient funds)", "Insufficient funds", "Insufficient Funds", "Incorrect PIN", "EXPIRED_CARD : Transaction declined due to expired card (54 : Expired card)", "Expired Card", "Exceeds Withdrawal Value/Amount Limits", "Exceeds Withdrawal Frequency Limit", "DECLINED : Transaction declined by issuer (61 : Exceeds withdrawal amount limits)", "DECLINED : Transaction declined by issuer (57 : Transaction not permitted to cardholder)", "DECLINED : Transaction declined by issuer (43 : Stolen Card, Pick up)", "DECLINED : Transaction declined by issuer (41 : Lost Card)", "DECLINED : Transaction declined by issuer (05 : Do not honour)", "DECLINED : Transaction declined by issuer (04 : Pick up card)", "Declined - Do Not Honour", "103 CVC is not the right length", "062 : Restricted card", "055 : Incorrect PIN (Personal Identification Number)", "051 : Insufficient funds - Limit exceeded override possible", "5 : Transaction Declined - Insufficient funds (Insufficient Funds)", "4 : Transaction Declined - Expired Card (Expired Card)", "FRAUD", "Pick Up Card (No Fraud)" }) }
          ], kind: inner, on: {application_name}, prefix: "total."
          | fieldsAdd charge_success_rate_raw = 100 * (charge_success[] / total.charge_total[] )
          | fieldsAdd charge_success_rate = if(charge_success_rate_raw[] > 100, 100, else: charge_success_rate_raw[])
          | fieldsAdd event_name = "Charge Success Rate Below 70%"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, charge_success_rate, dt_service.id, dt_service.entity.name, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "4"
        evaluationWindow: "5"
        eventName: "Charge Success Rate Low"
        dimensionNameProperty: "event_name"

      overall-fraud-rate-high:
        title: "[payment-processing-switch] Overall Fraud Rate Above Baseline"
        description: "High fraud rate for payment-processing-switch > 12% over 3m"
        alertCondition: "ABOVE"
        threshold: 12
        query: |
          timeseries fraud_errors = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            by: {application_name},
            interval: 1m,
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(state, {"ERROR", "FAILURE"}) AND matchesValue(reason, "FRAUD") }
          | join [
              timeseries sale_total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
                by: {application_name},
                interval: 1m,
                filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(transaction_type, "SALE") }
          ], kind: inner, on: {application_name}, prefix: "total."
          | fieldsAdd fraud_rate_raw = 100 * (fraud_errors[] / total.sale_total[])
          | fieldsAdd fraud_rate = if(fraud_rate_raw[] > 100, 100, else: fraud_rate_raw[])
          | fieldsAdd event_name = "Fraud Rate > 12%"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, total.sale_total, dt_service.id, dt_service.entity.name, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "4"
        evaluationWindow: "5"
        eventName: "Fraud Rate High"
        dimensionNameProperty: "event_name"

      add-card-down-cpay-card-aed:
        title: "[payment-processing-switch] Add Card Down CPAY-CARD-AED"
        description: "No AUTH transactions observed for CPAY-CARD-AED in 4 hour"
        alertCondition: "BELOW"
        threshold: 1
        query: |
          timeseries add_card_total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            by: {application_name},
            interval: 60m,
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(parenttransactiontype, "AUTH") AND matchesValue(merchant, {"*CPAY-CARD-AED*"}) }
          | fieldsAdd event_name = "Add Card Down - CPAY-CARD-AED in last 4 hours"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, add_card_total, dt_service.id, dt_service.entity.name, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "3"
        evaluationWindow: "4"
        eventName: "Add Card Down - CPAY-CARD-AED in last 4 hours"
        dimensionNameProperty: "event_name"

      add-card-down-cpay-card-sar:
        title: "[payment-processing-switch] Add Card Down CPAY-CARD-SAR"
        description: "No AUTH transactions observed for CPAY-CARD-SAR in 4 hours"
        alertCondition: "BELOW"
        threshold: 1
        query: |
          timeseries add_card_total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            by: {application_name},
            interval: 60m,
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(parenttransactiontype, "AUTH") AND matchesValue(merchant, {"*CPAY-CARD-SAR*"}) }
          | fieldsAdd event_name = "Add Card Down - CPAY-CARD-SAR in last 4 hours"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, add_card_total, dt_service.id, dt_service.entity.name, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "3"
        evaluationWindow: "4"
        eventName: "Add Card Down - CPAY-CARD-SAR in last 4 hours"
        dimensionNameProperty: "event_name"

      recurring-trx-failure-rate-high:
        title: "[payment-processing-switch] Recurring trx failure rate above threshold (50%)"
        description: "Recurring transaction failure rate > 50% in the last 10 minutes"
        alertCondition: "ABOVE"
        threshold: 50
        query: |
          timeseries recurring_failures = sum(payment_processing_switch_counter_recurring_payment_transaction_total, default: 0, rate: 1m),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(state, {"ERROR", "FAILURE"}) AND NOT matchesValue(category, "Customer Issue") }
          | join [
              timeseries recurring_total = sum(payment_processing_switch_counter_recurring_payment_transaction_total, default: 0, rate: 1m),
                by: {application},
                interval: 1m,
                filter: { matchesValue(application, "payment-processing-switch") AND NOT matchesValue(state, "IN_PROGRESS") }
          ], kind: inner, on: {application}, prefix: "total."
          | fieldsAdd fail_rate = 100 * (recurring_failures[] / total.recurring_total[])
          | fieldsAdd event_name = "Recurring trx failure rate > 50%"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, fail_rate, dt_service.id, dt_service.entity.name, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "8"
        evaluationWindow: "10"
        eventName: "Recurring Failure Rate High"
        dimensionNameProperty: "event_name"

      add-card-failure-rate-cpay-card:
        title: "[payment-processing-switch] Add card trx failure rate above 50% for CPAY-CARD in last hour"
        description: "Add card transaction failure rate above threshold (50%) for CPAY-CARD in the last hour"
        alertCondition: "ABOVE"
        threshold: 50
        query: |
          timeseries failures = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            by: {merchant},
            interval: 1m,
            filter: { matchesValue(application_name, "payment-processing-switch") AND NOT matchesValue(state, {"SUCCESSFUL", "REDIRECT_SHOPPER"}) AND matchesValue(transaction_type, {"AUTH", "COMPLETE"}) AND matchesValue(parenttransactiontype, {"AUTH", "NONE"}) AND NOT matchesValue(parenttransactiontype, "SALE") AND matchesValue(merchant, {"CPAY-CARD-AED", "CPAY-CARD-SAR"}) }
          | join [
              timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
                by: {merchant},
                interval: 1m,
                filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(transaction_type, {"AUTH", "COMPLETE"}) AND matchesValue(parenttransactiontype, {"AUTH", "NONE"}) AND NOT matchesValue(parenttransactiontype, "SALE") AND matchesValue(merchant, {"CPAY-CARD-AED", "CPAY-CARD-SAR"}) }
          ], kind: inner, on: {merchant}, prefix: "total."
          | fieldsAdd event_name = "Add Card Failure Rate > 50% for CPAY-CARD"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, failures, dt_service.id, dt_service.entity.name, entity.name, merchant, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "50"
        evaluationWindow: "60"
        eventName: "Add Card Failure Rate High"
        dimensionNameProperty: "event_name"

      issuer-failure-rate-high:
        title: "[payment-processing-switch] High Issuer Failure Rate > 60%"
        description: "Failure rate > 60% for major issuers in the last 15 minutes"
        alertCondition: "ABOVE"
        threshold: 60
        query: |
          timeseries issuer_failures = sum(payment_processing_switch_counter_issuer_status_total, default: 0),
            interval: 1m,
            by: {application, issuingbankname},
            filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(state, {"FAILURE", "ERROR", "UNKNOWN"}) AND matchesValue(issuingbankname, {"Al Rajhi Banking & Inv. Corp.", "EMIRATES NBD BANK (P.J.S.C.)", "HSBC BANK MIDDLE EAST", "Emirates Nbd Bank (P.J.S.C.)", "ABU DHABI COMMERCIAL BANK", "ARAB BANK PLC", "BANK AL ETIHAD", "MASHREQBANK", "AL RAJHI BANKING AND INVESTMENT CORP.", "EMIRATES NBD", "Abu Dhabi Commercial Bank", "Hsbc Bank Middle East", "CITIBANK, N.A.", "FIRST ABU DHABI BANK PJSC", "WIO BANK P.J.S.C", "STC BANK", "EMIRATES ISLAMIC BANK P.J.S.C." }) }
          | join [
              timeseries issuer_total = sum(payment_processing_switch_counter_issuer_transaction_status_total, default: 0),
                interval: 1m,
                by: {application, issuingbankname},
                filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(issuingbankname, {"Al Rajhi Banking & Inv. Corp.", "EMIRATES NBD BANK (P.J.S.C.)", "HSBC BANK MIDDLE EAST", "Emirates Nbd Bank (P.J.S.C.)", "ABU DHABI COMMERCIAL BANK", "ARAB BANK PLC", "BANK AL ETIHAD", "MASHREQBANK", "AL RAJHI BANKING AND INVESTMENT CORP.", "EMIRATES NBD", "Abu Dhabi Commercial Bank", "Hsbc Bank Middle East", "CITIBANK, N.A.", "FIRST ABU DHABI BANK PJSC", "WIO BANK P.J.S.C", "STC BANK", "EMIRATES ISLAMIC BANK P.J.S.C." }) }
          ], kind: inner, on: {application, issuingbankname}, prefix: "total."
          | fieldsAdd failure_percentage = 100 * ( issuer_failures[] / total.issuer_total[] )
          | fieldsAdd failure_rate =  if( (total.issuer_total[] > issuer_failures[] AND total.issuer_total[] > 5) , failure_percentage[] , else: 0)
          | fieldsAdd event_name = concat("High Issuer Failure Rate > 60% - ", issuingbankname)
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, failure_rate, dt_service.id, dt_service.entity.name, event_name, issuingbankname
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "12"
        evaluationWindow: "15"
        eventName: "High Issuer Failure Rate"
        dimensionNameProperty: "event_name"

      threeds-failure-rate-high:
        title: "[payment-processing-switch] 3DS Failures Over Threshold"
        description: "3DS-related failures exceeded threshold over 10 minutes"
        alertCondition: "ABOVE"
        threshold: 20
        query: |
          timeseries failures = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            interval: 1m,
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(state, {"FAILURE", "ERROR"}) AND matchesValue(transaction_type, "COMPLETE") AND matchesValue(parenttransactiontype, "SALE") }
          | fieldsAdd event_name = "3DS Failures > 20 in last 10m"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, failures, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "8"
        evaluationWindow: "10"
        eventName: "3DS Failure Rate High"
        dimensionNameProperty: "event_name"

      dynamic-defer-payment-triggered:
        title: "[payment-processing-switch] DYNAMIC DEFER PAYMENT TRIGGERED FOR MORE THAN 5 MINUTES"
        description: "Dynamic defer payment logic triggered more than once in last 10 minutes"
        alertCondition: "ABOVE"
        threshold: 1
        query: |
          timeseries defer_triggered = sum(payment_processing_switch_counter_dynamic_deferred_errors_total, default: 0),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") }
          | fieldsAdd event_name = "Dynamic Defer Payment Triggered > 5m"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "3"
        evaluationWindow: "5"
        eventName: "Dynamic Defer Payment Triggered"
        dimensionNameProperty: "event_name"

      kafka-throughput-delay-update-high:
        title: "[payment-processing-switch] Kafka Transaction Update Throughput Delay High"
        description: "Delay between publisher and consumer for Transaction Update exceeds 1000"
        alertCondition: "ABOVE"
        threshold: 1000
        query: |
          timeseries pub = sum(cpay_pp_switch_kafka_seconds_count, default: 0),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(class, {"*TransactionUpdateKafkaEventPublishe*"}) }
          | join [
              timeseries con = sum(cpay_pp_switch_kafka_seconds_count, default: 0),
                by: {application},
                interval: 1m,
                filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(class, {"*TransactionUpdateKafkaEventConsumer*"}) }
          ], kind: inner, on: {application}, prefix: "con."
          | fieldsAdd delay = pub[] - con.con[]
          | fieldsAdd event_name = "Kafka Update Throughput Delay > 1000"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, delay, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        eventName: "Kafka Update Throughput Delay High"
        dimensionNameProperty: "event_name"

      kafka-throughput-delay-modify-high:
        title: "[payment-processing-switch] Kafka Transaction Modify Throughput Delay High"
        description: "Delay between publisher and consumer for Transaction Modify exceeds 1000"
        alertCondition: "ABOVE"
        threshold: 1000
        query: |
          timeseries pub = sum(cpay_pp_switch_kafka_seconds_count, default: 0, rate: 1m),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(class, {"*TransactionModifyKafkaEventPublisher*"}) }
          | join [
              timeseries con = sum(cpay_pp_switch_kafka_seconds_count, default: 0, rate: 1m),
                by: {application},
                interval: 1m,
                filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(class, {"*TransactionModifyKafkaEventConsumer*"}) }
          ], kind: inner, on: {application}, prefix: "con."
          | fieldsAdd delay = pub[] - con.con[]
          | fieldsAdd event_name = "Kafka Modify Throughput Delay > 1000"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, delay, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        eventName: "Kafka Modify Throughput Delay High"
        dimensionNameProperty: "event_name"

      kafka-throughput-delay-request-high:
        title: "[payment-processing-switch] Kafka Transaction Request Throughput Delay High"
        description: "Delay between publisher and consumer for Transaction Request exceeds 1000"
        alertCondition: "ABOVE"
        threshold: 1000
        query: |
          timeseries pub = sum(cpay_pp_switch_kafka_seconds_count, default: 0, rate: 1m),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(class, {"*TransactionRequestKafkaEventPublisher*"}) }
          | join [
              timeseries con = sum(cpay_pp_switch_kafka_seconds_count, default: 0, rate: 1m),
                by: {application},
                interval: 1m,
                filter: { matchesValue(application, "payment-processing-switch") AND matchesValue(class, {"*TransactionRequestEventConsumer*"}) }
          ], kind: inner, on: {application}, prefix: "con."
          | fieldsAdd delay = pub[] - con.con[]
          | fieldsAdd event_name = "Kafka Request Throughput Delay > 1000"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, delay, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        eventName: "Kafka Request Throughput Delay High"
        dimensionNameProperty: "event_name"

      kafka-producer-latency-transaction-modify-high:
        title: "[payment-processing-switch] Kafka Transaction Modify Producer Latency High"
        description: "Kafka Transaction Modify Producer latency exceeds 0.1s"
        alertCondition: "ABOVE"
        threshold: 0.1
        query: |
          timeseries sum_latency = sum(cpay_pp_switch_kafka_seconds_sum, default: 0),
            by: {class, application},
            interval: 1m,
            filter: { matchesValue(class, {"*TransactionModifyKafkaEventPublisher*"}) AND matchesValue(application, "payment-processing-switch") }
          | join [
              timeseries count_latency = sum(cpay_pp_switch_kafka_seconds_count, default: 0),
                by: {class, application},
                interval: 1m,
                filter: { matchesValue(class, {"*TransactionModifyKafkaEventPublisher*"}) AND matchesValue(application, "payment-processing-switch") }
          ], kind: inner, on: {class, application}, prefix: "count."
          | fieldsAdd producer_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)
          | fieldsAdd event_name = "Transaction Modify Producer Latency > 0.1s"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, producer_latency, dt_service.id, dt_service.entity.name, class, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        eventName: "Kafka Transaction Modify Producer Latency High"
        dimensionNameProperty: "event_name"

      kafka-producer-latency-transaction-release-high:
        title: "[payment-processing-switch] Kafka Transaction Release Producer Latency High"
        description: "Kafka Transaction Release Producer latency exceeds 0.1s"
        alertCondition: "ABOVE"
        threshold: 0.1
        query: |
          timeseries sum_latency = sum(cpay_pp_switch_kafka_seconds_sum, default: 0),
            by: {class, application},
            interval: 1m,
            filter: { matchesValue(class, {"*TransactionReleaseKafkaEventPublisher*"}) AND matchesValue(application, "payment-processing-switch") }
          | join [
              timeseries count_latency = sum(cpay_pp_switch_kafka_seconds_count, default: 0),
                by: {class, application},
                interval: 1m,
                filter: { matchesValue(class, {"*TransactionReleaseKafkaEventPublisher*"}) AND matchesValue(application, "payment-processing-switch") }
          ], kind: inner, on: {class, application}, prefix: "count."
          | fieldsAdd producer_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)
          | fieldsAdd event_name = "Transaction Release Producer Latency > 0.1s"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, producer_latency, dt_service.id, dt_service.entity.name, class, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        eventName: "Kafka Transaction Release Producer Latency High"
        dimensionNameProperty: "event_name"

      kafka-producer-latency-transaction-complete-high:
        title: "[payment-processing-switch] Kafka Transaction Complete Producer Latency High"
        description: "Kafka Transaction Complete Producer latency exceeds 0.1s"
        alertCondition: "ABOVE"
        threshold: 0.1
        query: |
          timeseries sum_latency = sum(cpay_pp_switch_kafka_seconds_sum, default: 0, rate: 1m),
            by: {class, application},
            interval: 1m,
            filter: { matchesValue(class, {"*TransactionCompleteKafkaEventPublisher*"}) AND matchesValue(application, "payment-processing-switch") }
          | join [
              timeseries count_latency = sum(cpay_pp_switch_kafka_seconds_count, default: 0, rate: 1m),
                by: {class, application},
                interval: 1m,
                filter: { matchesValue(class, {"*TransactionCompleteKafkaEventPublisher*"}) AND matchesValue(application, "payment-processing-switch") }
          ], kind: inner, on: {class, application}, prefix: "count."
          | fieldsAdd producer_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)
          | fieldsAdd event_name = "Transaction Complete Producer Latency > 0.1s"
          | fieldsAdd entity.name = application
          | join [fetch dt.entity.service], kind: leftOuter, prefix: "dt_service.", on: {entity.name}
          | fields interval, timeframe, producer_latency, dt_service.id, dt_service.entity.name, class, entity.name, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        eventName: "Kafka Transaction Complete Producer Latency High"
        dimensionNameProperty: "event_name"

      gateway-failure-rate-payrails-high:
        title: "[payment-processing-switch] PAYRAILS Failure Rate Above Baseline"
        description: "PAYRAILS gateway failure rate > 45%"
        alertCondition: "ABOVE"
        threshold: 45
        query: |
          timeseries failures = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
            by: {application_name},
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(state, {"ERROR", "FAILURE"}) AND matchesValue(gateway, "PAYRAILS") }
          | join [
              timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0),
                by: {application_name},
                filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(gateway, "PAYRAILS") }
          ], kind: inner, on: {application_name}, prefix: "total."
          | fieldsAdd failure_rate = 100 * ( failures[] / total.total[] )
          | fieldsAdd event_name = "PAYRAILS Failure Rate > 45%"
          | fieldsAdd entity.name = application_name
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, failure_rate, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "3"
        evaluationWindow: "5"
        eventName: "PAYRAILS Failure Rate High"
        dimensionNameProperty: "event_name"

      gateway-downgraded-3ds-transactions-high:
        title: "[payment-processing-switch] Downgraded 3DS Transactions > 0"
        description: "One or more 3DS transactions were automatically downgraded to Non-3DS"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries downgraded_3ds = sum(payment_processing_switch_counter_downgraded_3ds_transactions_total, default: 0),
            by: {application},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") }
          | fieldsAdd event_name = "Downgraded 3DS Transactions > 0"
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, downgraded_3ds, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "3"
        evaluationWindow: "5"
        eventName: "Downgraded 3DS Transactions"
        dimensionNameProperty: "event_name"

      gateway-failure-rate-adyen-high:
        title: "[payment-processing-switch] ADYEN Failure Rate Above Baseline"
        description: "ADYEN gateway failure rate > 45%"
        alertCondition: "ABOVE"
        threshold: 45
        query: |
          timeseries failures = sum(payment_processing_switch_counter_payment_transaction_total, default: 0, rate: 1m),
           interval: 1m,
           by: {application_name},
           filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(state, {"ERROR", "FAILURE", "UNKNOWN"}) AND matchesValue(gateway, "*ADYEN*") }
          | join [
             timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0, rate: 1m),
               interval: 1m,
               by: {application_name},
               filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(gateway, "*ADYEN*") }
          ], kind: inner, on: {application_name}, prefix: "total."
          | fieldsAdd failure_rate = 100 * (failures[] / total.total[] )
          | fieldsAdd event_name = "ADYEN Failure Rate > 45%"
          | fieldsAdd entity.name = application_name
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, failure_rate, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "3"
        evaluationWindow: "5"
        eventName: "ADYEN Failure Rate High"
        dimensionNameProperty: "event_name"

      gateway-failure-rate-checkout-high:
        title: "[payment-processing-switch] CHECKOUT Failure Rate Above Baseline"
        description: "CHECKOUT gateway failure rate > 45%"
        alertCondition: "ABOVE"
        threshold: 45
        query: |
          timeseries failures = sum(payment_processing_switch_counter_payment_transaction_total, default: 0, rate: 1m),
            by: {application_name},
            filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(state, {"ERROR", "FAILURE", "UNKNOWN"}) AND matchesValue(gateway, "*CHECKOUT*") }
          | join [
              timeseries total = sum(payment_processing_switch_counter_payment_transaction_total, default: 0, rate: 1m),
                by: {application_name},
                filter: { matchesValue(application_name, "payment-processing-switch") AND matchesValue(gateway, "*CHECKOUT*") }
          ], kind: inner, on: {application_name}, prefix: "total."
          | fieldsAdd failure_rate = 100 * (failures[] / total.total[])
          | fieldsAdd event_name = "CHECKOUT Failure Rate > 45%"
          | fieldsAdd entity.name = application_name
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, failure_rate, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "3"
        evaluationWindow: "5"
        eventName: "CHECKOUT Failure Rate High"
        dimensionNameProperty: "event_name"

      gateway-payrails-method-failures:
        title: "[payment-processing-switch] Payrails API Method Failures > 0"
        description: "Payrails API methods failing with exceptions (any call in last minute)"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries failed_calls = sum(cpay_pp_switch_gateway_payrails_seconds_sum, default: 0),
            by: {method},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") AND NOT matchesValue(exception, "*none*") AND matchesValue(method, {"*getAccessToken*", "*modifyPayment*", "*getExecutionByIdAndWorkflowName*", "*createExecution*", "*tokenizeCard*", "*createInstrument*"}) }
          | fieldsAdd event_name = concat("Payrails Method Failure - ", method)
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, failed_calls, entity.name, event_name, dt_service.id, dt_service.entity.name, method
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "1"
        eventName: "Payrails Method Failure"
        dimensionNameProperty: "event_name"
        
      gateway-payrails-latency-high:
        title: "[payment-processing-switch] Payrails Latency > 4s"
        description: "One of Payrails APIs exceeded 4 seconds latency in last 10 minutes"
        alertCondition: "ABOVE"
        threshold: 4
        query: |
          timeseries sum_latency = sum(cpay_pp_switch_gateway_payrails_seconds_sum, default: 0, rate: 1m),
            by: {method},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") }
          | join [
              timeseries count_latency = sum(cpay_pp_switch_gateway_payrails_seconds_count, default: 0, rate: 1m),
                by: {method},
                interval: 1m,
                filter: { matchesValue(application, "payment-processing-switch") }
          ], kind: inner, on: {method}, prefix: "count."
          | fieldsAdd avg_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)
          | fieldsAdd event_name = concat("Payrails Latency > 4s - ", method)
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, avg_latency, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "8"
        evaluationWindow: "10"
        eventName: "Payrails Latency High"
        dimensionNameProperty: "event_name"
        
      gateway-adyen-latency-high:
        title: "[payment-processing-switch] Adyen Latency > 5s"
        description: "One of Adyen APIs exceeded 5 seconds latency in last 10 minutes"
        alertCondition: "ABOVE"
        threshold: 8
        query: |
          timeseries sum_latency = sum(cpay_pp_switch_gateway_adyen_seconds_sum, default: 0, rate: 1m),
            by: {method},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") }
          | join [
              timeseries count_latency = sum(cpay_pp_switch_gateway_adyen_seconds_count, default: 0, rate: 1m),
                by: {method},
                interval: 1m,
                filter: { matchesValue(application, "payment-processing-switch") }
          ], kind: inner, on: {method}, prefix: "count."
          | fieldsAdd avg_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)
          | fieldsAdd event_name = concat("Adyen Latency > 8s - ", method)
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, avg_latency, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "8"
        evaluationWindow: "10"
        eventName: "Adyen Latency High"
        dimensionNameProperty: "event_name"
      
      gateway-checkout-latency-high:
        title: "[payment-processing-switch] Checkout Latency > 3s"
        description: "One of Checkout APIs exceeded 4 seconds latency in last 10 minutes"
        alertCondition: "ABOVE"
        threshold: 4
        query: |
          timeseries sum_latency = sum(cpay_pp_switch_gateway_checkout_seconds_sum, default: 0, rate: 1m),
            by: {method},
            interval: 1m,
            filter: { matchesValue(application, "payment-processing-switch") }
          | join [
              timeseries count_latency = sum(cpay_pp_switch_gateway_checkout_seconds_count, default: 0, rate: 1m),
                by: {method},
                interval: 1m,
                filter: { matchesValue(application, "payment-processing-switch") }
          ], kind: inner, on: {method}, prefix: "count."
          | fieldsAdd avg_latency = sum_latency[] / if(count.count_latency[] > 0, count.count_latency[], else: 1)
          | fieldsAdd event_name = concat("Checkout Latency > 3s - ", method)
          | fieldsAdd entity.name = "payment-processing-switch"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, avg_latency, entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "CUSTOM_ALERT"
        violatingSamples: "8"
        evaluationWindow: "10"
        eventName: "Checkout Latency High"
        dimensionNameProperty: "event_name"

      payment-gateway-unavailable:
        title: "[payment-processing-switch] Payment gateway failover triggered"
        description: "Payment gateway failover triggered in last hour"
        alertCondition: "ABOVE"
        threshold: 1000
        query: |
          timeseries un_avalibilty_count = sum(payment_processing_switch_counter_partner_availability_total), 
          by: {partner_type},
          filter: { matchesValue(is_available, "FALSE") AND matchesValue(application, "payment-processing-switch") },
          interval: 1m
            | fieldsAdd partner_name = partner_type
            | fieldsAdd entity.name = "payment-processing-switch"
            | fieldsAdd event_name = concat("Payment Gateway ", partner_name, " is unavaliblw")
            | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
            | fields interval, timeframe, un_avalibilty_count , entity.name, event_name, dt_service.id, dt_service.entity.name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "entity.name"
        eventType: "ERROR_EVENT"
        violatingSamples: "5"
        evaluationWindow: "30"
        eventName: "Payment gateway failover triggered"
        dimensionNameProperty: "event_name"