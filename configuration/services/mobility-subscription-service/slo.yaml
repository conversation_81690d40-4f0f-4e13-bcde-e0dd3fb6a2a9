---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: mobility-subscription-service-slo
  labels:
    service: mobility-subscription-service
    scope: global
spec:
  global:
    latency: 3937
  keyRequests:
    - name: "benefits-metadata-inspect-cache"
      path: /benefits-metadata/inspect-cache
      target: 99.95
      latency: 3221
    - name: "benefits-metadata-create"
      path: /benefits-metadata/create
      target: 99.95
      latency: 3221
    - name: "user-benefits-benefitidentifier-eligible"
      path: /user-benefits/<id>/eligible
      target: 99.95
      latency: 44
    - name: "disable-expired-mot-benefits"
      path: /disable-expired-mot-benefits
      target: 99.95
      latency: 3937
    - name: "subscription-benefits-get-subscription-promo"
      path: /subscription-benefits/get-subscription-promo
      target: 99.95
      latency: 2147
    - name: "user-benefits-has-benefit"
      path: /user-benefits/has-benefit
      target: 99.95
      latency: 357

