---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: customer-order-experience-service-slo
  labels:
    service: customer-order-experience-service
    scope: global
spec:
  global:
    latency: 10000
  keyRequests:
    - name: "v1-orders"
      path: /v1/orders
      target: 99.95
      latency: 4300
    - name: "v1-orders-orderid"
      path: /v1/orders/<orderID>
      target: 99.95
      latency: 10000
    - name: "v1-orders-pre-charge"
      path: /v1/orders/pre-charge
      target: 99.95
      latency: 3600
    - name: "orders-orderid-replace-items"
      path: /v1/orders/<orderID>/replace-items
      target: 99.95
      latency: 2500
    - name: "orders-orderid-auto-replace-items"
      path: /v1/orders/<orderID>/auto-replace-items
      target: 99.95
      latency: 1800
    - name: "orders-orderid-status"
      path: /v1/orders/<orderID>/status
      target: 99.95
      latency: 540
    - name: "orders-orderid-user-action"
      path: /v1/orders/<orderID>/user-action
      target: 99.95
      latency: 110