---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: istio-workload-migration
  labels:
    scope: global
spec:
  name: "Istio Workload Migration"
  json: |
    {
        "version": 18,
        "variables": [
            {
                "key": "Cluster",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "fetch dt.entity.kubernetes_cluster\n| summarize clusters = collectDistinct(entity.name)\n| expand clusters\n| sort clusters",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "Namespace",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries total = max(istio_requests_total, rollup:avg, default:0), filter: {matchesValue(reporter, \"destination\")}, interval:1m , by: {destination_workload_namespace, destination_workload, k8s.cluster.name} | fieldsAdd namespace = destination_workload_namespace, deployment = destination_workload | fieldsRemove destination_workload_namespace, destination_workload\n| append  [timeseries total = sum(request_total, rollup:avg, default:0), nonempty:false, filter: {matchesValue(direction, \"inbound\")}, interval:1m , by: {namespace, deployment, k8s.cluster.name}] \n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name,\"-eks\") \n| filter matchesValue(k8s_cluster_name, $Cluster)\n| summarize count=count(), by: {namespace}\n| fieldsRemove count\n",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            },
            {
                "key": "Deployment",
                "visible": true,
                "type": "query",
                "version": 1,
                "editable": true,
                "input": "timeseries total = max(istio_requests_total, rollup:avg, default:0), filter: {matchesValue(reporter, \"destination\")}, interval:1m , by: {destination_workload_namespace, destination_workload, k8s.cluster.name} | fieldsAdd namespace = destination_workload_namespace, deployment = destination_workload | fieldsRemove destination_workload_namespace, destination_workload\n| append  [timeseries total = sum(request_total, rollup:avg, default:0), nonempty:false, filter: {matchesValue(direction, \"inbound\")}, interval:1m , by: {namespace, deployment, k8s.cluster.name}] \n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name,\"-eks\") \n| filter matchesValue(k8s_cluster_name, $Cluster)\n| filter matchesValue(namespace, $Namespace)\n| summarize count=count(), by: {deployment}\n| fieldsRemove count",
                "multiple": true,
                "defaultValue": [
                    "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
                ]
            }
        ],
        "tiles": {
            "1": {
                "title": "Traffic By Code",
                "type": "data",
                "query": "// ── Istio Requests by Response Code ───────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $Namespace) AND\n    matchesValue(destination_workload, $Deployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, response_code}\n| fieldsAdd code = response_code\n| fieldsRemove response_code\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n\n// ── Append Inbound Requests by Status Code ────────────────────────────────\n| append [\n\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $Namespace) AND\n      matchesValue(deployment, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, status_code}\n  | fieldsAdd code = status_code\n  | fieldsRemove status_code\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s_cluster_name, k8s.cluster.name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747772745963
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "2": {
                "title": "Traffic by Deployment",
                "type": "data",
                "query": "// ── Istio Requests Total by Destination Workload ─────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $Namespace) AND\n    matchesValue(destination_workload, $Deployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n| fieldsAdd deployment = destination_workload\n| fieldsRemove destination_workload\n\n// ── Append Inbound Request Totals by Deployment ──────────────────────────\n| append [\n\n  timeseries total = sum(request_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $Namespace) AND\n      matchesValue(deployment, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, deployment}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s_cluster_name, k8s.cluster.name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1747772979614
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "3": {
                "title": "Canary Traffic Split",
                "type": "data",
                "query": "timeseries weight = max(flagger_canary_weight, rollup:avg, default:0), \nfilter: { matchesValue(workload, $Namespace)}, interval:1m , by: {k8s.cluster.name, workload} \n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name,\"-eks\") \n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "weight"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "weight"
                                ],
                                "value": "sparkline",
                                "id": 1747773629802
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "4": {
                "type": "markdown",
                "content": "**Traffic**\n--- ---\n "
            },
            "5": {
                "type": "markdown",
                "content": "**Latency**\n--- --- \n "
            },
            "6": {
                "title": "Latency",
                "type": "data",
                "query": "// ── Istio 99th Percentile Latency (Destination Perspective) ──────────────\ntimeseries latency = percentile(istio_request_duration_milliseconds_latency, rollup:avg, 99),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $Namespace) AND\n    matchesValue(destination_workload, $Deployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n| fieldsAdd namespace = destination_workload_namespace\n| fieldsRemove destination_workload_namespace\n\n// ── Append Inbound 99th Percentile Latency ───────────────────────────────\n| append [\n\n  timeseries latency = percentile(response_latency_ms_latency, rollup:avg, 99),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $Namespace) AND\n      matchesValue(deployment, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s_cluster_name, k8s.cluster.name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "latency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "latency"
                                ],
                                "value": "sparkline",
                                "id": 1747986400754
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "latency",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747773973477
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "7": {
                "title": "Latency by Deployment",
                "type": "data",
                "query": "// ── Istio 99th Percentile Latency by Deployment ──────────────────────────\ntimeseries latency = percentile(istio_request_duration_milliseconds_latency, rollup:avg, 99),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $Namespace) AND\n    matchesValue(destination_workload, $Deployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s_cluster_name, k8s.cluster.name\n| fieldsAdd deployment = destination_workload\n| fieldsRemove destination_workload\n\n// ── Append Inbound 99th Percentile Latency by Deployment ─────────────────\n| append [\n\n  timeseries latency = percentile(response_latency_ms_latency, rollup:avg, 99),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $Namespace) AND\n      matchesValue(deployment, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, deployment}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s_cluster_name, k8s.cluster.name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 0,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-ideal-default, #2f6863)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                },
                                {
                                    "id": 1,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-warning-default, #eca440)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                },
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": ""
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "end",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "latency"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": true,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "latency"
                                ],
                                "value": "sparkline",
                                "id": 1747774087210
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "latency",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747774105495
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "9": {
                "title": "Problems",
                "type": "data",
                "query": "fetch dt.davis.problems\n| filter matchesValue(k8s.cluster.name, $Cluster)\n| filter matchesValue(k8s.namespace.name, $Namespace) \n| filter matchesValue(event.status, \"ACTIVE\") \n| fields timestamp, k8s.namespace.name, k8s.workload.name, event.category, event.name, event.description, count=toLong(1)",
                "visualization": "table",
                "visualizationSettings": {
                    "autoSelectVisualization": false,
                    "thresholds": [
                        {
                            "id": 1,
                            "field": "count",
                            "title": "",
                            "isEnabled": true,
                            "rules": [
                                {
                                    "id": 2,
                                    "color": {
                                        "Default": "var(--dt-colors-charts-status-critical-default, #c4233b)"
                                    },
                                    "comparator": "≥",
                                    "label": "",
                                    "value": 0
                                }
                            ]
                        }
                    ],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "auto",
                        "xAxisLabel": "timestamp",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "resolved_problem_duration"
                        ],
                        "fieldMapping": {
                            "timestamp": "timestamp",
                            "leftAxisValues": [
                                "count"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [],
                        "columnOrder": [
                            "[\"timestamp\"]",
                            "[\"k8s.workload.name\"]",
                            "[\"event.category\"]",
                            "[\"event.name\"]",
                            "[\"event.description\"]",
                            "[\"k8s.namespace.name\"]",
                            "[\"count\"]"
                        ],
                        "enableThresholdInRow": true,
                        "selectedColumnForRowThreshold": "count"
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "event.id"
                        },
                        "displayedFields": [
                            "event.id",
                            "event.status",
                            "event.category",
                            "dt.davis.mute.status",
                            "event.status_transition",
                            "display_id",
                            "event.name",
                            "event.kind",
                            "event.description",
                            "root_cause_entity_id",
                            "root_cause_entity_name"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    }
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "componentState": {
                        "selectedAnalyzerName": "dt.statistics.ui.anomaly_detection.StaticThresholdAnomalyDetectionAnalyzer",
                        "inputData": {
                            "dt.statistics.ui.anomaly_detection.StaticThresholdAnomalyDetectionAnalyzer": {
                                "generalParameters": {
                                    "timeframe": {
                                        "startTime": "2025-06-02T04:49:53.372Z",
                                        "endTime": "2025-06-02T06:49:53.373Z"
                                    },
                                    "resolveDimensionalQueryData": true,
                                    "logVerbosity": "INFO"
                                },
                                "threshold": 0,
                                "alertCondition": "ABOVE",
                                "alertOnMissingData": false,
                                "violatingSamples": 3,
                                "slidingWindow": 5,
                                "dealertingSamples": 5,
                                "query": "fetch dt.davis.problems\n| filter matchesValue(k8s.cluster.name, \"control-prod-eks\",\"data-prod-rh-eks\",\"prod-logging-eks\",\"prod-mot-eks\",\"prod-pay-eks\",\"prod-rh-eks\",\"prod-rides-eks\",\"prod-shared-services-eks\")\n| filter matchesValue(k8s.namespace.name, \"account-balance-service\",\"account-management-service\",\"account-orchestrator-service\",\"activities-notification-service\",\"activities-sdui-service\",\"activity-service\",\"admin-order-experience-service\",\"ai-care\",\"ai-support\",\"alert-management-service\",\"alert-service\",\"alfred\",\"amaken-admin-webapp\",\"analytika-router-uber\",\"anubis\",\"appengine-service\",\"arbitration-service\",\"argus-service-mirror\",\"ask-data-bot\",\"ask-data-engine\",\"ask-data-ui\",\"authentication-service\",\"authorization-engine\",\"autosuggestion-service\",\"bab-integration-service\",\"bank-account-management-service\",\"basket-service-v2\",\"batching-service\",\"benefit-service\",\"benefits-ingestor\",\"bike-experience-service\",\"bike-ops-web\",\"bike-subscription-consumer\",\"billing-adapter-service\",\"booking-integration-service\",\"booking-kafka-events-service\",\"bookmark-usl-service\",\"braze-content-service\",\"braze-gateway\",\"business-identity-service\",\"business-recharge-service\",\"captain-account-deletion\",\"captain-analytics-service\",\"captain-api-gateway\",\"captain-availability-service\",\"captain-blocking-service\",\"captain-comms-service\",\"captain-communication-service\",\"captain-earning-verification-service\",\"captain-engagement-experience\",\"captain-experience-service\",\"captain-identity-service\",\"captain-login-service\",\"captain-portal\",\"captain-pricing-service\",\"captain-rewards-engine\",\"captain-rewards-service\",\"captain-shift-service\",\"captain-trip-api\",\"captain-unblocking-service\",\"care-content-service\",\"care-flowable-ui\",\"care-search-service\",\"care-transactions-service\",\"care-zendesk-service\",\"careem-public-web-api\",\"careem-public-web-app\",\"careem-pubweb-microservice\",\"cashout-service\",\"catalog-quality\",\"catalog-request\",\"catalog-staging\",\"cdc-maxwell\",\"cfconfig-service\",\"challenge-service\",\"chargeback-engine\",\"chat-service\",\"chewbacca\",\"citc-service\",\"cleopay-service\",\"clients-service\",\"cocktail-service\",\"colosseum\",\"commercial-tooling-service\",\"communication-fabric\",\"communication-management\",\"communication-priority-sender\",\"communication-recipients\",\"communication-sender\",\"compliance-reporting-service\",\"control-center\",\"corporate-payment-service\",\"corporate-profile-service\",\"currency-conversion-service\",\"customer-engagement-experience\",\"customer-order-experience-service\",\"customer-pricing-service\",\"deliveries-eta-service\",\"device-management-service\",\"devops-webhook-handler\",\"dineout-activity-publisher\",\"dineout-assistant\",\"dineout-assistant-ui\",\"discovery-service\",\"dispatch-service\",\"document-expiry-service\",\"donations-service\",\"eagle-service\",\"eand-adapter\",\"earning-dashboard\",\"easypaisa-adapter\",\"echo\",\"ecom-facade-service\",\"efawateercom-adapter\",\"egypt-tax-reporting-service\",\"elm-trip-registration\",\"engagement-management\",\"espresso\",\"eta-central-service\",\"eta-service\",\"event-router\",\"explore-client-service\",\"explore-listings-service\",\"explore-search-service\",\"explore-service\",\"express-merchant-profile-service\",\"fab-integration-service\",\"fawry-adapter\",\"feedback-orchestrator\",\"feedback-orchestrator-ml\",\"firmina\",\"flagger-system\",\"flash-api\",\"food-activity-publisher\",\"food-eta\",\"food-expansion-be\",\"food-expansion-management\",\"fp-abacus\",\"fp-abacus-dev-staging\",\"fp-metastore\",\"fp-metastore-dev-staging\",\"fraud-service\",\"fs-feature-serving\",\"fs-feature-serving-dev-staging\",\"fs-orchestrator-service\",\"fs-orchestrator-service-dev-staging\",\"fs-persistence-service\",\"fs-persistence-service-dev-staging\",\"galileo-server\",\"galileo-ui\",\"geocoder-service\",\"github-webhook-handler\",\"global-checkout\",\"global-tipping-service\",\"google-trip-id-service\",\"gopher\",\"groceries-eta\",\"grocery-activity-publisher\",\"grocery-search-experience\",\"growth-comms-apis\",\"growth-comms-regulator\",\"grpcecho-demo\",\"guard-service\",\"hala-whatsapp-bot\",\"hypergate\",\"hypergate-dev\",\"hyperstore\",\"hyperstore-ro\",\"identity-search-service\",\"idp-service\",\"incentive-inbox\",\"incentive-service\",\"incident-control-tower\",\"incident-management-bot\",\"ingestor\",\"inventory-management-service\",\"invoice-adapter-service\",\"invoice-reporting-service\",\"iris\",\"itc-adapter-service\",\"jirassic\",\"kamino\",\"kepler\",\"khazendar-app\",\"kong-ingress\",\"ksa-tax-reporting-service\",\"kubera-service\",\"kyc-service\",\"launcher-management\",\"launcher-tile-service\",\"lead-management-service\",\"lean-integration\",\"legacy-user-service\",\"limits-service\",\"linkerd\",\"linkerd-smi\",\"linkerd-viz\",\"localization-service\",\"location-resolution-service\",\"location-service\",\"login-service\",\"loyalty-service-v2\",\"lp-adapter-service\",\"lp-shipment-service\",\"lulu-integration-service\",\"map-service\",\"marilyn-adapter\",\"marilyn-authorization\",\"marilyn-service\",\"marilyn-shard-service\",\"marketing-data-provider\",\"masar-service\",\"master-product-service\",\"matcher-service\",\"maxwell-profile-service\",\"media-platform\",\"merchant-analytics-service\",\"merchant-city-area-mapping\",\"merchant-event-service\",\"merchant-notification-dispatcher\",\"merchant-notification-processor\",\"merchant-onboarding-service\",\"merchant-order-service\",\"merchants\",\"mindhar\",\"mobility-partner-service\",\"mobility-subscription-service\",\"mot-logistics-adapter\",\"navigation-service\",\"notiverse\",\"oa-activity-publisher\",\"oa-experience-service\",\"odin-eye\",\"onboarder-service\",\"one-click-checkout\",\"onecard-adapter\",\"online-checkout-hosted\",\"online-checkout-webapp\",\"optimus-api\",\"order-core-service\",\"order-tracking-service\",\"orders-export-service\",\"orion\",\"osiris\",\"osrm-service-1\",\"osrm-service-2\",\"osrm-service-4\",\"osrm-service-5\",\"otp-service\",\"p2p-service\",\"page-oncall\",\"partner-analytics-service\",\"partner-billing-service\",\"partner-payment-service\",\"partner-portal\",\"password-recovery-service\",\"payment-orchestrator\",\"payment-orchestrator-notifications-service\",\"payment-processing-core\",\"payment-processing-payment-service\",\"payment-processing-switch\",\"payment-service\",\"peak-api\",\"peak-api-staging\",\"peak-config-api\",\"peak-config-api-staging\",\"peak-handler\",\"peak-handler-staging\",\"peak-octopus\",\"peak-octopus-staging\",\"pi-feature-service\",\"picker-service\",\"pie-trigger-service\",\"pie-trigger-service-experimental\",\"ping-api-service\",\"platform-partners-service\",\"plt-ads-portal-api\",\"plt-ads-service\",\"plt-ads-tracking-service\",\"plt-search-service\",\"pos\",\"pos-custom-integrations\",\"pricing-calculation\",\"pricing-configuration-service\",\"pricing-estimation\",\"pricing-mot\",\"profile-service\",\"profile-store\",\"promotion-usage-tracker\",\"qe-service\",\"quik-dashboard\",\"raptor-test-service\",\"referral-service\",\"remittance-service\",\"rey\",\"ride-activity-publisher\",\"ride-communication-service\",\"ride-engine\",\"ride-update-orchestrator\",\"rides-fulfillment-service\",\"rides-platform-routing-service\",\"rides-tracking-service\",\"routing-navigation-adapter\",\"rumi-adapter-service\",\"rumi-care-service\",\"saindexer\",\"screen-be\",\"screen-fe\",\"search-experience-service\",\"search-indexer\",\"segment-builder-api\",\"segment-builder-orchestrator\",\"self-service-backend\",\"settlement-service\",\"sg-comms-service\",\"shorturl-devx\",\"sm-okai-iot-service\",\"sm-omni-iot-service\",\"sre-lockout-service\",\"staff-management-service\",\"store-management-service\",\"storeops-service\",\"subscription-billing-service\",\"subscription-communication-service\",\"subscription-jwt-registry\",\"subscription-mobile-service\",\"subscription-plans-service\",\"subscription-refund-service\",\"subscription-renewal-service\",\"subscription-savings-service\",\"subscription-segments-service\",\"subscription-sentinel-service\",\"subscription-service\",\"subscription-top-up-service\",\"superapp-partner-events-collector\",\"superportal\",\"superportal-external\",\"superportal-ui\",\"supply-chain-orchestrator\",\"supply-gate-admin-console\",\"supply-gate-frontend\",\"supply-invoicing-service\",\"supply-journey-service\",\"supply-location-searcher\",\"supply-metadata-reader\",\"supply-profile-service\",\"supply-proxy\",\"supply-registry-service\",\"supply-util-edge\",\"supplygate-rule-engine\",\"tech-blog-web\",\"tenant-activity-publisher\",\"third-party-location\",\"thunder-camel\",\"tile-service\",\"trace-processing-service\",\"transactions-history\",\"transporter\",\"tsi-screening-service\",\"uber-chat-service\",\"underpayments-service\",\"unrated-trip-service\",\"upfront-eta-service\",\"user-indexing-adapter\",\"utility-bills\",\"utility-bills-autopay-service\",\"utility-bills-partner-adaptor-service\",\"vista-event-processor\",\"wallet-funding-service\",\"wallet-info-service\",\"wallet-statement-service\",\"webhook-service\",\"withdrawal-service\",\"workflow-management-service\",\"workshape-simulator\",\"wusool-service\") \n| filter matchesValue(event.status, \"ACTIVE\") \n| fields timestamp, k8s.namespace.name, k8s.workload.name, event.category, event.name, event.description, count=toLong(1)"
                            }
                        }
                    },
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "10": {
                "type": "markdown",
                "content": "**Logs**\n--- --- \n "
            },
            "11": {
                "title": "Logs Trend",
                "type": "data",
                "query": "fetch logs\n| filter matchesValue(k8s.cluster.name, $Cluster)\n| filter matchesValue(k8s.namespace.name, $Namespace) \n| filter k8s.namespace.name != \"null\"\n| filter dt.kubernetes.workload.name != \"null\"\n| makeTimeseries error_logs = countIf(status == \"ERROR\"), all_logs = count(), by: {k8s.namespace.name}\n\n",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "bandChartSettings": {
                            "lower": "error_logs",
                            "upper": "all_logs"
                        },
                        "xAxisScaling": "auto",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "resolved_problem_duration"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "error_logs",
                                "all_logs"
                            ]
                        },
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "content"
                                ],
                                "id": 1747799556895,
                                "value": "log-content"
                            }
                        ],
                        "columnOrder": [
                            "[\"timeframe\",\"start\"]",
                            "[\"timeframe\",\"end\"]",
                            "[\"interval\"]",
                            "[\"k8s.namespace.name\"]",
                            "[\"error_logs\"]",
                            "[\"all_logs\"]"
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "event.id"
                        },
                        "displayedFields": [
                            "event.id",
                            "event.status",
                            "event.category",
                            "dt.davis.mute.status",
                            "event.status_transition",
                            "display_id",
                            "event.name",
                            "event.kind",
                            "event.description",
                            "root_cause_entity_id",
                            "root_cause_entity_name"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "12": {
                "title": "Error Logs",
                "type": "data",
                "query": "fetch logs\n| filter matchesValue(status, \"ERROR\")\n| filter matchesValue(k8s.cluster.name, $Cluster)\n| filter matchesValue(k8s.namespace.name, $Namespace) \n| filter k8s.namespace.name != \"null\"\n| filter dt.kubernetes.workload.name != \"null\"\n| fields timestamp, dt.kubernetes.workload.name, content \n",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "auto",
                        "xAxisLabel": "timestamp",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "resolved_problem_duration"
                        ],
                        "leftYAxisSettings": {}
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {
                            "[\"dt.kubernetes.workload.name\"]": 510,
                            "[\"content\"]": 695
                        },
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "content"
                                ],
                                "id": 1747799556895,
                                "value": "log-content"
                            }
                        ],
                        "columnOrder": [
                            "[\"timestamp\"]",
                            "[\"dt.kubernetes.workload.name\"]",
                            "[\"content\"]"
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "event.id"
                        },
                        "displayedFields": [
                            "event.id",
                            "event.status",
                            "event.category",
                            "dt.davis.mute.status",
                            "event.status_transition",
                            "display_id",
                            "event.name",
                            "event.kind",
                            "event.description",
                            "root_cause_entity_id",
                            "root_cause_entity_name"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "13": {
                "type": "markdown",
                "content": "**Traces**\n--- --- \n "
            },
            "14": {
                "title": "Failure Traces",
                "type": "data",
                "query": "fetch spans, samplingRatio: 100, scanLimitGBytes: 50\n// if in \"requests\" mode\n| filter request.is_root_span == true AND isNotNull(endpoint.name) AND request.is_failed == true\n// calculate multiplicity based on sampling\n| fieldsAdd sampling.probability = (power(2, 56) - coalesce(sampling.threshold, 0)) * power(2, -56), sampling.multiplicity = 1/sampling.probability, multiplicity = coalesce(sampling.multiplicity, 1) * coalesce(aggregation.count, 1) * dt.system.sampling_ratio\n| limit 1000\n| fieldsAdd request.status_code = if(request.is_failed, \"Failure\", else: \"Success\")\n| fieldsAdd span.source = if(isNotNull(dt.agent.module.id), \"OneAgent\", else: \"OpenTelemetry\")\n| fieldsAdd http.response.status_code = coalesce(http.response.status_code, toLong(http.status_code))\n| fieldsAdd k8s.workload.name = coalesce(k8s.workload.name, dt.kubernetes.workload.name)\n| fieldsAdd cluster = entityAttr(dt.entity.kubernetes_cluster, \"entity.name\")\n| filter matchesValue(cluster, $Cluster) AND  matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.workload.name, $Deployment)\n| fields\n    start_time,\n    endpoint.name,\n    duration,\n    http.response.status_code,\n    k8s.workload.name,\n    k8s.namespace.name,\n    span.id,\n    trace.id\n\n",
                "visualization": "table",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "xAxisScaling": "auto",
                        "xAxisLabel": "start_time",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "resolved_problem_duration"
                        ],
                        "leftYAxisSettings": {},
                        "fieldMapping": {
                            "timestamp": "start_time",
                            "leftAxisValues": [
                                "duration"
                            ]
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {
                            "[\"dt.kubernetes.workload.name\"]": 510,
                            "[\"content\"]": 695
                        },
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "content"
                                ],
                                "id": 1747799556895,
                                "value": "log-content"
                            }
                        ],
                        "columnOrder": [
                            "[\"start_time\"]",
                            "[\"endpoint.name\"]",
                            "[\"duration\"]",
                            "[\"http.response.status_code\"]",
                            "[\"k8s.workload.name\"]",
                            "[\"k8s.namespace.name\"]",
                            "[\"span.id\"]",
                            "[\"trace.id\"]"
                        ],
                        "sortBy": [
                            {
                                "columnId": "[\"endpoint.name\"]",
                                "direction": "ascending"
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "event.id"
                        },
                        "displayedFields": [
                            "event.id",
                            "event.status",
                            "event.category",
                            "dt.davis.mute.status",
                            "event.status_transition",
                            "display_id",
                            "event.name",
                            "event.kind",
                            "event.description",
                            "root_cause_entity_id",
                            "root_cause_entity_name"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "15": {
                "title": "Requests",
                "type": "data",
                "query": "fetch spans,  samplingRatio: 100, scanLimitGBytes: 50\n\n// if in \"requests\" mode\n| filter request.is_root_span == true AND isNotNull(endpoint.name)\n| fieldsAdd cluster = entityAttr(dt.entity.kubernetes_cluster, \"entity.name\")\n| filter matchesValue(cluster, $Cluster) AND  matchesValue(k8s.namespace.name, $Namespace) AND matchesValue(k8s.workload.name, $Deployment)\n\n// calculate multiplicity based on sampling\n| fieldsAdd sampling.probability = (power(2, 56) - coalesce(sampling.threshold, 0)) * power(2, -56), sampling.multiplicity = 1/sampling.probability, multiplicity = coalesce(sampling.multiplicity, 1) * coalesce(aggregation.count, 1) * dt.system.sampling_ratio\n\n// construct fields\n| fieldsAdd request.status_code = if(request.is_failed, \"Failure\", else: \"Success\")\n\n// for multiplicity (due to sampling) sum() needs to be used instead of count()\n| fieldsAdd failedIf = if(request.status_code == \"Failure\", `multiplicity`, else: 0), successIf = if(request.status_code == \"Success\", `multiplicity`, else: 0)\n\n| makeTimeseries {\n    failure = sum(failedIf, default: 0),\n    success = sum(successIf, default: 0)\n    \n  }, bins: 120",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "apdex-inverted",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "bandChartSettings": {
                            "lower": "failure",
                            "upper": "success"
                        },
                        "xAxisScaling": "auto",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "resolved_problem_duration"
                        ],
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "failure",
                                "success"
                            ]
                        },
                        "leftYAxisSettings": {},
                        "seriesOverrides": [
                            {
                                "seriesId": [
                                    "failure"
                                ],
                                "override": {
                                    "color": {
                                        "Default": "var(--dt-colors-charts-categorical-themed-fireplace-color-01-default, #ae132d)"
                                    }
                                }
                            },
                            {
                                "seriesId": [
                                    "success"
                                ],
                                "override": {
                                    "color": {
                                        "Default": "var(--dt-colors-charts-apdex-good-default, #1c520a)"
                                    }
                                }
                            }
                        ]
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "content"
                                ],
                                "id": 1747799556895,
                                "value": "log-content"
                            }
                        ],
                        "columnOrder": [
                            "[\"timeframe\",\"start\"]",
                            "[\"timeframe\",\"end\"]",
                            "[\"interval\"]",
                            "[\"failure\"]",
                            "[\"success\"]"
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {
                            "value": "event.id"
                        },
                        "displayedFields": [
                            "event.id",
                            "event.status",
                            "event.category",
                            "dt.davis.mute.status",
                            "event.status_transition",
                            "display_id",
                            "event.name",
                            "event.kind",
                            "event.description",
                            "root_cause_entity_id",
                            "root_cause_entity_name"
                        ],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [
                            {
                                "valueAxis": "dt.system.sampling_ratio",
                                "rangeAxis": ""
                            },
                            {
                                "valueAxis": "interval",
                                "rangeAxis": ""
                            }
                        ],
                        "variant": "single",
                        "truncationMode": "middle",
                        "displayedFields": [
                            "interval",
                            "dt.system.sampling_ratio"
                        ]
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": false,
                    "unitsOverrides": [
                        {
                            "identifier": "failure",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747813484828
                        },
                        {
                            "identifier": "success",
                            "unitCategory": "unspecified",
                            "baseUnit": "none",
                            "displayUnit": null,
                            "decimals": 0,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747813492587
                        },
                        {
                            "identifier": "p50",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747813523465
                        },
                        {
                            "identifier": "avg",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747813551336
                        },
                        {
                            "identifier": "p90",
                            "unitCategory": "time",
                            "baseUnit": "millisecond",
                            "displayUnit": null,
                            "decimals": 2,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1747813565773
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "16": {
                "title": "Success Rate",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $Namespace) AND\n    matchesValue(destination_workload, $Deployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| join on: { timeframe, k8s.cluster.name, destination_workload_namespace }, [\n\n  // ── Successful Istio Responses ─────────────────────────────────────────\n  timeseries success = sum(istio_requests_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(response_code, \"2*\") AND\n      matchesValue(reporter, \"destination\") AND\n      matchesValue(destination_workload_namespace, $Namespace) AND\n      matchesValue(destination_workload, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, destination_workload_namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s_cluster_name\n\n], fields: { success }\n| fieldsAdd successRate = 100 * (success[] / total[])\n| fieldsAdd namespace = destination_workload_namespace\n| fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name, destination_workload_namespace\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| append [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $Namespace) AND\n      matchesValue(deployment, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n\n  | join on: { timeframe, k8s.cluster.name, namespace}, [\n\n    // ── Inbound Successful Responses ─────────────────────────────────────\n    timeseries success = sum(response_total, rollup:avg, default:0),\n      filter: {\n        matchesValue(status_code, \"2*\") AND\n        matchesValue(direction, \"inbound\") AND\n        matchesValue(namespace, $Namespace) AND\n        matchesValue(deployment, $Deployment)\n      },\n      interval: 1m,\n      by: {k8s.cluster.name, namespace}\n    | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n    | filter matchesValue(k8s_cluster_name, $Cluster)\n\n\n  ], fields: { success }\n\n  | fieldsAdd successRate = 100 * (success[] / total[])\n  | fieldsRemove total, success, k8s.cluster.name, k8s_cluster_name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "successRate"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total",
                            "min": "data-min"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": []
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true,
                    "unitsOverrides": [
                        {
                            "identifier": "successRate",
                            "unitCategory": "percentage",
                            "baseUnit": "percent",
                            "displayUnit": null,
                            "decimals": 1,
                            "suffix": "",
                            "delimiter": false,
                            "added": 1748271013194
                        }
                    ]
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "17": {
                "title": "5xx",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(response_code, \"5*\") AND\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $Namespace) AND\n    matchesValue(destination_workload, $Deployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\"), namespace = destination_workload_namespace\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name, destination_workload_namespace, k8s_cluster_name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| append [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(status_code, \"5*\") AND\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $Namespace) AND\n      matchesValue(deployment, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name, k8s_cluster_name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1748271194302
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "18": {
                "title": "4xx",
                "type": "data",
                "query": "// ── Primary Istio Metrics ────────────────────────────────────────────────\ntimeseries total = sum(istio_requests_total, rollup:avg, default:0),\n  filter: {\n    matchesValue(response_code, \"4*\") AND\n    matchesValue(reporter, \"destination\") AND\n    matchesValue(destination_workload_namespace, $Namespace) AND\n    matchesValue(destination_workload, $Deployment)\n  },\n  interval: 1m,\n  by: {k8s.cluster.name, destination_workload_namespace}\n| fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\"), namespace = destination_workload_namespace\n| filter matchesValue(k8s_cluster_name, $Cluster)\n| fieldsRemove k8s.cluster.name, destination_workload_namespace, k8s_cluster_name\n\n\n// ── Append Inbound Request Metrics ──────────────────────────────────────\n| append [\n\n  // ── Inbound Total Requests ─────────────────────────────────────────────\n  timeseries total = sum(response_total, rollup:avg, default:0),\n    filter: {\n      matchesValue(status_code, \"4*\") AND\n      matchesValue(direction, \"inbound\") AND\n      matchesValue(namespace, $Namespace) AND\n      matchesValue(deployment, $Deployment)\n    },\n    interval: 1m,\n    by: {k8s.cluster.name, namespace}\n  | fieldsAdd k8s_cluster_name = concat(k8s.cluster.name, \"-eks\")\n  | filter matchesValue(k8s_cluster_name, $Cluster)\n  | fieldsRemove k8s.cluster.name, k8s_cluster_name\n\n]",
                "visualization": "lineChart",
                "visualizationSettings": {
                    "thresholds": [],
                    "chartSettings": {
                        "gapPolicy": "gap",
                        "circleChartSettings": {
                            "groupingThresholdType": "relative",
                            "groupingThresholdValue": 0,
                            "valueType": "relative"
                        },
                        "categoryOverrides": {},
                        "curve": "linear",
                        "pointsDisplay": "auto",
                        "categoricalBarChartSettings": {
                            "layout": "horizontal",
                            "categoryAxisTickLayout": "horizontal",
                            "scale": "absolute",
                            "groupMode": "stacked",
                            "colorPaletteMode": "multi-color",
                            "valueAxisScale": "linear"
                        },
                        "colorPalette": "categorical",
                        "valueRepresentation": "absolute",
                        "truncationMode": "middle",
                        "fieldMapping": {
                            "timestamp": "timeframe",
                            "leftAxisValues": [
                                "total"
                            ]
                        },
                        "xAxisScaling": "analyzedTimeframe",
                        "xAxisLabel": "timeframe",
                        "xAxisIsLabelVisible": false,
                        "hiddenLegendFields": [
                            "interval"
                        ],
                        "leftYAxisSettings": {
                            "isLabelVisible": false,
                            "label": "istio_requests_total"
                        },
                        "legend": {
                            "position": "auto",
                            "hidden": false
                        }
                    },
                    "singleValue": {
                        "showLabel": true,
                        "label": "",
                        "prefixIcon": "AnalyticsIcon",
                        "isIconVisible": false,
                        "autoscale": true,
                        "alignment": "center",
                        "colorThresholdTarget": "value"
                    },
                    "table": {
                        "rowDensity": "condensed",
                        "enableSparklines": false,
                        "hiddenColumns": [],
                        "linewrapEnabled": false,
                        "lineWrapIds": [],
                        "monospacedFontEnabled": false,
                        "monospacedFontColumns": [],
                        "columnWidths": {},
                        "columnTypeOverrides": [
                            {
                                "fields": [
                                    "total"
                                ],
                                "value": "sparkline",
                                "id": 1748271194302
                            }
                        ]
                    },
                    "honeycomb": {
                        "shape": "hexagon",
                        "legend": {
                            "hidden": false,
                            "position": "auto",
                            "ratio": "auto"
                        },
                        "dataMappings": {},
                        "displayedFields": [],
                        "truncationMode": "middle",
                        "colorMode": "color-palette",
                        "colorPalette": "categorical"
                    },
                    "histogram": {
                        "legend": {
                            "position": "auto"
                        },
                        "yAxis": {
                            "label": "Frequency",
                            "isLabelVisible": true,
                            "scale": "linear"
                        },
                        "colorPalette": "categorical",
                        "dataMappings": [],
                        "variant": "single",
                        "truncationMode": "middle"
                    },
                    "valueBoundaries": {
                        "min": "auto",
                        "max": "auto"
                    },
                    "autoSelectVisualization": true
                },
                "querySettings": {
                    "maxResultRecords": 1000,
                    "defaultScanLimitGbytes": 500,
                    "maxResultMegaBytes": 1,
                    "defaultSamplingRatio": 10,
                    "enableSampling": false
                },
                "davis": {
                    "enabled": false,
                    "davisVisualization": {
                        "isAvailable": true
                    }
                }
            },
            "19": {
                "type": "markdown",
                "content": "**Problems**\n--- --- \n "
            }
        },
        "layouts": {
            "1": {
                "x": 0,
                "y": 5,
                "w": 9,
                "h": 4
            },
            "2": {
                "x": 9,
                "y": 1,
                "w": 8,
                "h": 4
            },
            "3": {
                "x": 17,
                "y": 1,
                "w": 7,
                "h": 4
            },
            "4": {
                "x": 0,
                "y": 0,
                "w": 24,
                "h": 1
            },
            "5": {
                "x": 0,
                "y": 9,
                "w": 12,
                "h": 1
            },
            "6": {
                "x": 0,
                "y": 10,
                "w": 6,
                "h": 5
            },
            "7": {
                "x": 6,
                "y": 10,
                "w": 6,
                "h": 5
            },
            "9": {
                "x": 12,
                "y": 10,
                "w": 12,
                "h": 5
            },
            "10": {
                "x": 0,
                "y": 27,
                "w": 24,
                "h": 1
            },
            "11": {
                "x": 0,
                "y": 28,
                "w": 24,
                "h": 6
            },
            "12": {
                "x": 0,
                "y": 34,
                "w": 24,
                "h": 6
            },
            "13": {
                "x": 0,
                "y": 15,
                "w": 24,
                "h": 1
            },
            "14": {
                "x": 0,
                "y": 21,
                "w": 24,
                "h": 6
            },
            "15": {
                "x": 0,
                "y": 16,
                "w": 24,
                "h": 5
            },
            "16": {
                "x": 0,
                "y": 1,
                "w": 9,
                "h": 4
            },
            "17": {
                "x": 9,
                "y": 5,
                "w": 8,
                "h": 4
            },
            "18": {
                "x": 17,
                "y": 5,
                "w": 7,
                "h": 4
            },
            "19": {
                "x": 12,
                "y": 9,
                "w": 12,
                "h": 1
            }
        },
        "importedWithCode": false,
        "settings": {}
    }
