---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: Dashboard
metadata:
  name: istio-migration
  labels:
    scope: global
spec:
  name: "Istio Migration"
  json: |
    {
      "version": 18,
      "variables": [
        {
          "key": "Cluster",
          "visible": true,
          "type": "query",
          "version": 1,
          "editable": true,
          "input": "fetch dt.entity.kubernetes_cluster\n| summarize clusters = collectDistinct(entity.name)\n| expand clusters\n| sort clusters",
          "multiple": true
        },
        {
          "key": "Mesh",
          "visible": true,
          "type": "csv",
          "version": 1,
          "editable": true,
          "input": "istio,linkerd",
          "multiple": true,
          "defaultValue": [
            "3420b2ac-f1cf-4b24-b62d-61ba1ba8ed05*"
          ]
        }
      ],
      "tiles": {
        "0": {
          "title": "Migration Status",
          "description": "Excluding namespaces \"kong-ingress\", \"linkerd*\" and  \"flagger-system\"",
          "type": "data",
          "query": "fetch dt.entity.container_group_instance, from: now() - 5m\n| expand containerNames\n| parse containerStatus, \"DATA 'state=' WORD:state\"\n| filter (containerNames == \"istio-proxy\" OR containerNames == \"linkerd-proxy\")  AND state == \"running\"\n| fieldsAdd cluster.id = belongs_to[dt.entity.kubernetes_cluster]\n| fieldsAdd namespace.id = belongs_to[dt.entity.cloud_application_namespace]\n| fieldsAdd Mesh = if(containerNames == \"istio-proxy\", \"istio\", else: \"linkerd\")\n| lookup [\n    fetch dt.entity.kubernetes_cluster, from: now() - 5m \n    | fields id, entity.name\n], \n    sourceField: cluster.id, \n    lookupField: id, \n    fields: { clusterName = entity.name }, \n    executionOrder:leftFirst\n| lookup [\n    fetch dt.entity.cloud_application_namespace\n    | fields id, entity.name\n], \n    sourceField: namespace.id, \n    lookupField: id, \n    fields: { namespace = entity.name }, \n    executionOrder: leftFirst\n| fields \n    Mesh,\n    cluster=clusterName,\n    namespace\n| filterOut  matchesValue(namespace, array(\"kong-ingress\", \"linkerd*\", \"flagger-system\"))\n| filter in(cluster, $Cluster) AND  in(Mesh, $Mesh) \n| summarize pods=count(), by: { Mesh, namespace, cluster }\n",
          "visualization": "table",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle"
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "",
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {
                "[\"cluster\"]": 234.5625
              },
              "columnTypeOverrides": [],
              "sortBy": {
                "columnId": "[\"Istio\"]",
                "direction": "ascending"
              }
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "1": {
          "title": "Migration Progress",
          "type": "data",
          "query": "fetch dt.entity.container_group_instance, from: now() - 5m\n| expand containerNames\n| parse containerStatus, \"DATA 'state=' WORD:state\"\n| filter (containerNames == \"istio-proxy\" OR containerNames == \"linkerd-proxy\")  AND state == \"running\"\n| fieldsAdd cluster.id = belongs_to[dt.entity.kubernetes_cluster]\n| fieldsAdd namespace.id = belongs_to[dt.entity.cloud_application_namespace]\n| fieldsAdd Mesh = if(containerNames == \"istio-proxy\", \"istio\", else: \"linkerd\")\n| lookup [\n    fetch dt.entity.kubernetes_cluster, from: now() - 5m \n    | fields id, entity.name\n], \n    sourceField: cluster.id, \n    lookupField: id, \n    fields: { clusterName = entity.name }, \n    executionOrder:leftFirst\n| lookup [\n    fetch dt.entity.cloud_application_namespace\n    | fields id, entity.name\n], \n    sourceField: namespace.id, \n    lookupField: id, \n    fields: { namespace = entity.name }, \n    executionOrder: leftFirst\n| fields \n    Mesh,\n    cluster=clusterName,\n    namespace\n| filter in(cluster, $Cluster) \n| filterOut  matchesValue(namespace, array(\"kong-ingress\", \"linkerd*\", \"flagger-system\"))\n| summarize pods=count(), by: { Mesh, namespace, cluster }\n| summarize count(),  by: { Mesh }\n",
          "visualization": "pieChart",
          "visualizationSettings": {
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "relative",
                "groupingThresholdValue": 0,
                "valueType": "relative"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxis": [
                  "Mesh"
                ],
                "valueAxis": [
                  "count()"
                ],
                "categoryAxisLabel": "Mesh",
                "valueAxisLabel": "count()",
                "tooltipVariant": "single"
              },
              "colorPalette": "categorical",
              "valueRepresentation": "absolute",
              "truncationMode": "middle"
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "",
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [],
              "variant": "single",
              "truncationMode": "middle"
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "autoSelectVisualization": false,
            "dataMapping": {
              "value": "count()"
            },
            "label": {
              "showLabel": true,
              "label": "count()"
            }
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        },
        "2": {
          "title": "Count",
          "type": "data",
          "query": "fetch dt.entity.container_group_instance, from: now() - 1h\n| expand containerNames\n| parse containerStatus, \"DATA 'state=' WORD:state\"\n| filter (containerNames == \"istio-proxy\" OR containerNames == \"linkerd-proxy\")  AND state == \"running\"\n| fieldsAdd cluster.id = belongs_to[dt.entity.kubernetes_cluster]\n| fieldsAdd namespace.id = belongs_to[dt.entity.cloud_application_namespace]\n| fieldsAdd Mesh = if(containerNames == \"istio-proxy\", \"istio\", else: \"linkerd\")\n| lookup [\n    fetch dt.entity.kubernetes_cluster, from: now() - 1h \n    | fields id, entity.name\n], \n    sourceField: cluster.id, \n    lookupField: id, \n    fields: { clusterName = entity.name }, \n    executionOrder:leftFirst\n| lookup [\n    fetch dt.entity.cloud_application_namespace\n    | fields id, entity.name\n], \n    sourceField: namespace.id, \n    lookupField: id, \n    fields: { namespace = entity.name }, \n    executionOrder: leftFirst\n| fields \n    Mesh,\n    cluster=clusterName,\n    namespace\n| filter in(cluster, $Cluster) \n| filterOut  matchesValue(namespace, array(\"kong-ingress\", \"linkerd*\", \"flagger-system\"))\n| summarize pods=count(), by: { Mesh, namespace, cluster }\n| summarize count(),  by: { Mesh }\n",
          "visualization": "categoricalBarChart",
          "visualizationSettings": {
            "autoSelectVisualization": false,
            "thresholds": [],
            "chartSettings": {
              "gapPolicy": "gap",
              "circleChartSettings": {
                "groupingThresholdType": "absolute",
                "groupingThresholdValue": 0,
                "valueType": "absolute"
              },
              "categoryOverrides": {},
              "curve": "linear",
              "pointsDisplay": "auto",
              "categoricalBarChartSettings": {
                "layout": "horizontal",
                "categoryAxisTickLayout": "horizontal",
                "scale": "absolute",
                "groupMode": "stacked",
                "colorPaletteMode": "multi-color",
                "valueAxisScale": "linear",
                "categoryAxis": "Mesh",
                "categoryAxisLabel": "Mesh",
                "valueAxis": [
                  "count()"
                ],
                "valueAxisLabel": "count()",
                "tooltipVariant": "single"
              },
              "colorPalette": "purple-rain",
              "valueRepresentation": "absolute",
              "truncationMode": "middle",
              "legend": {}
            },
            "singleValue": {
              "showLabel": true,
              "label": "",
              "prefixIcon": "",
              "autoscale": true,
              "alignment": "center",
              "colorThresholdTarget": "value"
            },
            "table": {
              "rowDensity": "condensed",
              "enableSparklines": false,
              "hiddenColumns": [],
              "linewrapEnabled": false,
              "lineWrapIds": [],
              "monospacedFontEnabled": false,
              "monospacedFontColumns": [],
              "columnWidths": {},
              "columnTypeOverrides": []
            },
            "honeycomb": {
              "shape": "hexagon",
              "legend": {
                "hidden": false,
                "position": "auto",
                "ratio": "auto"
              },
              "dataMappings": {},
              "displayedFields": [],
              "truncationMode": "middle",
              "colorMode": "color-palette",
              "colorPalette": "categorical"
            },
            "histogram": {
              "legend": {
                "position": "auto"
              },
              "yAxis": {
                "label": "Frequency",
                "isLabelVisible": true,
                "scale": "linear"
              },
              "colorPalette": "categorical",
              "dataMappings": [
                {
                  "valueAxis": "count()",
                  "rangeAxis": ""
                }
              ],
              "variant": "single",
              "truncationMode": "middle",
              "displayedFields": [
                "Mesh"
              ]
            },
            "valueBoundaries": {
              "min": "auto",
              "max": "auto"
            },
            "dataMapping": {
              "value": "count()"
            },
            "label": {
              "showLabel": true,
              "label": "count()"
            },
            "unitsOverrides": [
              {
                "identifier": "count()",
                "unitCategory": "unspecified",
                "baseUnit": "none",
                "displayUnit": null,
                "decimals": 0,
                "suffix": "",
                "delimiter": false,
                "added": 1743594439124
              }
            ]
          },
          "querySettings": {
            "maxResultRecords": 1000,
            "defaultScanLimitGbytes": 500,
            "maxResultMegaBytes": 1,
            "defaultSamplingRatio": 10,
            "enableSampling": false
          },
          "davis": {
            "enabled": false,
            "davisVisualization": {
              "isAvailable": true
            }
          }
        }
      },
      "layouts": {
        "0": {
          "x": 0,
          "y": 8,
          "w": 24,
          "h": 8
        },
        "1": {
          "x": 0,
          "y": 0,
          "w": 13,
          "h": 8
        },
        "2": {
          "x": 13,
          "y": 0,
          "w": 11,
          "h": 8
        }
      },
      "importedWithCode": false,
      "settings": {}
    }