---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: admin-order-experience-service-slo
  labels:
    service: admin-order-experience-service
    scope: global
spec:
  global:
    latency: 5000
  keyRequests:
    - name: "orders-orderid-accept"
      path: /v1/orders/<orderID>/accept
      target: 99.95
      latency: 3000
    - name: "v1-orders-minimal"
      path: /v1/orders/minimal
      target: 99.95
      latency: 3000
    - name: "orders-orderid-notes"
      path: /v1/orders/<orderID>/notes
      target: 99.95
      latency: 3000
    - name: "orders-orderid-ready"
      path: /v1/orders/<orderID>/ready
      target: 99.95
      latency: 250
    - name: "orders-orderid-ready"
      path: /v1/internal/orders/<orderID>/ready
      target: 99.95
      latency: 250
    - name: "orderid-baskets-basketid"
      path: /v1/orders/<orderID>/baskets/<basketID>
      target: 99.95
      latency: 1000
    - name: "orders-orderid-cancel"
      path: /v1/orders/<orderID>/cancel
      target: 99.95
      latency: 1800
    - name: "baskets-basketid-item-suggestions"
      path: /v1/baskets/<basketID>/item-suggestions
      target: 99.95
      latency: 1800
    - name: "v1-orders-scoped_reasons"
      path: /v1/orders/scoped_reasons
      target: 99.95
      latency: 1800
    - name: "v1-orders-reasons"
      path: /v1/orders/reasons
      target: 99.95
      latency: 2500
    - name: "v1-orders-list"
      path: /v1/orders/list
      target: 99.95
      latency: 2000
      namingRuleEnforce: true
    - name: "v1-orders-orderid"
      path: /v1/orders/<orderID>
      target: 99.95
      latency: 5000
      namingRuleEnforce: true
