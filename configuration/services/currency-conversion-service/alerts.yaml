apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: currency-conversion-service-anomaly-detection
  labels:
    scope: global
    service: currency-conversion-service
spec:
  anomalies:
    staticThreshold:
      currency-conversion-service-rates-not-updated-error:
        title: "[currency-conversion-service] currency rates not updated over 36 hours"
        description: "[currency-conversion-service] currency rates not updated over 36 hours"
        alertCondition: "ABOVE"
        threshold: 2160
        query: |
          timeseries errors = max(currency_conversion_minutes_since_last_update_total),
          by: {c_service}, interval: 1m
          | fieldsAdd entity.name = c_service
          | fieldsAdd event_name = "currency rates not updated"
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, errors, dt_service.id, dt_service.entity.name, c_service, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "ERROR_EVENT"
        eventName: " for over 36 hours"
        dimensionNameProperty: event_name
      currency-conversion-service-rates-file-fetch-n-processing-errors:
        title: "[currency-conversion-service] file fetch or processing errors > 0"
        description: "[currency-conversion-service] file fetch or processing errors > 0"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries errors = sum(currency_conversion_s3_fetcher_error_total),
          by: {c_service},
          interval: 1m
          | fieldsAdd entity.name = c_service
          | fieldsAdd event_name = "error in fetching rates from S3"
          | fieldsAdd errors_moving_sum = arrayMovingSum(errors, 10)
          | append [
            timeseries errors = sum(currency_conversion_rates_loader_file_error_total),
            by: {c_service},
            interval: 1m
            | fieldsAdd entity.name = c_service
            | fieldsAdd event_name = "error in processing rates file"
            | fieldsAdd errors_moving_sum = arrayMovingSum(errors, 10)
          ]
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, errors_moving_sum, dt_service.id, dt_service.entity.name, c_service, event_name
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "ERROR_EVENT"
        eventName: " is more than 0"
        dimensionNameProperty: event_name
