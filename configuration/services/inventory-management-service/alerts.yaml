apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: inventory-management-service-anomaly-detection
  labels:
    scope: global
    service: inventory-management-service
spec:
  anomalies:
    staticThreshold:
      quik_products_oos:
        title: "[inventory-management-service] Too many products out of stock"
        description: "Triggers when too many products are out of stock"
        alertCondition: "ABOVE"
        threshold: 30
        evaluationWindow: "60"
        query: |
          timeseries quik_products_oos = sum(inventory_management_service_event), 
            by: {store_id, c_service}, 
            filter: event_type == "quik_products_oos", 
            interval: 1m
          | fieldsAdd entity.name = c_service
          | fieldsAdd quik_products_oos_60m = arrayMovingSum(quik_products_oos, 60)
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, quik_products_oos_60m, dt_service.id, dt_service.entity.name, c_service, store_id
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "ERROR_EVENT"
        eventName: "quik products oos"
        dimensionNameProperty: store_id
      item_not_available_high:
        title: "[inventory-management-service] Too Many Missing Products in IMS"
        description: "Triggers when item_not_available events exceed 20 per hour"
        alertCondition: "ABOVE"
        threshold: 20
        evaluationWindow: "60"
        query: |
          timeseries item_not_available = sum(inventory_management_service_event), 
            by: {store_id, c_service}, 
            filter: event_type == "item_not_available" and store_id != "0", 
            interval: 1m
          | fieldsAdd entity.name = c_service
          | fieldsAdd item_not_available_60m = arrayMovingSum(item_not_available, 60)
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, item_not_available_60m, dt_service.id, dt_service.entity.name, c_service, store_id
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "ERROR_EVENT"
        eventName: "Item Not Available Spike"
        dimensionNameProperty: store_id

      quantity_below_zero:
        title: "[inventory-management-service] Quantity Below Zero Detected"
        description: "Triggers when any quantity_below_zero event occurs"
        alertCondition: "ABOVE"
        threshold: 0
        query: |
          timeseries below_zero = sum(inventory_management_service_event), 
            by: {store_id, c_service}, 
            filter: event_type == "quantity_below_zero", 
            interval: 1m
          | fieldsAdd entity.name = c_service
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, below_zero, dt_service.id, dt_service.entity.name, c_service, store_id
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "ERROR_EVENT"
        eventName: "Quantity Below Zero"
        dimensionNameProperty: store_id

      quik_order_low:
        title: "[inventory-management-service] Low Quik Order Placed Throughput"
        description: "Triggers when fewer than 10 quik_order events are seen in 30 minutes"
        alertCondition: "BELOW"
        threshold: 10
        evaluationWindow: "60"
        query: |
          timeseries quik_orders = sum(inventory_management_service_event), 
            by: {store_id, c_service}, 
            filter: event_type == "quik_order", 
            interval: 1m
          | fieldsAdd entity.name = c_service
          | fieldsAdd quik_orders_30m = arrayMovingSum(quik_orders, 30)
          | join [fetch dt.entity.service], kind:leftOuter, prefix:"dt_service.", on:{entity.name}
          | fields interval, timeframe, quik_orders_30m, dt_service.id, dt_service.entity.name, c_service, store_id
        entityIdProperty: "dt_service.id"
        entityNameProperty: "c_service"
        eventType: "ERROR_EVENT"
        eventName: "Low Quik Order Throughput"
        dimensionNameProperty: store_id
