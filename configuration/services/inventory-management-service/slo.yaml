---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: inventory-management-service-slo
  labels:
    service: inventory-management-service
    scope: global
spec:
  global:
    latency: 1000
  failureDetection:
    httpErrorCodes: 400-403,405-599
  keyRequests:
    - name: "api-item-quantities"
      path: /v1/inventory/api/item/quantities
      target: 99.95
      latency: 1000
    - name: "api-item-fetch-quantities"
      path: /v2/inventory/api/item/fetch-quantities
      target: 99.95
      latency: 110
    - name: "v1-webhook-order-event"
      path: /v1/webhook/order-event
      target: 99.95
      latency: 540
    - name: "api-item-fetch-quantities"
      path: /v1/inventory/api/item/fetch-quantities
      target: 99.95
      latency: 110
  kafka:
    target: 99.95
    groups:
      - name: kafka-consumer-master-product
        topics:
          - kafka-consumer.master-product
        latency: 0.8
        max_age: 120.0
      - name: kafka-consumer-receiving
        topics:
          - kafka-consumer.receiving
        latency: 6.2
        max_age: 0.2
      - name: kafka-consumer-catalog
        topics:
          - kafka-consumer.catalog
        latency: .8
        max_age: 250.0
      - name: kafka-consumer-order-domain
        topics:
          - kafka-consumer.order-domain
        latency: 0.3
        max_age: 1.0
      - name: kafka-consumer-stock-update
        topics:
          - kafka-consumer.stock-update
        latency: 0.5
        max_age: 2.0
