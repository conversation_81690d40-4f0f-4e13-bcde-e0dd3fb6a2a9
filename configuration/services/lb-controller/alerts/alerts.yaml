---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: lb-controller-alerts
  labels:
    service: lb-controller
    scope: global
spec:
  anomalies:
    staticThreshold:
      webhook_failures:
        title: "[lb-controller] webhook failures"
        description: "Webhook failures detected in lb-controller"
        alertCondition: "ABOVE"
        threshold: 0
        evaluationWindow: "5"
        violatingSamples: "1"
        query: |
          timeseries sum(apiserver_admission_webhook_request_total, default:0), interval: 1m, nonempty: true, filter:{ toLong(code) >= 400 AND matchesValue(name, "*elbv2*")}, by: { name, k8s.cluster.name }
            | fieldsAdd k8s.cluster.name = concat(k8s.cluster.name, "-eks")
            | join [
                fetch dt.entity.cloud_application
                | filter matchesValue(entity.name, "*lb-controller*")
                | fieldsAdd clusterId = clustered_by[dt.entity.kubernetes_cluster]
                | join [
                    fetch dt.entity.kubernetes_cluster
                ],
                on: { left[clusterId] == right[id] },
                fields: { clusterName = entity.name }
            ],
            on: { left[k8s.cluster.name] == right[clusterName] }
            | fieldsAdd entityId = right.id, entityName = right.entity.name
            | fieldsRemove right.id, right.entity.name, k8s.cluster.name, right.clusterName, right.clusterId
        entityIdProperty: "entityId"
        entityNameProperty: "entityName"
        eventType: "ERROR_EVENT"
        eventName: "[lb-controller] webhook failures"
        dimensionNameProperty: name
      reconcile_errors:
        title: "[lb-controller] reconcile errors"
        description: "Reconcile errors detected in lb-controller"
        alertCondition: "ABOVE"
        threshold: 0
        evaluationWindow: "5"
        violatingSamples: "1"
        query: |
            timeseries errors = sum(controller_runtime_reconcile_errors_total,  default: 0),
                by: { controller, k8s.cluster.name, k8s.namespace.name },
                filter: { matchesValue(kubernetes_namespace, "lb-controller") },
                interval: 1m

            | fieldsAdd k8s.cluster.name = concat(k8s.cluster.name, "-eks")

            | join [
                fetch dt.entity.cloud_application
                | filter matchesValue(entity.name, "*lb-controller*")
                | fieldsAdd namespaceId = belongs_to[dt.entity.cloud_application_namespace]
                
                | join [
                    fetch dt.entity.cloud_application_namespace
                    | filter matchesValue(entity.name, "*lb-controller*")
                ],
                on: { left[namespaceId] == right[id] },
                fields: { namespace = entity.name }
            ],

            on: { left[k8s.namespace.name] == right[namespace] }

            | fieldsAdd applicationId = right.id
            | fieldsRemove right.id

            | fieldsAdd applicationName = right.entity.name
            | fieldsRemove right.entity.name

            | fieldsRemove k8s.namespace.name, right.namespace, right.namespaceId
        entityIdProperty: "applicationId"
        entityNameProperty: "applicationName"
        eventType: "ERROR_EVENT"
        eventName: "[lb-controller] reconcile errors"
        dimensionNameProperty: controller
