---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: DynatraceConfig
metadata:
  name: supply-registry-service-dynatrace
  labels:
    service: supply-registry-service
    scope: global
spec:
  oneAgent:
    enabled: true
    platform: "beanstalk"
---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: DynatraceConfig
metadata:
  name: supply-registry-service-k8s-dynatrace
  labels:
    service: supply-registry-service
    scope: global
spec:
  oneAgent:
    enabled: true
    platform: "kubernetes"
