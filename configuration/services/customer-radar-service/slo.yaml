---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: SLO
metadata:
  name: customer-radar-service-slo
  labels:
    service: customer-radar-service
    scope: global
spec:
  global:
    latency: 2500
  keyRequests:
    - name: "booking-bookinguuid-generatetrackingurl"
      path: /v<version>/oa/booking/<bookingUUID>/generateTrackingUrl
      target: 99.95
      latency: 68
    - name: "vversion-rides-tracker"
      path: /v<version>/rides/tracker
      target: 99.95
      latency: 2500